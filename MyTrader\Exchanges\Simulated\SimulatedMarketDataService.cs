using System;
using System.Collections.Generic;
using System.IO;
using System.Reactive.Linq;
using System.Reactive.Subjects;
using System.Reactive.Threading.Tasks;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Newtonsoft.Json;
using MyTraderSpace.Models;
using MyTraderSpace.Logging;

namespace MyTraderSpace.Exchanges.Simulated
{
    public class SimulatedMarketDataService : IMarketDataService
    {
        //public Type ExchangeType => typeof(Models.Simulated);
        public ExchangeType ExchangeType => ExchangeType.Simulated;

        private readonly ReplaySubject<SpotMarketData> _spotMarketSubject = new(1); // Buffer size of 1 to replay last value
        private readonly ReplaySubject<FuturesMarketData> _futuresMarketSubject = new(1);
        private readonly CancellationTokenSource _cts = new();
        private readonly object _syncLock = new();
        private bool _isInitialized;
        public bool IsInitialized => _isInitialized;
        private Task? _simulationTask;

        private readonly SimulatedMarketDataConfig _config;
        private readonly LogManager _log;
        private MarketDataFileManager? _fileManager;

        private SpotMarketData? _lastSpotMarketData;
        private FuturesMarketData? _lastFuturesMarketData;
        private readonly CurrencyPair _tradingPair;
        private readonly decimal _initialPrice;
        private readonly TimeSpan _updateInterval;
        private IEnumerable<MarketDataRecord>? _loadedRecords = null;

        private volatile bool _isPaused;
        private readonly ManualResetEventSlim _pauseEvent = new(true); // Initially not paused

        public IObservable<SpotMarketData> SpotMarketData => _spotMarketSubject.AsObservable();
        public IObservable<FuturesMarketData> FuturesMarketData => _futuresMarketSubject.AsObservable();
        
        // Implement the new interface properties
        public bool SupportsSpot => true;
        public bool SupportsFutures => _config.SimulateFutures;

        // Traditional events
        public event Action<SpotMarketData>? OnSpotMarketUpdate;
        public event Action<FuturesMarketData>? OnFuturesMarketUpdate;
        
        // Implement the new interface methods
        public IDisposable SubscribeToSpotMarketData(CurrencyPair pair, Action<SpotMarketData> callback)
        {
            return SpotMarketData.Subscribe(callback);
        }
        
        public IDisposable SubscribeToFuturesMarketData(CurrencyPair pair, Action<FuturesMarketData> callback)
        {
            return FuturesMarketData.Subscribe(callback);
        }

        public SimulatedMarketDataService(
            CurrencyPair tradingPair,
            SimulatedMarketDataConfig? config = null,
            LogManager? logManager = null)
        {
            _config = config ?? new SimulatedMarketDataConfig();
            _log = logManager ?? new LogManager(nameof(SimulatedMarketDataService));
            _tradingPair = tradingPair;
            _initialPrice = _config.InitialPrice;
            _updateInterval = _config.UpdateInterval;

            _log.Debug("SimulatedMarketDataService constructed for {Symbol}.", _tradingPair.Symbol);
        }

        private void HandleSpotTickerUpdateInternal(SpotMarketData data)
        {
            lock (_syncLock)
            {
                _lastSpotMarketData = data;
                //Log.Debug("About to call OnNext for spot data");
                _spotMarketSubject.OnNext(data);
                //Log.Debug("OnNext called for spot data");
            }
        }

        private void HandleFuturesTickerUpdateInternal(FuturesMarketData data)
        {
            lock (_syncLock)
            {
                _lastFuturesMarketData = data;
                _futuresMarketSubject.OnNext(data);
            }
        }

        public void SetPause(bool pause)
        {
            if (pause != _isPaused)  // Only act if state is changing
            {
                if (pause)
                    Pause();
                else
                    Resume();
            }
        }

        private void Pause()
        {
            _isPaused = true;
            _pauseEvent.Reset();
            _log.Information("Market data simulation paused");
        }

        private void Resume()
        {
            _isPaused = false;
            _pauseEvent.Set();
            _log.Information("Market data simulation resumed");
        }

        public bool IsPaused => _isPaused;

        private async Task SimulateMarketDataAsync()
        {
            if (_loadedRecords != null && _loadedRecords.Any())
            {
                _log.Information($"Starting market data replay from file ({_loadedRecords.Count()} records) ...");
                foreach (var record in _loadedRecords)
                {
                    if (_cts.Token.IsCancellationRequested) break;
                    
                    _pauseEvent.Wait(_cts.Token); // Wait if paused

                    if (record.SpotData != null)
                    {
                        HandleSpotTickerUpdateInternal(record.SpotData);
                    }
                    if (record.FuturesData != null)
                    {
                        HandleFuturesTickerUpdateInternal(record.FuturesData);
                    }

                    // Wait for the specified interval
                    try
                    {
                        await Task.Delay(_updateInterval, _cts.Token);
                    }
                    catch (OperationCanceledException)
                    {
                        _log.Information("Market data replay task cancelled during delay.");
                        break;
                    }
                }
                _log.Information("Market data replay from file finished or stopped.");
                return; // Exit if loop finished or was cancelled
            }

            // --- Live Simulation Logic (if no file loaded or loading failed) ---
            _log.Information("Starting live market data simulation (no historical data loaded or specified)...");
            var random = new Random();
            var currentPrice = _initialPrice;

            while (!_cts.Token.IsCancellationRequested)
            {
                try
                {
                    _pauseEvent.Wait(_cts.Token); // Wait if paused

                    // Simulate price movement using configured volatility
                    var maxPriceMove = currentPrice * _config.PriceVolatility;
                    var priceChange = (decimal)(random.NextDouble() - 0.5) * maxPriceMove * 2;
                    currentPrice = Math.Max(1, currentPrice + priceChange); // Ensure price doesn't go below 1

                    // Calculate spread based on configuration
                    var spreadAmount = currentPrice * _config.SpreadPercent;
                    var bidPrice = currentPrice - (spreadAmount / 2);
                    var askPrice = currentPrice + (spreadAmount / 2);

                    var spotData = new SpotMarketData
                    {
                        Symbol = _tradingPair.Symbol,
                        LastPrice = currentPrice,
                        HighestBid = bidPrice,
                        LowestAsk = askPrice,
                        Volume24h = 1000m, // Example volume
                        Timestamp = DateTime.UtcNow // Update timestamp
                    };
                    HandleSpotTickerUpdateInternal(spotData);

                    if (_config.SimulateFutures) // Check if futures simulation is enabled
                    {
                        var futuresData = new FuturesMarketData
                        {
                            Symbol = _tradingPair.Symbol,
                            LastPrice = currentPrice,
                            MarkPrice = currentPrice, // * (1 + (decimal)(random.NextDouble() - 0.5) * 0.0001m), // Slight diff for mark
                            IndexPrice = currentPrice, // * (1 + (decimal)(random.NextDouble() - 0.5) * 0.0001m), // Slight diff for index
                            HighestBid = bidPrice,
                            LowestAsk = askPrice,
                            Volume24h = 1000m, // Example volume
                            OpenInterest = 500m, // + (decimal)(random.NextDouble() - 0.5) * 10, // Simulate OI changes
                            FundingRate = 0.0001m, // + (decimal)(random.NextDouble() - 0.5) * 0.00002m, // Simulate funding changes
                            NextFundingTime = DateTime.UtcNow.AddHours(8), //DateTime.UtcNow.Date.AddHours( (DateTime.UtcNow.Hour / 8 + 1) * 8 ), // Next 8h mark
                            Turnover24h = currentPrice * 1000m, // Example turnover
                            Timestamp = DateTime.UtcNow // Update timestamp
                        };
                        HandleFuturesTickerUpdateInternal(futuresData);
                    }

                    await Task.Delay(_updateInterval, _cts.Token);
                }
                catch (OperationCanceledException) when (_cts.Token.IsCancellationRequested)
                {
                    _log.Information("Live simulation task cancelled.");
                    break; // Exit loop cleanly on cancellation
                }
                catch (Exception ex)
                {
                    _log.Error(ex, "Error during live simulation loop.");
                    await Task.Delay(1000); // Delay before retrying after error
                }
            }
            _log.Information("Live market data simulation stopped.");
        }

        public async Task InitializeAsync()
        {
            // Prevent double initialization
            if (_isInitialized)
            {
                _log.Debug("InitializeAsync called but already initialized.");
                return;
            }

            _log.Information("Initializing SimulatedMarketDataService for {Symbol} ...", _tradingPair.Symbol);

            // --- Load Historical Data (if configured) ---
            if (!string.IsNullOrEmpty(_config.LoadDirectory))
            {
                _log.Information($"LoadDirectory specified: '{_config.LoadDirectory}'. Attempting to load historical data.");
                // Initialize file manager only if loading
                _fileManager = new MarketDataFileManager(_config.LoadDirectory); // Use the specified directory

                try
                {
                    _log.Information($"Attempting to load market data for symbol '{_tradingPair.Symbol}' from FileManager targeting directory '{_config.LoadDirectory}'.");
                    // Load data asynchronously
                    _loadedRecords = await _fileManager.LoadMarketDataAsync<MarketDataRecord>(_tradingPair.Symbol);

                    if (_loadedRecords != null && _loadedRecords.Any())
                    {
                        _log.Information($"Successfully loaded {_loadedRecords.Count()} records for symbol '{_tradingPair.Symbol}'.");
                        // Optional: Order records
                        // _loadedRecords = _loadedRecords.OrderBy(r => r.Timestamp).ToList();
                    }
                    else
                    {
                        _log.Warning($"LoadDirectory specified, but no historical data found for symbol '{_tradingPair.Symbol}' in '{_config.LoadDirectory}'. Live simulation will be used.");
                        _loadedRecords = null;
                    }
                }
                catch (Exception ex)
                {
                    _log.Error(ex, $"Error loading market data for symbol '{_tradingPair.Symbol}' using MarketDataFileManager from '{_config.LoadDirectory}'. Live simulation will be used.");
                    _loadedRecords = null;
                    // Optional: Decide if loading failure should prevent initialization
                    // throw; // Re-throw if loading failure is critical
                }
            }
            else
            {
                _log.Information("LoadDirectory not specified. Live simulation will be used.");
                _fileManager = null; // Ensure fileManager is null
                _loadedRecords = null;
            }

            // --- Start Simulation Task ---
            // Ensure simulation task is started only once and after loading attempt
            lock (_syncLock) // Protect simulation task start
            {
                if (_simulationTask == null) // Check if already started (e.g., concurrent calls to InitializeAsync - although ideally prevented by _isInitialized check)
                {
                    _log.Debug("Starting simulation task...");
                    _simulationTask = Task.Run(SimulateMarketDataAsync, _cts.Token); // Pass CancellationToken
                }
            }

            // --- Wait for First Data ---
            try
            {
                _log.Debug("Waiting for first market data update...");
                using var timeoutCts = new CancellationTokenSource(TimeSpan.FromSeconds(10));
                // Link with the main CTS to cancel waiting if StopAsync is called during initialization
                using var linkedCts = CancellationTokenSource.CreateLinkedTokenSource(timeoutCts.Token, _cts.Token);

                var tasksToWait = new List<Task>();
                // Only wait for data if simulation is actually expected to produce it (live or replay)
                if (SupportsSpot)
                {
                    tasksToWait.Add(_spotMarketSubject.FirstAsync().ToTask(linkedCts.Token));
                }
                if (SupportsFutures)
                {
                     // Only wait for futures if simulating futures OR if loaded records contain futures
                    bool expectFutures = _config.SimulateFutures || (_loadedRecords?.Any(r => r.FuturesData != null) ?? false);
                    if (expectFutures)
                    {
                        tasksToWait.Add(_futuresMarketSubject.FirstAsync().ToTask(linkedCts.Token));
                    }
                    else
                    {
                        _log.Debug("Not waiting for Futures data: Simulation disabled and no loaded futures data found.");
                    }
                }

                if (!tasksToWait.Any())
                {
                    // This case might happen if SupportsSpot is false and futures are disabled/not loaded
                    _log.Warning("Initialization complete, but no market data (Spot/Futures) is expected based on configuration and loaded data.");
                    _isInitialized = true; // Mark as initialized even if no data expected
                    return;
                }

                var firstDataTask = Task.WhenAny(tasksToWait);
                await firstDataTask; // Wait for the first piece of data (spot or futures)

                // Check outcome
                if (firstDataTask.IsCompletedSuccessfully)
                {
                    _log.Information("First market data received successfully.");
                }
                else if (firstDataTask.IsFaulted)
                {
                    // Log the specific exception from the faulted task if available
                    var faultedTask = tasksToWait.FirstOrDefault(t => t.IsFaulted);
                    var innerEx = faultedTask?.Exception?.InnerException ?? firstDataTask.Exception?.InnerException ?? firstDataTask.Exception;
                    _log.Error(innerEx!, "Error waiting for first market data.");
                    // Do not set _isInitialized = true;
                    throw new InvalidOperationException("Failed to receive initial market data.", innerEx);
                }
                else if (firstDataTask.IsCanceled || timeoutCts.IsCancellationRequested)
                {
                    _log.Warning("Timed out or cancelled waiting for first market data update.");
                    // Do not set _isInitialized = true;
                    throw new TimeoutException("Timed out waiting for the first market data update from the simulation.");
                }
                _isInitialized = true; // Mark as initialized *after* successful first data wait
                _log.Information("SimulatedMarketDataService initialized successfully for {Symbol}.", _tradingPair.Symbol);

            }
            catch (OperationCanceledException) when (_cts.IsCancellationRequested) // Catch cancellation from StopAsync specifically
            {
                _log.Warning("Initialization cancelled by StopAsync while waiting for first market data.");
                // Do not set _isInitialized = true;
                throw; // Re-throw cancellation
            }
            catch (Exception ex) // Catch other exceptions during the wait or from the tasks
            {
                _log.Error(ex, "Failed during InitializeAsync while waiting for first data.");
                // Ensure cleanup if initialization fails mid-way
                await StopSimulationTaskAsync(); // Attempt to stop the potentially running task
                _isInitialized = false; // Ensure it's marked as not initialized
                throw; // Re-throw the exception
            }
        }

        // Helper to safely stop the simulation task, used in error handling during init
        private async Task StopSimulationTaskAsync()
        {
            if (_simulationTask != null && !_cts.IsCancellationRequested)
            {
                _log.Debug("Stopping simulation task due to initialization error...");
                _cts.Cancel();
                try
                {
                    await _simulationTask;
                    _log.Debug("Simulation task stopped.");
                }
                catch (Exception stopEx)
                {
                    _log.Warning(stopEx, "Exception during simulation task cleanup after initialization error.");
                }
            }
        }

        public async Task StopAsync()
        {
            _log.Information("Stopping SimulatedMarketDataService...");
            if (!_cts.IsCancellationRequested)
            {
                _cts.Cancel(); // Signal cancellation
            }

            // Wait for the simulation task to complete
            await StopSimulationTaskAsync(); // Use helper here too

            _isInitialized = false; // Mark as not initialized when stopped
            _log.Information("SimulatedMarketDataService stopped.");
        }

        public void Dispose()
        {
            _log.Debug("Disposing SimulatedMarketDataService...");
            // Ensure StopAsync runs first, but handle potential exceptions during dispose
            try
            {
                StopAsync().Wait(TimeSpan.FromSeconds(5));
            }
            catch(Exception ex)
            {
                _log.Warning(ex, "Exception during StopAsync within Dispose.");
            }

            // Dispose managed resources
            _spotMarketSubject?.Dispose();
            _futuresMarketSubject?.Dispose();
            _cts?.Dispose();
            _pauseEvent?.Dispose();
            _fileManager?.Dispose(); // Safely dispose fileManager if it was initialized
            _log?.Dispose(); // Dispose logger instance

            // Suppress finalization
            GC.SuppressFinalize(this);
            // Cannot log after logger disposal
            // Console.WriteLine("SimulatedMarketDataService disposed."); // Use Console if needed
        }

        public (SpotMarketData? Spot, FuturesMarketData? Futures) GetLatestData()
        {
            lock (_syncLock)
            {
                SpotMarketData? spot = null;
                FuturesMarketData? futures = null;

                if (_lastSpotMarketData != null)
                {
                    spot = new SpotMarketData
                    {
                        Symbol = _lastSpotMarketData.Symbol,
                        LastPrice = _lastSpotMarketData.LastPrice,
                        HighestBid = _lastSpotMarketData.HighestBid,
                        LowestAsk = _lastSpotMarketData.LowestAsk,
                        Volume24h = _lastSpotMarketData.Volume24h
                    };
                }

                if (_lastFuturesMarketData != null)
                {
                    futures = new FuturesMarketData();
                    futures.Update(_lastFuturesMarketData);
                }

                return (spot, futures);
            }
        }

        //// What is this? Where is it used??
        //private async Task ReplayDataFromFileAsync()
        //{
        //    if (string.IsNullOrEmpty(_config.LoadDirectory)) return;

        //    var filePath = Path.Combine(_config.LoadDirectory, $"market_data_{_tradingPair.Symbol}.json");
        //    if (!File.Exists(filePath))
        //    {
        //        _log.Warning($"Market data file not found at {filePath}");
        //        return;
        //    }

        //    _log.Information($"Starting market data replay from file: {filePath}");

        //    try
        //    {
        //        using var reader = new StreamReader(filePath);
        //        string? line;
        //        while ((line = await reader.ReadLineAsync()) != null && !_cts.IsCancellationRequested)
        //        {
        //            if (_isPaused)
        //            {
        //                await Task.Delay(100, _cts.Token); // Wait while paused
        //                continue;
        //            }

        //            var record = JsonConvert.DeserializeObject<MarketDataRecord>(line);
        //            if (record != null)
        //            {
        //                if (record.SpotData != null)
        //                {
        //                    HandleSpotTickerUpdateInternal(record.SpotData);
        //                }
        //                if (record.FuturesData != null)
        //                {
        //                    HandleFuturesTickerUpdateInternal(record.FuturesData);
        //                }
        //            }

        //            await Task.Delay(_updateInterval, _cts.Token);
        //        }
        //        _log.Information("Market data replay from file finished or stopped.");
        //    }
        //    catch (OperationCanceledException) when (_cts.IsCancellationRequested)
        //    {
        //        _log.Information("Market data replay task cancelled.");
        //    }
        //    catch (Exception ex)
        //    {
        //        _log.Error(ex, "Error during market data replay from file.");
        //    }
        //}
    }
}