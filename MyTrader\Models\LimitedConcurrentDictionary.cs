using System;
using System.Collections;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Linq;

namespace MyTraderSpace.Models
{
    /// <summary>
    /// A thread-safe dictionary that maintains a maximum size by removing the oldest entries when new ones are added.
    /// </summary>
    public class LimitedConcurrentDictionary<TKey, TValue> : IDictionary<TKey, TValue> where TKey : notnull
    {
        private readonly ConcurrentDictionary<TKey, TValue> _dictionary;
        private readonly ConcurrentQueue<TKey> _keyOrder;
        private readonly int _maxSize;
        private readonly object _trimLock = new object();

        /// <summary>
        /// Creates a new LimitedConcurrentDictionary with the specified maximum size.
        /// </summary>
        /// <param name="maxSize">The maximum number of items to keep in the dictionary.</param>
        public LimitedConcurrentDictionary(int maxSize)
        {
            if (maxSize <= 0)
                throw new ArgumentOutOfRangeException(nameof(maxSize), "Maximum size must be greater than zero.");

            _maxSize = maxSize;
            _dictionary = new ConcurrentDictionary<TKey, TValue>();
            _keyOrder = new ConcurrentQueue<TKey>();
        }

        /// <summary>
        /// Adds a key/value pair to the dictionary. If the dictionary exceeds the maximum size,
        /// the oldest entry will be removed.
        /// </summary>
        public void Add(TKey key, TValue value)
        {
            if (_dictionary.TryAdd(key, value))
            {
                _keyOrder.Enqueue(key);
                TrimExcess();
            }
            else
            {
                throw new ArgumentException("An item with the same key has already been added.");
            }
        }

        /// <summary>
        /// Adds a key/value pair to the dictionary. If the dictionary exceeds the maximum size,
        /// the oldest entry will be removed.
        /// </summary>
        public void Add(KeyValuePair<TKey, TValue> item)
        {
            Add(item.Key, item.Value);
        }

        /// <summary>
        /// Gets or sets the value associated with the specified key.
        /// </summary>
        public TValue this[TKey key]
        {
            get => _dictionary[key];
            set
            {
                if (!_dictionary.ContainsKey(key))
                {
                    _keyOrder.Enqueue(key);
                    TrimExcess();
                }
                _dictionary[key] = value;
            }
        }

        /// <summary>
        /// Removes excess items from the dictionary to maintain the maximum size.
        /// </summary>
        private void TrimExcess()
        {
            // Use a lock to ensure we don't have race conditions when trimming
            lock (_trimLock)
            {
                while (_keyOrder.Count > _maxSize)
                {
                    if (_keyOrder.TryDequeue(out var oldestKey))
                    {
                        _dictionary.TryRemove(oldestKey, out _);
                    }
                }
            }
        }

        // Implement the rest of the IDictionary<TKey, TValue> interface

        public ICollection<TKey> Keys => _dictionary.Keys;
        public ICollection<TValue> Values => _dictionary.Values;
        public int Count => _dictionary.Count;
        public bool IsReadOnly => false;

        public void Clear()
        {
            _dictionary.Clear();
            while (_keyOrder.TryDequeue(out _)) { }
        }

        public bool ContainsKey(TKey key) => _dictionary.ContainsKey(key);

        public bool Contains(KeyValuePair<TKey, TValue> item) =>
            _dictionary.TryGetValue(item.Key, out var value) &&
            EqualityComparer<TValue>.Default.Equals(value, item.Value);

        public bool Remove(TKey key) => _dictionary.TryRemove(key, out _);

        public bool Remove(KeyValuePair<TKey, TValue> item)
        {
            if (!Contains(item)) return false;
            return Remove(item.Key);
        }

        public bool TryGetValue(TKey key, out TValue value) => _dictionary.TryGetValue(key, out value);

        public void CopyTo(KeyValuePair<TKey, TValue>[] array, int arrayIndex)
        {
            if (array == null)
                throw new ArgumentNullException(nameof(array));
            if (arrayIndex < 0)
                throw new ArgumentOutOfRangeException(nameof(arrayIndex));
            if (array.Length - arrayIndex < Count)
                throw new ArgumentException("The number of elements in the source dictionary is greater than the available space from arrayIndex to the end of the destination array.");

            foreach (var kvp in _dictionary)
            {
                array[arrayIndex++] = kvp;
            }
        }

        public IEnumerator<KeyValuePair<TKey, TValue>> GetEnumerator() => _dictionary.GetEnumerator();
        IEnumerator IEnumerable.GetEnumerator() => GetEnumerator();

        /// <summary>
        /// Tries to add a key/value pair to the dictionary. If the dictionary exceeds the maximum size,
        /// the oldest entry will be removed.
        /// </summary>
        public bool TryAdd(TKey key, TValue value)
        {
            if (_dictionary.TryAdd(key, value))
            {
                _keyOrder.Enqueue(key);
                TrimExcess();
                return true;
            }
            return false;
        }

        /// <summary>
        /// Tries to remove a key/value pair from the dictionary.
        /// </summary>
        public bool TryRemove(TKey key, out TValue value) => _dictionary.TryRemove(key, out value);

        /// <summary>
        /// Gets the values from the dictionary as a list.
        /// </summary>
        public List<TValue> ToValuesList() => _dictionary.Values.ToList();
    }
} 