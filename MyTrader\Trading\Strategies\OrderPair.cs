﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using MyTraderSpace.Logging;
using MyTraderSpace.Models;
using MyTraderSpace.Utils;
using MyTraderSpace.Exchanges;

namespace MyTraderSpace.Trading.Strategies
{
    public class OrderPair
    {
        private readonly LogManager _log;
        private readonly HedgeGridStrategyConfig _strategyConfig;
        private readonly CurrencyPair _tradingPair;
        private readonly FeeConfig _feeRate;

        public bool IsLong { get; }

        private string _name = string.Empty;
        public string Name => _name + (IsLong ? "_LongSide" : "_ShortSide");

        private readonly bool _isInitialPair = false;
        public bool IsCurrentlyPlacingOrder => _placingOrderType != PlacingOrderType.None;

        // Order State - store the most recent update for each
        public OrderModelUpdate? BaseOrder { get; set; }
        public OrderModelUpdate? TakeProfitOrder { get; set; }

        public decimal IntendedPrice { get; set; } = 0;

        // Forwarded events to (main)HedgeGridStrategy
        public event Action<OrderPair, OrderModelUpdate>? OnOrderUpdated; // Forward the received OrderUpdate to parent HedgeGridStrategy
        public event Action<OrderPair, FuturesOrderRequest>? OnOrderPlacementRequest; // Send out the order request to parent HedgeGridStrategy
        public event Action<OrderPair, Fee, Fee>? OnFeeUpdated; // (Calc, Reported) // In order for parent HGS to properly summarize Fees, it has to receive one by one when it is realized a Fee
        public event Action<OrderPair, decimal, decimal>? OnPnLUpdated; // (Calc, Reported) // In order for parent HGS to properly summarize PnLs, it has to receive one by one when it is realized a PnL
        public event Action<OrderPair, OrderResult?>? OnError; // Every last received Cancelled or Rejected result is set here and forwarded to parent HedgeGridStrategy

        private OrderResult? _lastError;
        public OrderResult? LastError // Carreful with this, this forwards the error to HGS, so when this is called, it has to be a 'final' error (meaning no intermediary rejection between placement attempts)
        {
            get => _lastError;
            private set
            {
                if (_lastError != value)
                {
                    _lastError = value;
                    if (_lastError != null)
                        _log.Warning(_lastError.Message); // if _lastError is null should we 'clear' the last error?!
                    OnError?.Invoke(this, _lastError); // Important Note: this will place the whole parent HGS in Error state and Stops it!
                }
            }
        }

        private enum PlacingOrderType { None, Base, TakeProfit }
        private PlacingOrderType _placingOrderType = PlacingOrderType.None;
        private TaskCompletionSource<bool>? _activePlacementAttemptTcs;

        public bool IsBaseOrderActive => BaseOrder != null && BaseExchangeAPI.IsOrderActive(BaseOrder.Status);
        public bool IsBaseOrderFilled => BaseOrder?.Status == OrderStatus.Filled;

        public bool IsBaseOrderActiveOrFilled() => IsBaseOrderActive || IsBaseOrderFilled;

        public bool IsTakeProfitOrderActive => TakeProfitOrder != null && BaseExchangeAPI.IsOrderActive(TakeProfitOrder.Status);
        public bool IsTakeProfitOrderFilled => TakeProfitOrder?.Status == OrderStatus.Filled;
        public bool IsTakeProfitOrderActiveOrFilled() => IsTakeProfitOrderActive || IsTakeProfitOrderFilled;


        public OrderPair(string name, bool isLong, CurrencyPair tradingPair, FeeConfig feeRate, HedgeGridStrategyConfig strategyConfig, LogManager log, bool IsInitialPair = false)
        {
            _name = name;
            IsLong = isLong;
            _isInitialPair = IsInitialPair;
            _tradingPair = tradingPair;
            _feeRate = feeRate ?? throw new ArgumentNullException(nameof(feeRate));
            _log = log ?? throw new ArgumentNullException(nameof(log));
            _strategyConfig = strategyConfig ?? throw new ArgumentNullException(nameof(strategyConfig));
            _log.Information($"OrderPair '{Name}' (IsLong: {IsLong}) created for {_tradingPair.Symbol}.");
        }

        public bool IsThisOrder(string? clientId = null, string? Id = null)
        {
            // Prioritize ClientID if available and matches
            if (!string.IsNullOrEmpty(clientId) && ((BaseOrder?.ClientOrderId == clientId) || (TakeProfitOrder?.ClientOrderId == clientId)))
            {
                return true;
            }
            // Fallback to OrderID if ClientID didn't match or wasn't provided
            if (!string.IsNullOrEmpty(Id) && ((BaseOrder?.OrderId == Id) || (TakeProfitOrder?.OrderId == Id)))
            {
                return true;
            }
            return false;
        }

        public bool IsManagingOrder(string? clientOrderId, string? orderId)
        {
            if (!string.IsNullOrEmpty(clientOrderId))
            {
                if (BaseOrder?.ClientOrderId == clientOrderId && !string.IsNullOrEmpty(BaseOrder.ClientOrderId)) return true;
                if (TakeProfitOrder?.ClientOrderId == clientOrderId && !string.IsNullOrEmpty(TakeProfitOrder.ClientOrderId)) return true;
            }
            if (!string.IsNullOrEmpty(orderId))
            {
                if (BaseOrder?.OrderId == orderId && !string.IsNullOrEmpty(BaseOrder.OrderId)) return true;
                if (TakeProfitOrder?.OrderId == orderId && !string.IsNullOrEmpty(TakeProfitOrder.OrderId)) return true;
            }
            return false;
        }

        private void UpdateFeesForOrder(OrderModelUpdate order)
        {
            if (order?.Status != OrderStatus.Filled ||
                !order.AveragePrice.HasValue || !order.QuantityFilled.HasValue || order.QuantityFilled.Value <= 0)
            {
                return;
            }

            // Calculated fee
            bool isMaker = order.OrderType == OrderType.Limit || order.TimeInForce == TimeInForce.PostOnly;
            decimal feeRate = _feeRate.GetFuturesFee(isMaker);
            decimal feeAmount = order.QuantityFilled.Value * order.AveragePrice.Value * feeRate;
            Fee calculatedFeeForOrder = new Fee(new CurrencyAmount(_tradingPair.BaseCoin, 0m), new CurrencyAmount(_tradingPair.QuoteCoin, feeAmount)).RoundTo(_tradingPair);

            // Reported fee - Fees from exchange are typically negative, but we store them as positive values.
            Fee reportedFeeForOrder = order.ExecutedFee ?? new Fee(_tradingPair);
            if (reportedFeeForOrder.Quote.Amount < 0)
            {
                reportedFeeForOrder = new Fee(reportedFeeForOrder.Base, new CurrencyAmount(reportedFeeForOrder.Quote.Currency, Math.Abs(reportedFeeForOrder.Quote.Amount)));
            }
            if (reportedFeeForOrder.Base.Amount < 0)
            {
                reportedFeeForOrder = new Fee(new CurrencyAmount(reportedFeeForOrder.Base.Currency, Math.Abs(reportedFeeForOrder.Base.Amount)), reportedFeeForOrder.Quote);
            }

            OnFeeUpdated?.Invoke(this, calculatedFeeForOrder, reportedFeeForOrder);
        }

        private void UpdatePnLForTpOrder(OrderModelUpdate takeProfitOrder)
        {
            if (BaseOrder?.Status != OrderStatus.Filled || !(BaseOrder?.AveragePrice.HasValue ?? false) || !(BaseOrder?.QuantityFilled.HasValue ?? false))
            {
                _log.Warning($"[{Name}] Cannot calculate PnL. Base order not in a valid Filled state. BaseOrder: {BaseOrder?.Status}, AvgPx: {BaseOrder?.AveragePrice}, QtyFilled: {BaseOrder?.QuantityFilled}");
                return;
            }
            if (takeProfitOrder?.Status != OrderStatus.Filled || !(takeProfitOrder?.AveragePrice.HasValue ?? false) || !(takeProfitOrder?.QuantityFilled.HasValue ?? false))
            {
                _log.Warning($"[{Name}] Cannot calculate PnL. Take profit order not in a valid Filled state. TP Order: {takeProfitOrder?.Status}, AvgPx: {takeProfitOrder?.AveragePrice}, QtyFilled: {takeProfitOrder?.QuantityFilled}");
                return;
            }

            decimal entryPrice = BaseOrder.AveragePrice.Value;
            decimal exitPrice = takeProfitOrder.AveragePrice.Value;

            // A full cycle should have matching quantities. If not, it's a partial close.
            // The PnL should be calculated based on the quantity that was actually closed.
            decimal quantity = takeProfitOrder.QuantityFilled.Value;

            decimal calculatedPnl;
            if (IsLong) // Base was Buy, TP is Sell
            {
                calculatedPnl = (exitPrice - entryPrice) * quantity;
            }
            else // Base was Sell, TP is Buy
            {
                calculatedPnl = (entryPrice - exitPrice) * quantity;
            }

            decimal reportedPnl = takeProfitOrder.ClosedPnl ?? 0m;

            OnPnLUpdated?.Invoke(this, calculatedPnl, reportedPnl);
        }

        // Here we receive the answer from OnOrderPlacementRequest
        public void HandleOrderUpdate(OrderModelUpdate update)
        {
            // Here the code has to be carreful
            if (!IsThisOrder(update.ClientOrderId, update.OrderId)) // if (!IsManagingOrder) ...
            {
                var errorResult = CreateErrorResultForFailure($"*UNEXPECTED*: OrderPair '{Name}': Received update for an order (Cloid: {update.ClientOrderId}, Oid: {update.OrderId}) that it's not actively managing. Ignoring.");
                _log.Error($"[{Name}][ORDERUPDATE]: {errorResult.Message}");
                return;
            }

            if (update.Status == OrderStatus.Created)
            {
                _log.Warning($"[{Name}][ORDERUPDATE]: *** Received CREATED order update! ***");
            }

            bool isBaseUpdateGeneral = (!string.IsNullOrEmpty(BaseOrder?.ClientOrderId)) && (BaseOrder?.ClientOrderId == update.ClientOrderId) || (!string.IsNullOrEmpty(BaseOrder?.OrderId)) && (BaseOrder?.OrderId == update.OrderId);
            bool isTpUpdateGeneral = (!string.IsNullOrEmpty(TakeProfitOrder?.ClientOrderId)) && (TakeProfitOrder?.ClientOrderId == update.ClientOrderId) || (!string.IsNullOrEmpty(TakeProfitOrder?.OrderId)) && (TakeProfitOrder?.OrderId == update.OrderId);

            // Check if this update pertains to an active placement attempt - this has to be the last 'thing' done in HandleOrderUpdate 
            // as it might complete await RequestBaseOrderPlacementAsync() or await RequestBaseOrderPlacementAsync()
            if (_activePlacementAttemptTcs != null && !_activePlacementAttemptTcs.Task.IsCompleted)
            {
                bool relevantToCurrentAttempt = false;
                if (isBaseUpdateGeneral)
                {
                    if (_placingOrderType != PlacingOrderType.Base)
                        _log.Warning($"[{Name}][ORDERUPDATE]: BaseUpdateGeneral received, but CurrentPlacingType is {_placingOrderType}");
                    relevantToCurrentAttempt = true;
                }
                else if (isTpUpdateGeneral)
                {
                    if (_placingOrderType != PlacingOrderType.TakeProfit)
                        _log.Warning($"[{Name}][ORDERUPDATE]: TakeProfitUpdateGeneral received, but CurrentPlacingType is {_placingOrderType}");
                    relevantToCurrentAttempt = true;
                }

                if (relevantToCurrentAttempt)
                {
                    if (update.Status == OrderStatus.Untriggered || update.Status == OrderStatus.New || update.Status == OrderStatus.Filled)
                    {
                        _log.Information($"[{Name}][ORDERUPDATE]: Placement attempt for {_placingOrderType} order (Cloid: {update.ClientOrderId}) confirmed by update (Status: {update.Status}).");
                        _activePlacementAttemptTcs.TrySetResult(true);
                    }
                    else if (BaseExchangeAPI.IsOrderInFinalState(update.Status))
                    {
                        _log.Warning($"[{Name}][ORDERUPDATE]: Placement attempt for {_placingOrderType} order (Cloid: {update.ClientOrderId}) received final non-success status: {update.Status}. Reason: {update.RejectReason}");
                        _activePlacementAttemptTcs.TrySetResult(false);
                    }
                    // For other non-final statuses (e.g., Created), the TCS remains pending.
                }
            }

            if (isBaseUpdateGeneral)
            {
                var previousStatus = BaseOrder?.Status;
                BaseOrder = update; // Authoritative update
                if (BaseOrder.Status == OrderStatus.Filled)
                {
                    UpdateFeesForOrder(BaseOrder);
                }
                else if (((BaseOrder.Status == OrderStatus.Rejected) || (BaseOrder.Status == OrderStatus.Cancelled)) &&
                        _placingOrderType != PlacingOrderType.Base) // Avoid double error if placement loop handles it
                {
                    _lastError = ConvertUpdateToErrorResult(update, $"[{Name}][ORDERUPDATE]: Base order (Cloid: {update.ClientOrderId}, Oid: {update.OrderId}) entered final non-filled state: {update.Status}, Reason: {update.RejectReason}");
                }
            }
            else if (isTpUpdateGeneral)
            {
                var previousStatus = TakeProfitOrder?.Status;
                TakeProfitOrder = update; // Authoritative update
                if (TakeProfitOrder.Status == OrderStatus.Filled)
                {
                    UpdateFeesForOrder(TakeProfitOrder);
                    UpdatePnLForTpOrder(TakeProfitOrder);
                }
                else if (((TakeProfitOrder.Status == OrderStatus.Rejected) || (TakeProfitOrder.Status == OrderStatus.Cancelled)) &&
                        _placingOrderType != PlacingOrderType.TakeProfit) // Avoid double error
                {
                    _lastError = ConvertUpdateToErrorResult(update, $"[{Name}][ORDERUPDATE]: TakeProfit order (Cloid: {update.ClientOrderId}, Oid: {update.OrderId}) entered final non-filled state: {update.Status}, RejectReason: {update.RejectReason}");
                }
            }
            else
            {
                // This case should ideally not be reached if IsManagingOrder worked correctly
                _log.Error($"[{Name}][ORDERUPDATE]: *UNEXPECTED*: Update for {update.ClientOrderId}/{update.OrderId} did not match current Base or TP client IDs after passing IsManagingOrder. BaseCoid:{BaseOrder?.ClientOrderId}, TPCoid:{TakeProfitOrder?.ClientOrderId}");
            }

            OnOrderUpdated?.Invoke(this, update);
        }

        public async Task<bool> RequestBaseOrderPlacementAsync()
        {
            if (IsCurrentlyPlacingOrder)
            {
                _log.Warning($"[{Name}][BASEREQ]: Base order placement request ignored, as a {_placingOrderType} order placement is already in progress.");
                return false;
            }

            bool canPlace = BaseOrder == null ||
                BaseOrder.Status == OrderStatus.Rejected ||
                BaseOrder.Status == OrderStatus.Cancelled;

            if (!canPlace)
            {
                _log.Information($"[{Name}][BASEREQ]: Base Order placement skipped. Current BaseOrder status: {BaseOrder?.Status}. Cannot place in this state.");
                return false; // Cannot place if order is active or filled
            }

            _log.Debug($"[{Name}][BASEREQ]: Initiating BaseOrder placement process.");

            _placingOrderType = PlacingOrderType.Base;
            _activePlacementAttemptTcs = null; // Ensure it's null before starting

            using var overallTimeoutCts = new CancellationTokenSource(_strategyConfig.PlacementOverallTimeoutMs);
            try
            {
                for (int attempt = 1; attempt <= _strategyConfig.PlacementMaxAttempts; attempt++)
                {
                    if (overallTimeoutCts.Token.IsCancellationRequested)
                    {
                        LastError = CreateErrorResultForFailure($"[{Name}][BASEREQ]: Base order placement cancelled before attempt {attempt} due to overall timeout..", BaseOrder?.ClientOrderId);
                        //OnError?.Invoke(this, LastError);
                        return false;
                    }

                    _activePlacementAttemptTcs = new TaskCompletionSource<bool>(TaskCreationOptions.RunContinuationsAsynchronously);

                    var requestOrderType = _isInitialPair ? _strategyConfig.BaseOrderType : OrderType.Limit; // if it is very first 'Initial Blank Slate' then BaseOrderType suppose to be set as Market
                    decimal? requestPrice = null;
                    if (requestOrderType == OrderType.Limit)
                    {
                        decimal priceAdjustment = _strategyConfig.BaseOrderLimitPriceAdjustment; // pretty much legacy, expected to be 0 in config
#if DEBUG
                        priceAdjustment = 0m;
#else
                        if (_strategyConfig.BaseOrderLimitPriceAdjustment > 0m)
                        {
                            var random = new Random();
                            priceAdjustment = (decimal)random.NextDouble() * _strategyConfig.BaseOrderLimitPriceAdjustment;
                        } else { priceAdjustment = 0m; }
#endif
                        requestPrice = IsLong ? IntendedPrice + priceAdjustment : IntendedPrice - priceAdjustment;
                        requestPrice = _tradingPair.RoundQuoteAmount(requestPrice.Value);
                    }
                    var requestTimeInForce = _isInitialPair ? _strategyConfig.BaseOrderTimeInForce : TimeInForce.PostOnly; // if it is very first 'Initial Blank Slate' then BaseOrderTimeInForce suppose to be set as GoodTillCancel
                    var baseRequest = new FuturesOrderRequest()
                    {
                        ClientId = Guid.NewGuid().ToShortId(),
                        IsBuy = IsLong,
                        Amount = _tradingPair.RoundBaseAmount(_strategyConfig.PositionSizePerStep),
                        OrderType = requestOrderType,
                        Price = requestPrice,
                        TimeInForce = requestTimeInForce,
                        IsReduceOnly = false,
                        PositionDirection = IsLong ? PositionDirection.Buy : PositionDirection.Sell
                    };

                    // Set placeholder. HandleOrderUpdate will receive the authoritative update.
                    BaseOrder = new OrderModelUpdate { ClientOrderId = baseRequest.ClientId, Status = OrderStatus.Created, Symbol = _tradingPair.Symbol, Side = IsLong ? OrderSide.Buy : OrderSide.Sell, OrderType = baseRequest.OrderType, Quantity = baseRequest.Amount, Price = baseRequest.Price };

                    _log.Information($"[{Name}][BASEREQ]: Attempt {attempt}/{_strategyConfig.PlacementMaxAttempts} to place Base order (Cloid: {baseRequest.ClientId}) @ {baseRequest.Price?.ToString("F2") ?? "Market"}.");
                    OnOrderPlacementRequest?.Invoke(this, baseRequest);

                    await _activePlacementAttemptTcs.Task.WaitAsync(overallTimeoutCts.Token);

                    if (_activePlacementAttemptTcs.Task.IsCompletedSuccessfully && _activePlacementAttemptTcs.Task.Result)
                    {
                        _log.Information($"[{Name}][BASEREQ]: Base order (Cloid: {baseRequest.ClientId}) placement attempt {attempt} successful.");
                        return true; // Successfully placed
                    }

                    _log.Warning($"[{Name}][BASEREQ]: Base order (Cloid: {baseRequest.ClientId}) placement attempt {attempt} was not confirmed successfully by TCS.");

                    if (attempt < _strategyConfig.PlacementMaxAttempts && !overallTimeoutCts.Token.IsCancellationRequested)
                    {
                        _log.Information($"[{Name}][BASEREQ]: Delaying {_strategyConfig.PlacementAttemptDelayMs}ms before next Base order placement attempt.");
                        await Task.Delay(_strategyConfig.PlacementAttemptDelayMs, overallTimeoutCts.Token);
                    }
                }
                LastError = CreateErrorResultForFailure($"Base order placement failed after {_strategyConfig.PlacementMaxAttempts} attempts.", BaseOrder?.ClientOrderId ?? "N/A_Base_All_Attempts_Failed");
                return false;
            }
            catch (OperationCanceledException)
            {
                LastError = CreateErrorResultForFailure($"Base order placement timed out.", BaseOrder?.ClientOrderId);
                return false;
            }
            catch (Exception ex)
            {
                LastError = CreateErrorResultForFailure($"Exception during base order placement: {ex.Message}", BaseOrder?.ClientOrderId);
                return false;
            }
            finally
            {
                _activePlacementAttemptTcs = null; // Ensure TCS is cleaned up
                _placingOrderType = PlacingOrderType.None;
            }
        }

        public async Task<bool> RequestTakeProfitOrderPlacementAsync()
        {
            if (IsCurrentlyPlacingOrder)
            {
                _log.Warning($"[{Name}][TPREQ]: TakeProfit order placement request ignored, as a {_placingOrderType} order placement is already in progress.");
                return false;
            }

            bool canPlace = TakeProfitOrder == null ||
                TakeProfitOrder.Status == OrderStatus.Rejected ||
                TakeProfitOrder.Status == OrderStatus.Cancelled;

            if (!canPlace)
            {
                _log.Debug($"[{Name}][TPREQ]: Take Profit Order placement skipped. Current TP Order status: {TakeProfitOrder?.Status}. Cannot place in this state.");
                return false;
            }

            _log.Information($"[{Name}][TPREQ]: Initiating TakeProfitOrder placement process.");

            _placingOrderType = PlacingOrderType.TakeProfit;
            _activePlacementAttemptTcs = null;

            using var overallTimeoutCts = new CancellationTokenSource(_strategyConfig.PlacementOverallTimeoutMs);
            try
            {
                for (int attempt = 1; attempt <= _strategyConfig.PlacementMaxAttempts; attempt++)
                {
                    if (overallTimeoutCts.Token.IsCancellationRequested)
                    {
                        LastError = CreateErrorResultForFailure($"[{Name}][TPREQ]: TP order placement cancelled before attempt {attempt} due to overall timeout.");
                        //_log.Error($"[{Name}][TPREQ]: TP order placement cancelled before attempt {attempt} due to overall timeout.");
                        //OnError?.Invoke(this, CreateErrorResultForFailure("TP order placement overall timeout.", null));
                        return false;
                    }
                    _activePlacementAttemptTcs = new TaskCompletionSource<bool>(TaskCreationOptions.RunContinuationsAsynchronously);

                    var takeProfitPrice = CalculateTakeProfitPrice();
                    if (takeProfitPrice == null)
                    {
                        LastError = CreateErrorResultForFailure($"[{Name}][TPREQ]: Cannot place Take Profit order for attempt {attempt}. Calculated TP price is null (Base order avg px: {BaseOrder?.AveragePrice}).");
                        //_log.Error($"[{Name}][TPREQ]: Cannot place Take Profit order for attempt {attempt}. Calculated TP price is null (Base order avg px: {BaseOrder?.AveragePrice}).");
                        //OnError?.Invoke(this, CreateErrorResultForFailure("Cannot calculate TP price for placement.", null));
                        return false;
                    }
                    var requestOrderType = OrderType.Limit; // _isInitialPair ? _strategyConfig.TakeProfitOrderType : OrderType.Limit; // pretty much legacy, always Limit
                    var requestTimeInForce = TimeInForce.PostOnly; //_isInitialPair ? _strategyConfig.TakeProfitOrderTimeInForce : TimeInForce.PostOnly; // pretty much legacy, always PostOnly
                    var requestPrice = requestOrderType == OrderType.Limit ? takeProfitPrice : (decimal?)null;
                    var requestAmount = BaseOrder!.QuantityFilled!.Value;

                    var tpRequest = new FuturesOrderRequest()
                    {
                        ClientId = Guid.NewGuid().ToShortId(),
                        IsBuy = !IsLong,
                        Amount = _tradingPair.RoundBaseAmount(requestAmount),
                        OrderType = requestOrderType,
                        Price = requestPrice,
                        TimeInForce = requestTimeInForce,
                        IsReduceOnly = true,
                        PositionDirection = IsLong ? PositionDirection.Buy : PositionDirection.Sell
                    };

                    TakeProfitOrder = new OrderModelUpdate { ClientOrderId = tpRequest.ClientId, Status = OrderStatus.Created, Symbol = _tradingPair.Symbol, Side = !IsLong ? OrderSide.Buy : OrderSide.Sell, OrderType = tpRequest.OrderType, Quantity = tpRequest.Amount, Price = tpRequest.Price };

                    _log.Information($"[{Name}][TPREQ]: Attempt {attempt}/{_strategyConfig.PlacementMaxAttempts} to place Take Profit order (Cloid: {tpRequest.ClientId}) for price {tpRequest.Price?.ToString("F2") ?? "Market"}.");
                    OnOrderPlacementRequest?.Invoke(this, tpRequest);

                    await _activePlacementAttemptTcs.Task.WaitAsync(overallTimeoutCts.Token);
                    
                    if (_activePlacementAttemptTcs.Task.IsCompletedSuccessfully && _activePlacementAttemptTcs.Task.Result)
                    {
                        _log.Information($"[{Name}][TPREQ]: Take Profit order (Cloid: {tpRequest.ClientId}) placement attempt {attempt} successful.");
                        return true; // Successfully placed
                    }
                    
                    _log.Warning($"[{Name}][TPREQ]: Take Profit order (Cloid: {tpRequest.ClientId}) placement attempt {attempt} was not confirmed successfully by TCS.");

                    if (attempt < _strategyConfig.PlacementMaxAttempts && !overallTimeoutCts.Token.IsCancellationRequested)
                    {
                        _log.Information($"[{Name}][TPREQ]: Delaying {_strategyConfig.PlacementAttemptDelayMs}ms before next TP order placement attempt.");
                        await Task.Delay(_strategyConfig.PlacementAttemptDelayMs, overallTimeoutCts.Token);
                    }
                }
                LastError = CreateErrorResultForFailure($"[{Name}][TPREQ]: Failed to place Take Profit order after {_strategyConfig.PlacementMaxAttempts} attempts.");
                return false;
            }
            catch (OperationCanceledException)
            {
                LastError = CreateErrorResultForFailure("TP order placement timed out.", TakeProfitOrder?.ClientOrderId);
                return false;
            }
            catch (Exception ex)
            {
                LastError = CreateErrorResultForFailure($"[{Name}][TPREQ]: Exception during TP order placement: {ex.Message}", TakeProfitOrder?.ClientOrderId);
                return false;
            }
            finally
            {
                _activePlacementAttemptTcs = null; // Ensure it's cleared
                _placingOrderType = PlacingOrderType.None;
            }
        }

        public void ReconstructOrders(OrderModelUpdate? reconstructedBaseOrder, OrderModelUpdate? reconstructedTpOrder)
        {
            _log.Information($"OrderPair '{Name}': Reconstructing state. Base: {reconstructedBaseOrder?.ClientOrderId ?? reconstructedBaseOrder?.OrderId}, TP: {reconstructedTpOrder?.ClientOrderId ?? reconstructedTpOrder?.OrderId}");
            if (reconstructedBaseOrder == null && reconstructedTpOrder == null)
            {
                _log.Warning($"OrderPair '{Name}': Both reconstructed orders are null. Cannot reconstruct state.");
                return;
            }
            BaseOrder = reconstructedBaseOrder;
            TakeProfitOrder = reconstructedTpOrder;
        }

        public decimal? CalculateTakeProfitPrice()
        {
            if (!IsBaseOrderFilled || !(BaseOrder?.AveragePrice.HasValue ?? false))
                return null;

            var calcTakeProfitPrice = IsLong ? BaseOrder.AveragePrice.Value + _strategyConfig.ProfitTargetDistance : BaseOrder.AveragePrice.Value - _strategyConfig.ProfitTargetDistance;

            if (calcTakeProfitPrice <= 0)
            {
                return null;
            }
            return _tradingPair.RoundQuoteAmount(calcTakeProfitPrice);
        }

        // Helper function to convert OrderModelUpdate to OrderResult for OnError event
        private OrderResult ConvertUpdateToErrorResult(OrderModelUpdate update, string defaultMessage)
        {
            return new FuturesOrderResult
            {
                IsSuccess = false,
                Message = update.RejectReason ?? defaultMessage,
                OrderId = update.OrderId ?? string.Empty,
                ClientOrderId = update.ClientOrderId,
                Status = update.Status, // Pass the final failed/cancelled status
                Timestamp = update.UpdateTime
            };
        }

        private OrderResult CreateErrorResultForFailure(string message, string? clientOrderId = null)
        {
            return new FuturesOrderResult
            {
                IsSuccess = false,
                Message = message,
                ClientOrderId = clientOrderId,
                Status = OrderStatus.Rejected, // Generic failure status for placement
                Timestamp = DateTime.UtcNow
            };
        }
    }
}
