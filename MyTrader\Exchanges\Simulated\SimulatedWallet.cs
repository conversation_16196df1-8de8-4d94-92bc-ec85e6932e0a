using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Linq;
using MyTraderSpace.Models;

namespace MyTraderSpace.Exchanges.Simulated
{
    /// <summary>
    /// Simulated wallet that represents the exchange-side balances
    /// </summary>
    public class SimulatedWallet : Wallet
    {
        // Store balances with both free and locked amounts
        private readonly ConcurrentDictionary<CoinType, (decimal Free, decimal Locked)> _exchangeBalances = new();
        
        public SimulatedWallet(IMarketDataService marketDataService, SimulatedExchangeConfig config) 
            : base(marketDataService)
        {
        }
        
        /// <summary>
        /// Sets the balance for a coin with separate free and locked amounts
        /// </summary>
        public void SetBalance(CoinType coin, decimal free, decimal locked)
        {
            if (free < 0)
                throw new ArgumentException("Free balance cannot be negative", nameof(free));
            if (locked < 0)
                throw new ArgumentException("Locked balance cannot be negative", nameof(locked));
                
            _exchangeBalances[coin] = (free, locked);
            
            // Update the base wallet with total amount
            base.SetBalance(coin, free + locked);
        }
        
        /// <summary>
        /// Gets the free and locked balances for a coin
        /// </summary>
        public (decimal Free, decimal Locked) GetExchangeBalance(CoinType coin)
        {
            return _exchangeBalances.GetValueOrDefault(coin, (0m, 0m));
        }
        
        /// <summary>
        /// Gets all balances with free and locked amounts
        /// </summary>
        public override IDictionary<CoinType, AssetBalance> GetAllBalances()
        {
            return _exchangeBalances.ToDictionary(
                kvp => kvp.Key,
                kvp => new AssetBalance
                {
                    Asset = kvp.Key.ToString(),
                    Available = kvp.Value.Free,
                    InOrder = kvp.Value.Locked,
                    Total = kvp.Value.Free + kvp.Value.Locked
                });
        }
        
        /// <summary>
        /// Locks funds for a coin (e.g., when placing an order)
        /// </summary>
        public bool LockFunds(CoinType coin, decimal amount)
        {
            if (amount <= 0)
                throw new ArgumentException("Amount must be positive", nameof(amount));
                
            var result = _exchangeBalances.AddOrUpdate(
                coin,
                (key) => throw new InvalidOperationException($"No balance for {coin}"),
                (key, oldValue) =>
                {
                    if (oldValue.Free < amount)
                        throw new InvalidOperationException($"Insufficient free {coin} balance");
                    return (oldValue.Free - amount, oldValue.Locked + amount);
                });
                
            // Update the base wallet balance (total remains the same, just updating for consistency)
            base.SetBalance(coin, result.Free + result.Locked);
                
            return result.Free >= 0;
        }
        
        /// <summary>
        /// Releases locked funds for a coin (e.g., when canceling an order)
        /// </summary>
        public void ReleaseFunds(CoinType coin, decimal amount)
        {
            if (amount <= 0)
                throw new ArgumentException("Amount must be positive", nameof(amount));
                
            var result = _exchangeBalances.AddOrUpdate(
                coin,
                (key) => throw new InvalidOperationException($"No balance for {coin}"),
                (key, oldValue) =>
                {
                    if (oldValue.Locked < amount)
                        throw new InvalidOperationException($"Insufficient locked {coin} balance");
                    return (oldValue.Free + amount, oldValue.Locked - amount);
                });
                
            // Update the base wallet balance
            base.SetBalance(coin, result.Free + result.Locked);
        }
        
        /// <summary>
        /// Consumes locked funds for a coin (e.g., when an order is executed)
        /// </summary>
        public void ConsumeFunds(CoinType coin, decimal amount)
        {
            if (amount <= 0)
                throw new ArgumentException("Amount must be positive", nameof(amount));
                
            var result = _exchangeBalances.AddOrUpdate(
                coin,
                (key) => throw new InvalidOperationException($"No balance for {coin}"),
                (key, oldValue) =>
                {
                    if (oldValue.Locked < amount)
                        throw new InvalidOperationException($"Insufficient locked {coin} balance");
                    return (oldValue.Free, oldValue.Locked - amount);
                });
                
            // Update the base wallet balance
            base.SetBalance(coin, result.Free + result.Locked);
        }
        
        /// <summary>
        /// Adds funds to the wallet (e.g., when an order is executed)
        /// </summary>
        public new void Add(CoinType coin, decimal amount)
        {
            if (amount <= 0)
                throw new ArgumentException("Amount must be positive", nameof(amount));
                
            var result = _exchangeBalances.AddOrUpdate(
                coin,
                (key) => (amount, 0m), // If the coin doesn't exist, create it with the specified amount
                (key, oldValue) => (oldValue.Free + amount, oldValue.Locked) // Add to free balance
            );
                
            // Update the base wallet balance
            base.SetBalance(coin, result.Free + result.Locked);
        }

        /// <summary>
        /// Adds balance for a coin using string representation (for futures trading)
        /// </summary>
        public bool AddBalance(CoinType coin, decimal amount)
        {
            if (amount <= 0)
                return false;
                
            Add(coin, amount);
            return true;
        }
        
        /// <summary>
        /// Deducts balance for a coin using string representation (for futures trading)
        /// </summary>
        public bool DeductBalance(CoinType coin, decimal amount)
        {
            if (amount <= 0)
                return false;
                
            var (free, _) = GetExchangeBalance(coin);
            if (free >= amount)
            {
                var result = _exchangeBalances.AddOrUpdate(
                    coin,
                    (key) => throw new InvalidOperationException($"No balance for {coin}"),
                    (key, oldValue) =>
                    {
                        if (oldValue.Free < amount)
                            throw new InvalidOperationException($"Insufficient free {coin} balance");
                        return (oldValue.Free - amount, oldValue.Locked);
                    });
                        
                // Update the base wallet balance
                base.SetBalance(coin, result.Free + result.Locked);
                return true;
            }
            
            return false;
        }
    }
} 