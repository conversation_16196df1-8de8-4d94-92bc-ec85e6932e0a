using System; // Needed for Action, IDisposable, Exception, TimeoutException
using System.Threading.Tasks; // Needed for Task
using System.Collections.Generic; // Needed for List
using System.Threading; // Needed for CancellationTokenSource
using System.IO; // Needed for Path
using System.Reactive.Subjects;
using System.Reactive.Linq;
using Bybit.Net.Clients;
using Bybit.Net;
using System.Reactive.Threading.Tasks;
using MyTraderSpace.Models;
using Bybit.Net.Objects.Models.V5;
using CryptoExchange.Net.Objects.Sockets;
using MyTraderSpace.Logging;

namespace MyTraderSpace.Exchanges.Bybit
{
    public class BybitMarketDataService : IMarketDataService
    {
        //public Type ExchangeType => typeof(ByBit);
        public ExchangeType ExchangeType => ExchangeType.Bybit;
        private readonly ReplaySubject<SpotMarketData> _spotMarketSubject = new(1);  // Buffer size of 1 to replay last value
        private readonly ReplaySubject<FuturesMarketData> _futuresMarketSubject = new(1);
        private readonly CancellationTokenSource _cts = new();
        private readonly object _spotLock = new();      // Protects spot-related data
        private readonly object _futuresLock = new();   // Protects futures-related data
        private readonly object _initLock = new();      // Protects initialization state
        private bool _isInitialized;
        public bool IsInitialized
        {
            get
            {
                lock (_initLock)
                {
                    return _isInitialized;
                }
            }
        }

        private SpotMarketData? _lastSpotMarketData;
        private FuturesMarketData? _lastFuturesMarketData;  // This now stores our maintained state
        private (decimal? BestBid, decimal? BestAsk) latestSpotOrderbook;
        private Task? _subscriptionTask;

        private BybitSocketClient? _socketClient;
        private volatile bool _showTickerDebugs;
        private readonly CurrencyPair _tradingPair;
        private readonly string? _saveDirectory; // Store save directory path
        private MarketDataFileManager? _fileManager; // Nullable file manager

        private readonly LogManager Log;
        private readonly BybitEnvironment _environment = BybitEnvironment.Live; // Attention/Warning/Important! Bybit uses 'Live' ticker even on DemoTrading!!

        // Track individual subscriptions
        private UpdateSubscription? _spotOrderbookSubscription;
        private UpdateSubscription? _spotTickerSubscription;
        private UpdateSubscription? _futuresTickerSubscription;

        private readonly Subject<bool> _completionSubject = new();

        private volatile bool _isStopping;  // Add this flag

        public IObservable<SpotMarketData> SpotMarketData => _spotMarketSubject.AsObservable();
        public IObservable<FuturesMarketData> FuturesMarketData => _futuresMarketSubject.AsObservable();
        
        // Implement the new interface properties
        public bool SupportsSpot => true;
        public bool SupportsFutures => true;

        public bool ShowTickerDebugs
        {
            get => _showTickerDebugs;
            set => _showTickerDebugs = value;
        }

        // Implement the new interface methods
        public IDisposable SubscribeToSpotMarketData(CurrencyPair pair, Action<SpotMarketData> callback)
        {
            return SpotMarketData.Subscribe(callback);
        }
        
        public IDisposable SubscribeToFuturesMarketData(CurrencyPair pair, Action<FuturesMarketData> callback)
        {
            return FuturesMarketData.Subscribe(callback);
        }

        public BybitMarketDataService(
            CurrencyPair tradingPair,
            BybitEnvironment? environment = null,
            string? saveDirectory = null, // Added optional parameter
            LogManager? logManager = null) // Added optional logger
        {
            _tradingPair = tradingPair;
            _environment = environment ?? BybitEnvironment.Live;
            _saveDirectory = saveDirectory; // Store the directory path
            Log = logManager ?? new LogManager(nameof(BybitMarketDataService)); // Use provided logger or create default

            Log.Information($"BybitMarketDataService created for {_tradingPair} in {_environment.Name}. Save directory: '{_saveDirectory ?? "Not specified"}'");

            if (!string.IsNullOrEmpty(_saveDirectory))
            {
                try
                {
                    // Directory creation logic remains useful here before FileManager init
                    if (!Directory.Exists(_saveDirectory))
                    {
                        Directory.CreateDirectory(_saveDirectory);
                        Log.Information($"Created save directory: '{_saveDirectory}'");
                    }
                    // Initialize FileManager - it now handles its own async saving internally
                    _fileManager = new MarketDataFileManager(_saveDirectory);
                    Log.Information($"MarketDataFileManager initialized for saving data to: '{_saveDirectory}'");
                }
                catch (Exception ex)
                {
                    Log.Error(ex, $"Failed to initialize MarketDataFileManager for directory '{_saveDirectory}'. Saving disabled.");
                    _fileManager = null;
                    _saveDirectory = null;
                }
            }
        }

        public async Task InitializeAsync()
        {
            bool success = false;
            try
            {
                lock (_initLock)
                {
                    if (_isInitialized) return;
                    _isInitialized = true;
                }
                Log.Information($"Initializing Bybit market data service for {_tradingPair}...");

                _socketClient = new BybitSocketClient(options =>
                {
                    options.Environment = _environment;
                    // Optional: Configure logging for the underlying client library if needed
                    // options.LogLevel = LogLevel.Trace;
                    // options.LogWriters = new List<ILogger> { Serilog.Log.Logger }; // Example if using Serilog globally
                });
                _subscriptionTask = InitializeTickerStreamsAsync();
                await _subscriptionTask;

                // Wait for the first data point *after* subscriptions are confirmed successful
                Log.Debug("Subscriptions initialized, waiting for first data...");
                using var timeoutCts = new CancellationTokenSource(TimeSpan.FromSeconds(10)); // Increased timeout
                // Also link with the main CTS in case StopAsync is called during init
                using var linkedCts = CancellationTokenSource.CreateLinkedTokenSource(timeoutCts.Token, _cts.Token);
                try
                {
                    var tasksToWait = new List<Task>();
                    if (SupportsSpot) tasksToWait.Add(_spotMarketSubject.FirstAsync().ToTask(linkedCts.Token));
                    if (SupportsFutures) tasksToWait.Add(_futuresMarketSubject.FirstAsync().ToTask(linkedCts.Token));

                    if (!tasksToWait.Any())
                    {
                        Log.Warning("Initialization appears complete, but no data streams (Spot/Futures) are expected.");
                        success = true; // Mark as successful even if no data expected
                        return;
                    }

                    var firstDataTask = Task.WhenAny(tasksToWait);
                    await firstDataTask;

                    if (firstDataTask.IsCompletedSuccessfully)
                    {
                        Log.Information("First market data received successfully.");
                        success = true; // Mark initialization as successful
                    }
                    else if (firstDataTask.IsFaulted)
                    {
                        var faultedTask = tasksToWait.FirstOrDefault(t => t.IsFaulted);
                        var innerEx = faultedTask?.Exception?.InnerException ?? firstDataTask.Exception?.InnerException ?? firstDataTask.Exception;
                        Log.Error(innerEx, "Error receiving first market data during initialization.");
                        throw new InvalidOperationException("Failed to receive initial market data.", innerEx);
                    }
                    else // Cancelled or timed out
                    {
                        Log.Error("Timed out or cancelled waiting for first market data during initialization.");
                        throw new TimeoutException("Bybit market data failed to start within timeout.");
                    }
                }
                catch (OperationCanceledException) when (timeoutCts.IsCancellationRequested)
                {
                    Log.Error("Timed out waiting for first market data during initialization.");
                    throw new TimeoutException("Timed out waiting for the first market data from Bybit.");
                }
                catch (OperationCanceledException) when (_cts.IsCancellationRequested)
                {
                    Log.Warning("Initialization cancelled by StopAsync.");
                    throw; // Re-throw cancellation
                }

                success = true; // Set success if first data wait succeeds
            }
            finally
            {
                if (!success)
                {
                    Log.Error("Initialization failed. Cleaning up resources.");
                    _isInitialized = false;
                    if (!_cts.IsCancellationRequested) _cts.Cancel();

                    // Cleanup for _subscriptionTask and _socketClient
                    if (_socketClient != null)
                    {
                        try { await _socketClient.UnsubscribeAllAsync(); } catch { /* Ignore */ }
                        _socketClient.Dispose();
                        _socketClient = null;
                    }
                    // Do not dispose _fileManager here, let Dispose() handle it
                }
            }
        }

        public async Task InitializeTickerStreamsAsync()
        {
            if (_socketClient == null)
            {
                throw new InvalidOperationException("Socket client is not initialized.");
            }

            Log.Debug("Initializing ticker streams...");
            var initTasks = new List<Task>();

            if (SupportsSpot)
            {
                initTasks.Add(InitializeSpotOrderbookStreamAsync());
                initTasks.Add(InitializeSpotTickerStreamAsync());
            }
            if (SupportsFutures)
            {
                initTasks.Add(InitializeFuturesTickerStreamAsync());
            }

            if (!initTasks.Any())
            {
                 Log.Warning("No streams to initialize based on SupportsSpot/SupportsFutures.");
                 return;
            }

            await Task.WhenAll(initTasks); // Let exceptions propagate
            Log.Debug("All ticker streams initialized successfully.");
        }

        private async Task InitializeSpotOrderbookStreamAsync()
        {
            try
            {
                var result = await _socketClient.V5SpotApi.SubscribeToOrderbookUpdatesAsync(
                    new[] { _tradingPair.Symbol },
                    50, // 1, 50 and 200 are the only allowed values!
                    HandleSpotOrderbookUpdateInternal,
                    _cts.Token);

                if (!result.Success)
                {
                    Log.Error($"Failed to subscribe to spot orderbook: {result.Error}");
                    throw new Exception($"Orderbook subscription failed: {result.Error}");
                }

                _spotOrderbookSubscription = result.Data;
                Log.Information($"Successfully subscribed to spot orderbook for {_tradingPair}");
            }
            catch (OperationCanceledException)
            {
                Log.Information("Spot orderbook subscription cancelled");
                throw;
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Error occurred during spot orderbook subscription");
                throw;
            }
        }

        private async Task InitializeSpotTickerStreamAsync()
        {
            try
            {
                var result = await _socketClient.V5SpotApi.SubscribeToTickerUpdatesAsync(
                    _tradingPair.Symbol,
                    HandleSpotTickerUpdateInternal,
                    _cts.Token);

                if (!result.Success)
                {
                    Log.Error($"Failed to subscribe to ByBit WebSocket: {result.Error}");
                    throw new Exception($"Ticker subscription failed: {result.Error}");
                }

                _spotTickerSubscription = result.Data;
                Log.Information($"Successfully subscribed to ByBit WebSocket spot ticker for {_tradingPair}");
            }
            catch (OperationCanceledException)
            {
                Log.Information("Spot ticker subscription cancelled");
                throw;
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Error occurred during ByBit WebSocket Spot Ticker subscription");
                throw;
            }
        }

        private async Task InitializeFuturesTickerStreamAsync()
        {
            try
            {
                var result = await _socketClient.V5LinearApi.SubscribeToTickerUpdatesAsync(
                    _tradingPair.Symbol,
                    HandleFuturesTickerUpdateInternal,
                    _cts.Token);

                if (!result.Success)
                {
                    Log.Error($"Failed to subscribe to ByBit WebSocket: {result.Error}");
                    throw new Exception($"Futures ticker subscription failed: {result.Error}");
                }

                _futuresTickerSubscription = result.Data;
                Log.Information($"Successfully subscribed to ByBit WebSocket futures ticker for {_tradingPair}");
            }
            catch (OperationCanceledException)
            {
                Log.Information("Futures ticker subscription cancelled");
                throw;
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Error occurred during ByBit WebSocket Futures Ticker subscription");
                throw;
            }
        }

        private void HandleSpotOrderbookUpdateInternal(DataEvent<BybitOrderbook> dataEvent)
        {
            if (_isStopping)  // Check if we're in the process of stopping
                return;

            var data = dataEvent.Data;
            if (data.Bids.Any() && data.Asks.Any())
            {
                lock (_spotLock)  // Protect both orderbook and potential ticker publishing
                {
                    latestSpotOrderbook = (
                        data.Bids.First().Price,
                        data.Asks.First().Price
                    );
                }

                if (ShowTickerDebugs)
                {
                    Log.Debug($"Spot Orderbook Update for {_tradingPair}:" +
                             $"\n\tBest Bid: {latestSpotOrderbook.BestBid}" +
                             $"\n\tBest Ask: {latestSpotOrderbook.BestAsk}");
                }
            }
        }

        private void HandleSpotTickerUpdateInternal(DataEvent<BybitSpotTickerUpdate> dataEvent)
        {
            if (_isStopping) return;

            try
            {
                SpotMarketData? latestSpotData = null;
                lock (_spotLock)
                {
                    if (latestSpotOrderbook.BestBid.HasValue && latestSpotOrderbook.BestAsk.HasValue)
                    {
                        latestSpotData = MarketDataMapping.MapToSpotMarket(
                            dataEvent.Data,
                            latestSpotOrderbook.BestBid.Value,
                            latestSpotOrderbook.BestAsk.Value);

                        _lastSpotMarketData = latestSpotData; // Update internal state

                        // --- Publish Copy ---
                        var spotCopy = new SpotMarketData(latestSpotData);
                        _spotMarketSubject.OnNext(spotCopy);

                        // --- Enqueue Save Operation ---
                        if (_fileManager != null)
                        {
                            FuturesMarketData? futuresCopy = null;
                            lock (_futuresLock)
                            {
                                if (_lastFuturesMarketData != null)
                                {
                                    futuresCopy = new FuturesMarketData(_lastFuturesMarketData);
                                }
                            }
                            var record = new MarketDataRecord
                            {
                                Timestamp = spotCopy.Timestamp,
                                SpotData = spotCopy,
                                FuturesData = futuresCopy
                            };

                            if (!_fileManager.TryEnqueueSave(record.SpotData.Symbol, record))
                            {
                                Log.Warning("Failed to enqueue market data record for saving (queue may be full). Symbol: {Symbol}", record.SpotData.Symbol);
                            }
                        }
                    }
                    else
                    {
                        Log.Verbose("Skipping spot ticker processing: Order book data not yet available.");
                    }
                }

                if (ShowTickerDebugs && latestSpotData != null) // Only log if data was processed
                {
                    Log.Debug("Spot Ticker Update {Symbol} Price:{Price} Bid:{Bid} Ask:{Ask} Volume:{Volume}",
                        latestSpotData.Symbol,
                        latestSpotData.LastPrice,
                        latestSpotData.HighestBid, // Use mapped bid/ask
                        latestSpotData.LowestAsk,
                        latestSpotData.Volume24h);
                }
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Error processing spot ticker update");
            }
        }

        private void HandleFuturesTickerUpdateInternal(DataEvent<BybitLinearTickerUpdate> dataEvent)
        {
            if (_isStopping) return;

            try
            {
                FuturesMarketData? futuresCopy = null; // Will hold the copy
                lock (_futuresLock)
                {
                    var mappedData = MarketDataMapping.MapToFuturesMarket(dataEvent.Data);
                    if (_lastFuturesMarketData == null)
                    {
                        _lastFuturesMarketData = mappedData;
                    }
                    else
                    {
                        _lastFuturesMarketData.Update(mappedData);
                    }

                    // --- Create and Publish Copy ---
                    futuresCopy = new FuturesMarketData(_lastFuturesMarketData);
                    _futuresMarketSubject.OnNext(futuresCopy); // Publish the copy

                    // --- Enqueue Save Operation ---
                    if (_fileManager != null)
                    {
                        SpotMarketData? spotCopy = null;
                        lock (_spotLock)
                        {
                            if (_lastSpotMarketData != null)
                            {
                                spotCopy = new SpotMarketData(_lastSpotMarketData);
                            }
                        }
                        var record = new MarketDataRecord
                        {
                            Timestamp = futuresCopy.Timestamp,
                            SpotData = spotCopy,
                            FuturesData = futuresCopy
                        };

                        if (!_fileManager.TryEnqueueSave(record.FuturesData.Symbol, record))
                        {
                            Log.Warning("Failed to enqueue market data record for saving (queue may be full). Symbol: {Symbol}", record.FuturesData.Symbol);
                        }
                    }
                }

                if (ShowTickerDebugs && futuresCopy != null) // Only log if data was processed
                {
                    Log.Debug($"Futures Ticker Update for {futuresCopy.Symbol}:" +
                            $"\n\tLast Price: {futuresCopy.LastPrice}" +
                            $"\n\tBest Ask: {futuresCopy.LowestAsk}" + // Use mapped properties
                            $"\n\tBest Bid: {futuresCopy.HighestBid}" +
                            $"\n\tMark Price: {futuresCopy.MarkPrice}" +
                            $"\n\tIndex Price: {futuresCopy.IndexPrice}" +
                            $"\n\tOpen Interest: {futuresCopy.OpenInterest}" +
                            $"\n\tFunding Rate: {futuresCopy.FundingRate:P4}");
                }
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Error processing futures ticker update");
            }
        }

        public async Task StopAsync()
        {
            if (_isStopping) return;
                _isStopping = true;
            Log.Information("Stopping BybitMarketDataService...");

            if (!_cts.IsCancellationRequested)
            {
                Log.Debug("Signaling cancellation...");
                _cts.Cancel();
            }

            // Unsubscribe streams first
            Log.Debug("Closing websocket subscriptions...");
            var unsubTasks = new List<Task>();
            if (_spotOrderbookSubscription != null) unsubTasks.Add(SafelyCloseSubscription(_spotOrderbookSubscription));
            if (_spotTickerSubscription != null) unsubTasks.Add(SafelyCloseSubscription(_spotTickerSubscription));
            if (_futuresTickerSubscription != null) unsubTasks.Add(SafelyCloseSubscription(_futuresTickerSubscription));

            try
            {
                await Task.WhenAll(unsubTasks);
                Log.Debug("All subscriptions closed.");
            }
            catch (Exception ex)
            {
                Log.Warning(ex, "Exception during subscription closing.");
            }

            // Wait for the main initialization/subscription task (if running)
            if (_subscriptionTask != null && !_subscriptionTask.IsCompleted)
            {
                Log.Debug("Waiting for main subscription task to complete...");
                try
                {
                     // Wait with a timeout in case cancellation didn't work
                    await Task.WhenAny(_subscriptionTask, Task.Delay(TimeSpan.FromSeconds(3)));
                    if (!_subscriptionTask.IsCompleted)
                    {
                        Log.Warning("Main subscription task did not complete within timeout after cancellation.");
                    }
                    else
                    {
                        Log.Debug("Main subscription task completed.");
                    }
                }
                catch (OperationCanceledException) { Log.Debug("Main subscription task cancelled as expected."); }
                catch (Exception ex) { Log.Warning(ex, "Exception waiting for main subscription task during stop."); }
            }

            // Stop the FileManager processing task gracefully (if initialized)
            if (_fileManager != null)
            {
                Log.Debug("Stopping MarketDataFileManager...");
                await _fileManager.StopAsync(TimeSpan.FromSeconds(5)); // Wait briefly for it to flush queue
                Log.Debug("MarketDataFileManager stopped.");
            }

            // Cleanup socket client
            if (_socketClient != null)
            {
                Log.Debug("Disposing socket client...");
                try
                {
                    // Might not be necessary if subscriptions are closed, but belt-and-suspenders
                    await _socketClient.UnsubscribeAllAsync();
                }
                catch (Exception ex) { Log.Warning(ex, "Exception during UnsubscribeAllAsync."); }
                _socketClient.Dispose();
                _socketClient = null;
                Log.Debug("Socket client disposed.");
            }

            _isInitialized = false;
            _isStopping = false;
            Log.Information("BybitMarketDataService stopped.");
        }

        // Helper to close subscription safely
        private async Task SafelyCloseSubscription(UpdateSubscription subscription)
        {
            try
            {
                await subscription.CloseAsync();
            }
            catch (Exception ex)
            {
                Log.Warning(ex, "Error closing subscription {SubscriptionId}", subscription.Id);
            }
        }

        public void Dispose()
        {
            Log.Debug("Disposing BybitMarketDataService...");
            try
            {
                 // StopAsync now implicitly stops the file manager too
                StopAsync().Wait(TimeSpan.FromSeconds(10));
            }
            catch (Exception ex)
            {
                Log.Warning(ex, "Exception during StopAsync within Dispose.");
            }
            finally
            {
                // Dispose managed resources
                _spotMarketSubject?.Dispose();
                _futuresMarketSubject?.Dispose();
                _completionSubject?.Dispose();
                _cts?.Dispose();
                _fileManager?.Dispose(); // Dispose file manager (calls its StopAsync again if needed)
                _socketClient?.Dispose();
                Log?.Dispose();

                GC.SuppressFinalize(this);
            }
        }

        public (SpotMarketData? Spot, FuturesMarketData? Futures) GetLatestData()
        {
            SpotMarketData? spot = null;
            FuturesMarketData? futures = null;

            lock (_spotLock)
            {
                if (_lastSpotMarketData != null)
                {
                    // Create a direct copy instead of using Update
                    spot = new SpotMarketData
                    {
                        Symbol = _lastSpotMarketData.Symbol,
                        LastPrice = _lastSpotMarketData.LastPrice,
                        HighestBid = _lastSpotMarketData.HighestBid,
                        LowestAsk = _lastSpotMarketData.LowestAsk,
                        Volume24h = _lastSpotMarketData.Volume24h
                    };
                }
            }

            lock (_futuresLock)
            {
                if (_lastFuturesMarketData != null)
                {
                    futures = new FuturesMarketData();
                    futures.Update(_lastFuturesMarketData);  // Return a copy of our maintained state
                }
            }

            return (spot, futures);
        }
    }
} 