using MyTraderSpace.Models;

namespace MyTraderSpace.Exchanges.Simulated
{
    // This class is used to store open, unfilled orders in case of simulation
    internal class SimulatedOrder
    {
        public string OrderId { get; } = Guid.NewGuid().ToString();
        public string Symbol { get; set; } = string.Empty;
        public OrderSide Side { get; set; }
        public decimal Quantity { get; set; }
        public decimal LimitPrice { get; set; }
        public decimal? Leverage { get; set; }
        public PositionDirection? PositionDirection { get; set; }
        public OrderStatus Status { get; set; }
        public DateTime CreatedTime { get; } = DateTime.UtcNow;
    }
} 