using System.Collections.Generic;
using MyTraderSpace.Logging;

namespace MyTraderSpace.Trading
{
    public class MainStrategyConfig
    {
        public LogLevel LogLevel { get; set; } = LogLevel.Information;

        /// <summary>
        /// List of BaseExchangeAPI.Name strings that MainStrategy should request from ExchangeTrader's pool.
        /// </summary>
        public List<string> ApiNamesToUse { get; set; } = new List<string>();

        /// <summary>
        /// Maximum number of concurrent HedgeGridStrategy instances.
        /// If 0, MainStrategy will attempt to use all APIs specified in ApiNamesToUse (up to the number of available APIs in the pool).
        /// </summary>
        public int MaxActiveStrategies { get; set; } = 0; 

        // Add any other MainStrategy-specific parameters here in the future
        // For example:
        // public decimal GlobalRiskLimit { get; set; } = 1000m;
        // public bool EnableDynamicStepAdjustment { get; set; } = false;
    }
} 