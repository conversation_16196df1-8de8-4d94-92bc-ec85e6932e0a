using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Collections.Concurrent;
using MyTraderSpace.Configuration;
using MyTraderSpace.Exchanges;
using MyTraderSpace.Logging;
using MyTraderSpace.Models;
using System.Reactive.Linq; // IMarketDataService.FuturesMarketData has IObservable for direct sub

namespace MyTraderSpace.Trading.Strategies
{
    /// <summary>
    /// Main strategy orchestrator.
    /// </summary>
    public class MainStrategy : IUIDataProvider, IDisposable
    {
        public CurrencyPair TradingPair { get; init; }
        private HedgeGridStrategy? _currentStrategy = null;
        public HedgeGridStrategy? CurrentStrategyForTest => CurrentStrategy;
        private HedgeGridStrategy? CurrentStrategy // a.k.a. "Frontier strategy", the one from currently processing market data updates
        {
            get => _currentStrategy;
            set
            {
                if (_currentStrategy != value)
                {
                    if (_currentStrategy != null)
                    {
                        _log.Information($"[CurrentStrategy] Unsubscribing OnMainStrategyFuturesMarketDataUpdate from old HGS '{_currentStrategy.NameId}'.");
                        _currentStrategy.OnMarketDataUpdate -= OnMainStrategyFuturesMarketDataUpdate;
                    }

                    _currentStrategy = value; // Set the new value first
                    
                    if (_currentStrategy != null)
                    {
                        // Validate
                        if (_currentStrategy.State == StrategyState.Error) // ((!_currentStrategy.IsValid) || (!_currentStrategy.IsReady)) // we might be under 'heavy' initialization when CurrentStrategy is set
                        {
                            _log.Error($"[CurrentStrategy] Attempted to set an invalid, errored, or stopped HGS ('{_currentStrategy.NameId}', State: {_currentStrategy.State}) as CurrentStrategy. Setting to null instead.");
                            _currentStrategy = null; 
                            SubscribeToGlobalMarketData(); // Ensure global sub is active if we nulled CurrentStrategy
                        }
                        else
                        {
                            _log.Information($"[CurrentStrategy] Subscribing OnMainStrategyFuturesMarketDataUpdate to new HGS '{_currentStrategy.NameId}'. Global subscription effectively paused by ProcessGlobalMarketData's internal check.");
                            _currentStrategy.OnMarketDataUpdate += OnMainStrategyFuturesMarketDataUpdate;
                            _latestGlobalFuturesData = _currentStrategy.GetLatestFuturesData() ?? _marketDataService.GetLatestData().Futures;
                            //CreateNewUpAndDownSteps();
                        }
                    }
                    else // _currentStrategy is null (being set to null from a non-null previous value)
                    {
                        _log.Information("[CurrentStrategy] CurrentStrategy is null. Ensuring MainStrategy is subscribed to global market data.");
                        SubscribeToGlobalMarketData();
                    }
                }
            }
        }
        private HedgeGridStrategy? _selectedStrategy;
        public HedgeGridStrategy? GetSelectedStrategy() => _selectedStrategy;
        private readonly ConfigurationLoader _configLoader;
        private readonly LogManager _log;
        private readonly IMarketDataService _marketDataService; 
        private List<BaseExchangeAPI> MasterApiPool { get; set; } = new List<BaseExchangeAPI>(); 
        private Dictionary<string, BaseExchangeAPI> ApiPool { get; set; } 
        public IReadOnlyDictionary<string, BaseExchangeAPI> ApiPoolForTest => ApiPool;
        private ConcurrentDictionary<string, HedgeGridStrategy> _strategyPool;
        public IReadOnlyDictionary<string, HedgeGridStrategy> StrategyPoolForTest => _strategyPool;

        private MainStrategyConfig? _mainStrategyConfig;
        private HedgeGridStrategyConfig? _hedgeGridStrategyConfig;

        private FuturesMarketData? _latestGlobalFuturesData;
        private decimal? _latestAvgBidAskPrice =>
            (_latestGlobalFuturesData?.HighestBid.HasValue == true && _latestGlobalFuturesData?.LowestAsk.HasValue == true)
                ? (_latestGlobalFuturesData.HighestBid.Value + _latestGlobalFuturesData.LowestAsk.Value) / 2m
                : null;
        private CancellationTokenSource? _runCts; 
        private bool _disposed = false; 

        private const int MaxRecentErrors = 10;
        private readonly LimitedConcurrentQueue<string> _recentErrors = new LimitedConcurrentQueue<string>(MaxRecentErrors); 

        private IDisposable? _activeMarketDataSubscriptionToken = null; // Single token for current subscription
        
        private TaskCompletionSource<bool> _initialGridSetupTcs;
        private bool _initialGridSetupCompleted = false;
        private readonly object _gridSetupLock = new object();
        
        private bool _isEvaluatingGrid = false;
        private readonly object _evaluationLock = new object();

        public StrategyState State { get; private set; } = StrategyState.Initializing;

        public decimal AccumulatedReportedRealizedPnL { get; private set; } = 0m;
        public decimal AccumulatedCalculatedRealizedPnL { get; private set; } = 0m;
        public decimal PeakStrategyCalculatedRealizedPnL { get; private set; } = 0m;
        public decimal PeakStrategyReportedRealizedPnL { get; private set; } = 0m;
        public decimal TroughStrategyCalculatedRealizedPnL { get; private set; } = decimal.MaxValue;
        public decimal TroughStrategyReportedRealizedPnL { get; private set; } = decimal.MaxValue;
        public Fee AccumulatedReportedFees { get; private set; } = new Fee();
        public Fee AccumulatedCalculatedFees { get; private set; } = new Fee();

        public MainStrategy(ConfigurationLoader configLoader, IMarketDataService marketDataService)
        {
            _configLoader = configLoader ?? throw new ArgumentNullException(nameof(configLoader));
            _log = new LogManager(nameof(MainStrategy));
            TradingPair = _configLoader.GetAppConfig().TradingPairParsed;
            _selectedStrategy = CurrentStrategy;
            _marketDataService = marketDataService ?? throw new ArgumentNullException(nameof(marketDataService));
            _initialGridSetupTcs = new TaskCompletionSource<bool>(TaskCreationOptions.RunContinuationsAsynchronously);

            AccumulatedReportedFees = new Fee(TradingPair);
            AccumulatedCalculatedFees = new Fee(TradingPair);

            ApiPool = new Dictionary<string, BaseExchangeAPI>();
            _strategyPool = new ConcurrentDictionary<string, HedgeGridStrategy>();
            _log.Information("[CONSTRUCTOR] MainStrategy created.");
        }

        // This supposed to be called before ExchangeTrader.InitializeAsync()
        public void SetAvailableApis(IEnumerable<BaseExchangeAPI> exchangeApisFromTrader)
        {
            if (State != StrategyState.Initializing)
            {
                _log.Warning($"[API SETUP] SetAvailableApis called but MainStrategy state is {State}. Cannot set APIs. Returning.");
                return;
            }
            MasterApiPool.Clear();
            if (exchangeApisFromTrader != null && exchangeApisFromTrader.Any())
            {
                _log.Information($"[API SETUP] Received {exchangeApisFromTrader.Count()} exchange APIs from trader. Setting up master API pool.");
                foreach (var api in exchangeApisFromTrader)
                {
                    // Ensure not to add duplicates to MasterApiPool if this method could be called multiple times with overlapping sets.
                    if (!MasterApiPool.Any(a => a.Name == api.Name))
                    {
                        MasterApiPool.Add(api);
                    }
                    else
                    {
                        _log.Warning($"[API SETUP] API instance '{api.Name}' already in MasterApiPool. Skipping duplicate.");
                    }
                }
            }
            else
            {
                _log.Warning("[API SETUP] No exchange APIs provided by trader. Master API pool will be empty.");
                // No exception here, InitializeAsync will handle if ApiPool remains empty
            }
            _log.Information($"[API SETUP] MainStrategy master API pool updated. Count: {MasterApiPool.Count}. Clear ApiPool before InitializeAsync reconstructs it based on config and MasterApiPool.");
            ApiPool.Clear(); // Clear the working ApiPool, it will be populated in InitializeAsync
        }

        public async Task InitializeAsync()
        {
            if (State != StrategyState.Initializing)
            {
                _log.Warning($"[INIT] MainStrategy InitializeAsync called but state is {State}. Returning.");
                return;
            }
            _log.Information("[INIT] Initializing MainStrategy...");
            try
            {
                _mainStrategyConfig = _configLoader.GetStrategyConfig<MainStrategyConfig>(nameof(MainStrategy));
                _hedgeGridStrategyConfig = _configLoader.GetStrategyConfig<HedgeGridStrategyConfig>(nameof(HedgeGridStrategy));
                _log.Information("[INIT] MainStrategyConfig and HedgeGridStrategyConfig loaded.");

                ApiPool.Clear(); 
                if (_mainStrategyConfig.ApiNamesToUse != null && _mainStrategyConfig.ApiNamesToUse.Count != 0)
                {
                    _log.Information($"[INIT] MainStrategyConfig.ApiNamesToUse has {_mainStrategyConfig.ApiNamesToUse.Count} entries. Filtering MasterApiPool.");
                    foreach (var apiNameInConfig in _mainStrategyConfig.ApiNamesToUse)
                    {
                        var apiInstanceFromMaster = MasterApiPool.FirstOrDefault(a => a.Name == apiNameInConfig);
                        if (apiInstanceFromMaster != null)
                        {
                            if (ApiPool.TryAdd(apiInstanceFromMaster.Name, apiInstanceFromMaster))
                            {
                                _log.Information($"[INIT] API '{apiInstanceFromMaster.Name}' added to MainStrategy's operational ApiPool from MasterApiPool.");
                            }
                            else
                            {
                                _log.Warning($"[INIT] API '{apiInstanceFromMaster.Name}' was already in MainStrategy's operational ApiPool. This shouldn't happen if ApiPool was cleared.");
                            }
                        }
                        else
                        {
                            _log.Warning($"[INIT] MainStrategyConfig specified API '{apiNameInConfig}', but it was not found in the MasterApiPool (count: {MasterApiPool.Count}). Skipping.");
                        }
                    }
                }
                else
                {
                    _log.Information("[INIT] MainStrategyConfig.ApiNamesToUse is empty. Attempting to use all APIs from MasterApiPool.");
                    foreach (var apiFromMaster in MasterApiPool)
                    {
                        if (ApiPool.TryAdd(apiFromMaster.Name, apiFromMaster))
                        {
                            _log.Information($"[INIT] API '{apiFromMaster.Name}' added to MainStrategy's operational ApiPool from MasterApiPool.");
                        }
                        else
                        {
                            _log.Warning($"[INIT] API '{apiFromMaster.Name}' was already in MainStrategy's operational ApiPool during full copy. This shouldn't happen.");
                        }
                    }
                }
                if (ApiPool.Count == 0)
                {
                    this.State = StrategyState.Error;
                    string errorMsg = $"[INIT] MainStrategy's operational ApiPool is empty after config processing. No APIs available to run strategies. MasterApiPool count: {MasterApiPool.Count}";
                    _log.Error(errorMsg);
                    throw new Exception(errorMsg);
                }
                _log.Information($"[INIT] MainStrategy's operational ApiPool populated. Count: {ApiPool.Count}. Starting reconstruction loop.");

                SubscribeToGlobalMarketData(); // IMPORTANT

                var apiReconstructionData = new List<(BaseExchangeAPI api, DateTime reconstructTimeStamp)>();
                foreach (var apiEntry in ApiPool.ToList()) 
                {
                    var api = apiEntry.Value;
                    DateTime maxTimestamp = DateTime.MinValue;
                    bool hasReconstructibleState = false;
                    try
                    {
                        var positions = (await api.GetPositionsAsync(Category.Linear, api.TradingPair.Symbol)).ToList();
                        var orders = (await api.GetActiveOrdersForCategoryAsync(Category.Linear, api.TradingPair.Symbol)).ToList();

                        var relevantPositions = positions.Where(p => p.Quantity != 0).ToList();

                        if (relevantPositions.Any() || orders.Any())
                        {
                            hasReconstructibleState = true;
                            foreach (var pos in relevantPositions)
                            {
                                if (pos.UpdateTime.HasValue && pos.UpdateTime.Value > maxTimestamp) maxTimestamp = pos.UpdateTime.Value;
                            }
                            foreach (var order in orders)
                            {
                                if (order.UpdateTime > maxTimestamp) maxTimestamp = order.UpdateTime;
                                if (order.CreateTime > maxTimestamp) maxTimestamp = order.CreateTime;
                            }
                        }
                    }
                    catch (Exception ex)
                    {
                        _log.Warning(ex, $"[INIT] Failed to fetch data for API '{api.Name}' during reconstruction pre-check/ordering. It will be excluded from timestamp-based reconstruction.");
                        // Keep hasReconstructibleState = false, so it won't be added based on this attempt
                    }

                    if (hasReconstructibleState)
                    {
                        apiReconstructionData.Add((api, maxTimestamp));
                        _log.Information($"[INIT] API '{api.Name}' has reconstructible state. Added to reconstruction list with timestamp {maxTimestamp}.");
                    }
                    else
                    {
                        _log.Information($"[INIT] API '{api.Name}' has no active positions or orders for symbol {api.TradingPair.Symbol}. It will not be ordered for reconstruction by timestamp.");
                        // This API remains in ApiPool, available for blank slate start if needed later.
                    }
                }

                // Sort by timestamp (ascending order - oldest first)
                var orderedApis = apiReconstructionData.OrderBy(x => x.reconstructTimeStamp).ToList();
                _log.Information($"[INIT] Ordered {orderedApis.Count} APIs by reconstruction timestamp for processing.");

                foreach (var (apiToReconstructWith, reconstructTimeStamp) in orderedApis)
                {
                    // Check if this API is still in the pool (might have been removed by previous reconstruction)
                    if (!ApiPool.ContainsKey(apiToReconstructWith.Name))
                    {
                        _log.Warning($"[INIT] API '{apiToReconstructWith.Name}' was expected in ApiPool but not found during reconstruction. Skipping as it was likely used by a previous successful HGS reconstruction.");
                        continue;
                    }
                    string hgsNameForReconstruction = $"HGS_{apiToReconstructWith.Name}";
                    _log.Information($"[INIT] Processing API '{apiToReconstructWith.Name}' with reconstructTimeStamp: {reconstructTimeStamp} for HGS '{hgsNameForReconstruction}'.");

                    HedgeGridStrategy? reconstructedHgs = await CreateAndInitializeHgsAsync(
                        hgsNameForReconstruction,
                        0, // IntendedPrice is 0 for reconstruction, HGS will derive it
                        apiToReconstructWith,
                        _marketDataService,
                        isInitialBlankSlateStep: false
                    );

                    if (reconstructedHgs != null && reconstructedHgs.IsValid && reconstructedHgs.IsReady) // Check IsReady for reconstructed
                    {
                        _log.Information($"[INIT] Reconstructed HGS '{reconstructedHgs.NameId}' initialized and is Ready. It will be started by StartAsync()");
                        // DO NOT call reconstructedHgs.StartAsync() here. MainStrategy.StartAsync will handle it.
                    }
                    else if (reconstructedHgs != null && reconstructedHgs.State == StrategyState.Error)
                    {
                        _log.Error($"[INIT] Reconstructed HGS '{reconstructedHgs.NameId}' (API: '{apiToReconstructWith.Name}') is in Error state after InitializeAsync. API NOT removed. It will be retained in error state.");
                    }
                    else
                    {
                        _log.Information($"[INIT] Failed to initialize reconstructed HGS with API '{apiToReconstructWith.Name}' (Name: '{hgsNameForReconstruction}') or it's not in a Ready state. API remains in ApiPool if HGS creation failed before API commit.");
                    }
                }

                _log.Information($"[INIT] Reconstruction scan finished. Active strategies in _strategyPool: {_strategyPool.Count}. APIs available in ApiPool: {ApiPool.Count}.");

                this.State = StrategyState.Ready;
                _log.Information($"[INIT] MainStrategy InitializeAsync finished. State: {State}. Active HGS: {_strategyPool.Count}. Available APIs: {ApiPool.Count}.");
            }
            catch (Exception ex)
            {
                this.State = StrategyState.Error;
                _log.Error(ex, $"[INIT] Critical error during MainStrategy InitializeAsync. Error: {ex.Message}");
                throw; 
            }
        }
        
        private async Task<HedgeGridStrategy?> CreateAndInitializeHgsAsync(string hgsNameId, decimal intendedPrice, BaseExchangeAPI apiForHgs, IMarketDataService mdService, bool isInitialBlankSlateStep)
        {
            if (_hedgeGridStrategyConfig == null)
            {
                _log.Error($"[{nameof(MainStrategy)}] _hedgeGridStrategyConfig is null. Cannot create HGS '{hgsNameId}'. API '{apiForHgs.Name}' will be released.");
                ReleaseApiToPool(apiForHgs); // Release API if we can't even proceed with HGS creation
                return null;
            }

            _log.Information($"[{nameof(MainStrategy)}][CREATEHGS] Creating HGS instance '{hgsNameId}' with API '{apiForHgs.Name}'. IntendedPrice: {intendedPrice}, IsInitial: {isInitialBlankSlateStep}.");
            var hgs = new HedgeGridStrategy(hgsNameId, intendedPrice, apiForHgs, mdService, _hedgeGridStrategyConfig, isInitialBlankSlateStep);

            if (_strategyPool.ContainsKey(hgs.NameId))
            {
                _log.Warning($"[{nameof(MainStrategy)}][CREATEHGS] '{hgs.NameId}' could not add to pool, because it's already in _strategyPool.");
                return null;
            }

            if (!_strategyPool.TryAdd(hgs.NameId, hgs))
            {
                _log.Error($"[{nameof(MainStrategy)}][CREATEHGS] CRITICAL: Failed to add newly created HGS '{hgs.NameId}' to _strategyPool. API '{apiForHgs.Name}' will be released. HGS will be disposed.");
                ReleaseApiToPool(apiForHgs); 
                CleanupHgs(hgs); // Dispose the HGS instance
                return null; 
            }
            _log.Information($"[{nameof(MainStrategy)}][CREATEHGS] HGS '{hgs.NameId}' (API: {apiForHgs.Name}) added to _strategyPool (Count: {_strategyPool.Count}) BEFORE InitializeAsync.");

            // Subscribe to events AFTER adding to pool and BEFORE InitializeAsync
            hgs.OnInitialPlacementFailed += HandleInitialPlacementFailed;
            hgs.OnActivated += HandleActivated;
            hgs.OnFeeUpdated += HandleFeeUpdated;
            hgs.OnPnLUpdated += HandlePnLUpdated;
            hgs.OnStrategyError += HandleHGSError;
            hgs.OnRemovalRequest += HandleStrategyRemovalRequest;

            try
            {
                await hgs.InitializeAsync(); // HGS.InitializeAsync should set its own state to Error if it fails.

                if (!hgs.IsValid)
                {
                    _log.Error($"[{nameof(MainStrategy)}][CREATEHGS] HGS '{hgs.NameId}' is in Error state after InitializeAsync. It remains in _strategyPool with its committed API '{apiForHgs.Name}'. HandleInitialPlacementFailed should manage it.");
                    // API is committed, HGS is in pool, as errored hgs elements must stay on the pool, preventing their Api's to be re-used
                }
                else if (!hgs.IsValid && !(isInitialBlankSlateStep && hgs.IntendedPrice <= 0))
                {
                    _log.Error($"[{nameof(MainStrategy)}][CREATEHGS] HGS '{hgs.NameId}' is NotValid after InitializeAsync (State: {hgs.State}, Price: {hgs.IntendedPrice}) and not a pending blank slate. API '{apiForHgs.Name}' committed. It may become an issue if not transitioned to Error by HGS itself.");
                    // Still, API is committed, HGS is in pool, as errored hgs elements must stay on the pool, preventing their Api's to be re-used
                }
                else
                {
                    _log.Information($"[{nameof(MainStrategy)}][CREATEHGS] HGS '{hgs.NameId}' (IntendedPrice: {hgs.IntendedPrice}) initialized. Current State: {hgs.State}. API '{apiForHgs.Name}' committed.");
                }
                return hgs; // Return HGS, caller will check state and decide to StartAsync
            }
            catch (Exception ex) 
            {
                _log.Error(ex, $"[{nameof(MainStrategy)}][CREATEHGS] Error during CreateAndInitializeHgsAsync for HGS '{hgs.NameId}'.");
                ReleaseApiToPool(apiForHgs); 
                CleanupHgs(hgs); 
                return null;
            }
        }

        private async Task HandleBlankSlateStartAsync()
        {
            if (_mainStrategyConfig == null || _hedgeGridStrategyConfig == null)
            {
                 _log.Error("[BLANK SLATE] Configs not loaded. Cannot start.");
                return;
            }
            // The decision to call HandleBlankSlateStartAsync is now made by EvaluateAndManageGridAsync 
            // after attempting consolidation. It can be assumed that if we are here, a blank slate is appropriate.

            BaseExchangeAPI? apiToUseForFirstStep = await AcquireNextAvailableApiAsync();
            if (apiToUseForFirstStep == null)
            {
                _log.Error("[BLANK SLATE] No available APIs in the pool to start the first step.");
                return;
            }

            if (_latestGlobalFuturesData == null && _marketDataService != null)
            {
                _latestGlobalFuturesData = _marketDataService.GetLatestData().Futures;
            }

            decimal initialIntendedPrice = decimal.Zero;
            if (_latestAvgBidAskPrice != null && _latestAvgBidAskPrice > 0)
            {
                initialIntendedPrice = _latestAvgBidAskPrice.Value;
            }
            else if (_latestGlobalFuturesData != null)
            {
                initialIntendedPrice = _latestGlobalFuturesData.MarkPrice ?? _latestGlobalFuturesData.LastPrice ?? 0m;
            }

            if (initialIntendedPrice <= 0)
            {
                _log.Error($"[BLANK SLATE] Could not determine a valid initial market price (resulted in {initialIntendedPrice}). Aborting blank slate start. Releasing API '{apiToUseForFirstStep.Name}'.");
                ReleaseApiToPool(apiToUseForFirstStep); 
                return;
            }
            initialIntendedPrice = TradingPair.RoundQuoteAmount(initialIntendedPrice);
            _log.Information($"[BLANK SLATE] Determined initial IntendedPrice: {initialIntendedPrice}. Using API '{apiToUseForFirstStep.Name}'.");

            string hgsNameForBlankSlate = $"HGS_{apiToUseForFirstStep.Name}_{initialIntendedPrice:F0}".Replace(".", "-");

            HedgeGridStrategy? newHgs = await CreateAndInitializeHgsAsync(
                hgsNameForBlankSlate,
                initialIntendedPrice,
                apiToUseForFirstStep,
                _marketDataService!,
                isInitialBlankSlateStep: true
            );

            if (newHgs != null && newHgs.IsValid && newHgs.IsReady) 
            {
                _log.Information($"[{nameof(MainStrategy)}][BLANK SLATE] HGS '{newHgs.NameId}' successfully initialized for blank slate and is Ready. It will be started by MainStrategy.StartAsync().");
                // DO NOT call await newHgs.StartAsync(); here.
            }
            else if (newHgs != null && newHgs.State == StrategyState.Error)
            {
                _log.Error($"[{nameof(MainStrategy)}][BLANK SLATE] HGS '{newHgs.NameId}' for blank slate is in Error state after InitializeAsync. API remains committed. It will be retained in error state.");
            }
            else
            {
                _log.Error($"[{nameof(MainStrategy)}][BLANK SLATE] Failed to create/initialize HGS for blank slate with API '{apiToUseForFirstStep.Name}' or it's not Ready. Releasing API if it wasn't committed by HGS creation.");
                // If newHgs is null or HGS init failed before API was taken by it, apiToUseForFirstStep needs release.
                // If newHgs exists but not IsValid/IsReady (and not Error), CreateAndInitializeHgsAsync should have handled API.
                if (newHgs == null || (newHgs != null && newHgs.ExchangeAPI != apiToUseForFirstStep)) // Check if API was actually taken
                {
                    ReleaseApiToPool(apiToUseForFirstStep);
                }
            }
            // No EvaluateAndManageGridAsync() here; MainStrategy.StartAsync -> HGS.StartAsync -> HGS.OnActivated for the newHgs will be the trigger.
        }

        private async Task CreateAndActivateNewHedgeGridStepAsync(decimal intendedPrice, BaseExchangeAPI apiToUse, IMarketDataService mdService, bool isInitialBlankSlateStep = false)
        {
            if (_mainStrategyConfig == null || _hedgeGridStrategyConfig == null)
            {
                _log.Error($"[{nameof(MainStrategy)}] Configs not loaded. Cannot create new step. API '{apiToUse.Name}' will be released.");
                ReleaseApiToPool(apiToUse);
                return;
            }

            if (_mainStrategyConfig.MaxActiveStrategies > 0 && _strategyPool.Values.Count(s => s.State == StrategyState.Running || s.IsActive()) >= _mainStrategyConfig.MaxActiveStrategies)
            {
                _log.Information($"[NEW STEP] MaxActiveStrategies ({_mainStrategyConfig.MaxActiveStrategies}) for running/active HGS reached. Cannot create for IntendedPrice {intendedPrice}. API '{apiToUse.Name}' released.");
                ReleaseApiToPool(apiToUse);
                return;
            }

            string hgsName = $"HGS_{apiToUse.Name}_{intendedPrice:F0}".Replace(".", "-"); 
            if (_strategyPool.ContainsKey(hgsName)) 
            {
                _log.Warning($"[{nameof(MainStrategy)}] HGS '{hgsName}' already exists in strategy pool. API '{apiToUse.Name}' released.");
                ReleaseApiToPool(apiToUse);
                return;
            }

            HedgeGridStrategy? newHgs = await CreateAndInitializeHgsAsync(
                hgsName,
                intendedPrice,
                apiToUse,
                mdService,
                isInitialBlankSlateStep
            );

            if (newHgs != null && newHgs.IsValid && newHgs.IsReady)
            {
                _log.Information($"[{nameof(MainStrategy)}] New HGS '{newHgs.NameId}' initialized successfully and is Ready. Attempting to start (which includes self-managed placement).");
                await newHgs.StartAsync(); // Important Note: New steps created during runtime should attempt to start immediately.
            }
            else if (newHgs != null && newHgs.State == StrategyState.Error)
            {
                 _log.Error($"[{nameof(MainStrategy)}] New HGS '{newHgs.NameId}' is in Error state after InitializeAsync. API remains committed. It will be retained in error state.");
            }
            else
            {
                _log.Error($"[{nameof(MainStrategy)}] Failed to create/initialize HGS '{hgsName}' for new step or it's not Ready. Releasing API if not committed.");
                if (newHgs == null || (newHgs != null && newHgs.ExchangeAPI != apiToUse))
                {
                    ReleaseApiToPool(apiToUse);
                }
            }
        }

        private async void HandleActivated(HedgeGridStrategy strategy)
        {
            _log.Information($"[{nameof(MainStrategy)}] Event: OnActivated received for HGS '{strategy.NameId}'. State: {strategy.State}, IntendedPrice: {strategy.IntendedPrice}, IsWasBothBaseOrdersFilledFirstTime: {strategy.IsWasBothBaseOrdersFilledFirstTime}.");

            if (!_strategyPool.ContainsKey(strategy.NameId))
            {
                _log.Warning($"[{nameof(MainStrategy)}] HGS '{strategy.NameId}' sent OnActivated, but it's not in the _strategyPool. This is unexpected. Ignoring activation.");
                strategy.ResetFirstActivation();
                return;
            }

            if (strategy.State == StrategyState.Error)
            {
                _log.Warning($"[{nameof(MainStrategy)}] HGS '{strategy.NameId}' sent OnActivated, but it's in Error state. Ignoring activation.");
                strategy.ResetFirstActivation();
                return;
            }

            if (strategy.IsWasBothBaseOrdersFilledFirstTime)
            {
                _log.Information($"[{nameof(MainStrategy)}] HGS '{strategy.NameId}' reports IsWasBothBaseOrdersFilledFirstTime. Setting as CurrentStrategy and creating flanking steps.");
                CurrentStrategy = strategy;
                await PruneDistantStrategiesAsync(strategy);
                await CreateFlankingStepsAsync(strategy);

                lock (_gridSetupLock)
                {
                    if (!_initialGridSetupCompleted)
                    {
                        _log.Information("Signaling completion of initial grid setup.");
                        _initialGridSetupTcs.TrySetResult(true);
                        _initialGridSetupCompleted = true;
                    }
                }
            }
            await EvaluateAndManageGridAsync();
        }

        private void HandleFeeUpdated(HedgeGridStrategy senderHGS, OrderPair senderOP, Fee feeCalc, Fee feeRep)
        {
            _log.Information($"[{senderHGS.NameId}] Fee Update from {senderOP.Name}. Calc: {feeCalc}, Reported: {feeRep}");
            AccumulatedCalculatedFees += feeCalc;
            AccumulatedReportedFees += feeRep;
            UpdateDrawdownAndLog();
        }

        private void HandlePnLUpdated(HedgeGridStrategy senderHGS, OrderPair senderOP, decimal pnlCalc, decimal pnlRep)
        {
            _log.Information($"[{senderHGS.NameId}] PnL Update from {senderOP.Name}. Calc: {pnlCalc}, Reported: {pnlRep}");
            AccumulatedCalculatedRealizedPnL += pnlCalc;
            AccumulatedReportedRealizedPnL += pnlRep;
            UpdateDrawdownAndLog();
        }

        private void UpdateDrawdownAndLog()
        {
            if (AccumulatedCalculatedRealizedPnL > PeakStrategyCalculatedRealizedPnL)
                PeakStrategyCalculatedRealizedPnL = AccumulatedCalculatedRealizedPnL;
            if (TroughStrategyCalculatedRealizedPnL == decimal.MaxValue || AccumulatedCalculatedRealizedPnL < TroughStrategyCalculatedRealizedPnL)
                TroughStrategyCalculatedRealizedPnL = AccumulatedCalculatedRealizedPnL;

            if (AccumulatedReportedRealizedPnL > PeakStrategyReportedRealizedPnL)
                PeakStrategyReportedRealizedPnL = AccumulatedReportedRealizedPnL;
            if (TroughStrategyReportedRealizedPnL == decimal.MaxValue || AccumulatedReportedRealizedPnL < TroughStrategyReportedRealizedPnL)
                TroughStrategyReportedRealizedPnL = AccumulatedReportedRealizedPnL;

            _log.Information($"[DRAWDOWN] AGGREGATE PnL: AccumCalcPnL: {AccumulatedCalculatedRealizedPnL:F4}, AccumRepPnL: {AccumulatedReportedRealizedPnL:F4}, AccumRepFees: {AccumulatedReportedFees.Quote.Amount:F4}, AccumCalcFees: {AccumulatedCalculatedFees.Quote.Amount:F4}");
        }

        private async Task CreateFlankingStepsAsync(HedgeGridStrategy centerStrategy)
        {
            if (centerStrategy == null || centerStrategy.State == StrategyState.Error) // !centerStrategy.IsValid
            {
                _log.Warning($"[{nameof(MainStrategy)}][FLANKING] Center strategy is null or invalid. Cannot create new steps.");
                return;
            }

            _log.Information($"[{nameof(MainStrategy)}][FLANKING] Center strategy '{centerStrategy.NameId}' (IntendedPrice: {centerStrategy.IntendedPrice}) triggered flanking step creation.");

            if (_hedgeGridStrategyConfig == null)
            {
                _log.Error($"[{nameof(MainStrategy)}][FLANKING] HedgeGridStrategyConfig is null. Cannot create new steps around HGS '{centerStrategy.NameId}'.");
                return;
            }
            decimal stepSize = _hedgeGridStrategyConfig.StepSize;
            decimal priceUp = centerStrategy.IntendedPrice + stepSize;
            decimal priceDown = centerStrategy.IntendedPrice - stepSize;

            priceUp = TradingPair.RoundQuoteAmount(priceUp);
            priceDown = TradingPair.RoundQuoteAmount(priceDown);

            _log.Information($"[{nameof(MainStrategy)}][FLANKING] Attempting to create new steps at PriceUp: {priceUp} and PriceDown: {priceDown} around '{centerStrategy.NameId}'.");

            // Check if a Not *Valid* strategy exists for UpStep
            if (!StrategyExistsNearPrice(priceUp, stepSize * 0.05m))
            {
                BaseExchangeAPI? apiForUpStep = await AcquireNextAvailableApiAsync();
                if (apiForUpStep != null)
                {
                    _log.Information($"[{nameof(MainStrategy)}][FLANKING] Acquired API '{apiForUpStep.Name}' for UpStep at {priceUp}.");
                    await CreateAndActivateNewHedgeGridStepAsync(priceUp, apiForUpStep, _marketDataService, false);
                }
                else
                {
                    _log.Warning($"[{nameof(MainStrategy)}][FLANKING] No available API for UpStep at {priceUp}.");
                }
            }
            else
            {
                _log.Information($"[{nameof(MainStrategy)}][FLANKING] Strategy already exists near UpStep price {priceUp}. Skipping creation.");
            }

            // Check for DownStep
            if (priceDown > 0 && !StrategyExistsNearPrice(priceDown, stepSize * 0.05m)) // Ensure priceDown is valid
            {
                BaseExchangeAPI? apiForDownStep = await AcquireNextAvailableApiAsync();
                if (apiForDownStep != null)
                {
                    _log.Information($"[{nameof(MainStrategy)}][FLANKING] Acquired API '{apiForDownStep.Name}' for DownStep at {priceDown}.");
                    await CreateAndActivateNewHedgeGridStepAsync(priceDown, apiForDownStep, _marketDataService, false);
                }
                else
                {
                    _log.Warning($"[{nameof(MainStrategy)}][FLANKING] No available API for DownStep at {priceDown}.");
                }
            }
            else
            {
                if(priceDown <=0)
                    _log.Warning($"[{nameof(MainStrategy)}][FLANKING] Calculated DownStep price {priceDown} is not valid. Skipping creation.");
                else
                    _log.Information($"[{nameof(MainStrategy)}][FLANKING] Strategy already exists near DownStep price {priceDown}. Skipping creation.");
            }
        }

        // Invalid (Errored, Not Ready strategies) don't count!
        private bool StrategyExistsNearPrice(decimal targetPrice, decimal tolerance)
        {
            return _strategyPool.Values.Any(s => s.IsValid && Math.Abs(s.IntendedPrice - targetPrice) < tolerance);
        }

        private async Task EvaluateAndManageGridAsync()
        {
            lock (_evaluationLock)
            {
                if (_isEvaluatingGrid)
                {
                    _log.Debug("[EvaluateAndManageGridAsync] Already in progress, skipping.");
                    return;
                }
                _isEvaluatingGrid = true;
            }

            try
            {
                _log.Information("[EvaluateAndManageGridAsync] Starting evaluation...");

                if (State != StrategyState.Running && State != StrategyState.Ready)
                {
                    _log.Warning($"[EvaluateAndManageGridAsync] MainStrategy not Running or Ready (State: {State}). Aborting.");
                    return;
                }

                decimal? relevantMarketPrice = _latestAvgBidAskPrice ?? _latestGlobalFuturesData?.MarkPrice;
                if (!relevantMarketPrice.HasValue || relevantMarketPrice.Value <= 0)
                {
                    _log.Warning("[EvaluateAndManageGridAsync] Could not determine a valid market price (_latestAvgBidAskPrice or MarkPrice). Aborting.");
                    return;
                }
                _log.Debug($"[EvaluateAndManageGridAsync] Using relevantMarketPrice: {relevantMarketPrice.Value:F2}");

                // Part 1: Determine CurrentStrategy
                var suitableStrategies = _strategyPool.Values
                    .Where(s => s.IsValid && s.IsReady && s.IsActive())
                    .ToList();

                HedgeGridStrategy? newPotentialCurrentStrategy = null;
                if (!suitableStrategies.Any())
                {
                    _log.Information("[EvaluateAndManageGridAsync] No IsValid & IsReady & IsActive() strategies found in the pool.");
                    if (CurrentStrategy != null) // If there was a CurrentStrategy, it's no longer suitable
                    {
                        _log.Information($"[EvaluateAndManageGridAsync] CurrentStrategy '{CurrentStrategy.NameId}' is no longer suitable or pool is empty. Setting CurrentStrategy to null.");
                        CurrentStrategy = null; // This will trigger global market data subscription if not already active
                    }
                    // Attempt blank slate start if pool is truly empty of any trackable HGS or if conditions warrant
                    // No suitable (IsValid & IsReady & IsActive()) strategies found.
                    _log.Information("[EvaluateAndManageGridAsync] No suitable (IsValid & IsReady & IsActive()) strategies found.");
                    await HandleBlankSlateStartAsync(); 
                    return; 
                }
                else
                {
                    newPotentialCurrentStrategy = suitableStrategies
                        .OrderBy(s => Math.Abs(s.IntendedPrice - relevantMarketPrice.Value))
                        .FirstOrDefault();

                    if (newPotentialCurrentStrategy != null)
                    {
                        if (CurrentStrategy == null || CurrentStrategy.NameId != newPotentialCurrentStrategy.NameId)
                        {
                            _log.Information($"[EvaluateAndManageGridAsync] Setting new CurrentStrategy to: {newPotentialCurrentStrategy.NameId} (Price: {newPotentialCurrentStrategy.IntendedPrice}, State: {newPotentialCurrentStrategy.State}) based on proximity to market price {relevantMarketPrice.Value:F2}.");
                            CurrentStrategy = newPotentialCurrentStrategy;
                        }
                        else
                        {
                            _log.Debug($"[EvaluateAndManageGridAsync] CurrentStrategy '{CurrentStrategy.NameId}' is already the closest suitable strategy.");
                        }
                    }
                    else // Should not happen if suitableStrategies.Any() is true, but as a safeguard
                    {
                        _log.Error("[EvaluateAndManageGridAsync] Logical error: suitableStrategies was not empty, but no closest strategy found.");
                        if (CurrentStrategy != null) 
                            CurrentStrategy = null;
                        return;
                    }
                }

                // Part 2: Hole Filling / End Addition (based on CurrentStrategy)
                if (CurrentStrategy == null) // If after all attempts, CurrentStrategy is still null (e.g., blank slate failed)
                {
                    _log.Warning("[EvaluateAndManageGridAsync] CurrentStrategy is null after determination phase. Cannot proceed with grid step creation.");
                    return;
                }

                if (_hedgeGridStrategyConfig == null) // Should have been caught earlier, but double check
                {
                    _log.Error("[EvaluateAndManageGridAsync] _hedgeGridStrategyConfig is null. Cannot proceed with step creation.");
                    return;
                }
                decimal stepSize = _hedgeGridStrategyConfig.StepSize;
                decimal distanceToMarket = Math.Abs(CurrentStrategy.IntendedPrice - relevantMarketPrice.Value);

                _log.Debug($"[EvaluateAndManageGridAsync] CurrentStrategy: {CurrentStrategy.NameId} (Price: {CurrentStrategy.IntendedPrice}). Market Price: {relevantMarketPrice.Value:F2}. Distance: {distanceToMarket:F2}. StepSize: {stepSize}.");

                if (distanceToMarket >= stepSize)
                {
                    int targetPriceDirection = (relevantMarketPrice.Value > CurrentStrategy.IntendedPrice) ? 1 : -1;
                    decimal newStepTargetPrice = CurrentStrategy.IntendedPrice + (targetPriceDirection * stepSize);
                    newStepTargetPrice = TradingPair.RoundQuoteAmount(newStepTargetPrice); // Ensure rounding

                    string reasonForNewStep = $"Market price ({relevantMarketPrice.Value:F2}) is >= StepSize ({stepSize}) away from CurrentStrategy '{CurrentStrategy.NameId}' ({CurrentStrategy.IntendedPrice:F2}).";
                    _log.Information($"[EvaluateAndManageGridAsync] New step identified. Target: {newStepTargetPrice:F2}. {reasonForNewStep}");

                    // Check if a strategy already exists at/near this target price
                    decimal existingCheckTolerance = stepSize * 0.05m; 
                    bool alreadyExists = _strategyPool.Values.Any(s => (s.IsValid && s.IsActive()) && Math.Abs(s.IntendedPrice - newStepTargetPrice) < existingCheckTolerance);

                    if (alreadyExists)
                    {
                        _log.Warning($"[EvaluateAndManageGridAsync] Wanted to create new step at {newStepTargetPrice:F2}, but an HGS already exists there (within {existingCheckTolerance:F2} tolerance).");
                    }
                    else
                    {
                        BaseExchangeAPI? api = await AcquireNextAvailableApiAsync(); 
                        if (api != null)
                        {
                            _log.Information($"[EvaluateAndManageGridAsync] Acquired API '{api.Name}' for new step at {newStepTargetPrice:F2}.");
                            await CreateAndActivateNewHedgeGridStepAsync(newStepTargetPrice, api, _marketDataService, false);
                        }
                        else 
                        {
                            _log.Warning($"[EvaluateAndManageGridAsync] Wanted to create new step at {newStepTargetPrice:F2}, but no API available even after trying to release one.");
                        }
                    }
                }
                else
                {
                    _log.Debug("[EvaluateAndManageGridAsync] Market price is within StepSize of CurrentStrategy. No new step needed based on distance from CurrentStrategy.");
                }
            }
            catch (Exception ex)
            {
                _log.Error(ex, "[EvaluateAndManageGridAsync] Critical error during grid evaluation and management.");
                // Potentially set MainStrategy state to Error or handle gracefully
            }
            finally
            {
                lock (_evaluationLock)
                {
                    _isEvaluatingGrid = false;
                }
            }
        }

        private async void ProcessGlobalMarketData(FuturesMarketData data)
        {
            if (this.State != StrategyState.Running && this.State != StrategyState.Ready) return;
            if (data == null) return;

            _log.Verbose($"[GlobalMarketData] Processing global market data for {data.Symbol}. MarkPx: {data.MarkPrice}, LastPx: {data.LastPrice}, Bid: {data.HighestBid}, Ask: {data.LowestAsk}");
            
            if (CurrentStrategy != null) // then it is subscribed to OnMainStrategyFuturesMarketDataUpdate, so that will do what we would do delow, so we return from here
                return;

            _latestGlobalFuturesData = data; // Ensures _latestGlobalFuturesData uses the most recent global tick when CurrentStrategy is null

            // Attempt to evaluate the grid. Concurrency is handled within EvaluateAndManageGridAsync.
            await EvaluateAndManageGridAsync();
        }

        // This handler is ONLY for data coming *from the CurrentStrategy's OnMarketDataUpdate event*
        private async void OnMainStrategyFuturesMarketDataUpdate(FuturesMarketData data)
        {
            // ##################################################################################
            // #######                                                                    #######
            // #######      CORE DESIGN PRINCIPLE: IGNORE TICKS WHILE PROCESSING          #######
            // #######                                                                    #######
            // ##################################################################################
            // To prevent race conditions and ensure sequential state changes, new market data
            // is IGNORED if any critical processing is already underway. "Processing" is defined
            // as the MainStrategy's grid evaluation or any sub-strategy's order placement.
            // There is NO QUEUING of ticks. Ticks are simply dropped if the system is busy.
            if (_isEvaluatingGrid || _strategyPool.Values.Any(s => s.IsAnyPairPlacing))
            {
                _log.Verbose($"[HGSMarketData] Ignored tick because a processing task is active.");
                return;
            }

            if (this.State != StrategyState.Running || data == null || CurrentStrategy == null)
            {
                if (data != null && CurrentStrategy == null)
                {
                    _log.Warning($"[HGSMarketData] Received data for {data.Symbol} but CurrentStrategy is unexpectedly null.");
                }
                return;
            }
            
            _latestGlobalFuturesData = data; // CurrentStrategy's data is considered the most up-to-date for global view.

            _log.Verbose($"[HGSMarketData] Tick from CurrentStrategy '{CurrentStrategy.NameId}': {data.Symbol} MarkPx: {data.MarkPrice}");

            // Attempt to evaluate the grid. Concurrency is handled within EvaluateAndManageGridAsync.
            await EvaluateAndManageGridAsync();
        }

        private async Task PruneDistantStrategiesAsync(HedgeGridStrategy currentActiveStrategy)
        {
            if (_hedgeGridStrategyConfig == null || _strategyPool.IsEmpty || currentActiveStrategy == null)
                return;

            _log.Information($"[{nameof(MainStrategy)}] Pruning distant strategies relative to active HGS '{currentActiveStrategy.NameId}' (IntendedPrice: {currentActiveStrategy.IntendedPrice}).");

            decimal referencePrice = currentActiveStrategy.IntendedPrice; // Use the newly active strategy's price
            decimal stepSize = _hedgeGridStrategyConfig.StepSize;
            // A strategy is distant if its IntendedPrice is more than one StepSize away from the reference.
            // (e.g., if current is 100, step 50, then anything < 0 or > 200 is too far if only 0, 50, 100, 150, 200 are target steps)
            // A simpler check: if a strategy's intended price is further than StepSize from the referencePrice,
            // and it's not the currentActiveStrategy itself, and it's not "active" (meaning its base orders are not filled).
            decimal maxAllowedDistance = stepSize;

            var strategiesToPrune = new List<HedgeGridStrategy>();

            foreach (var stratInPool in _strategyPool.Values.ToList()) // ToList for safe iteration while potentially modifying
            {
                if (stratInPool.NameId == currentActiveStrategy.NameId)
                    continue; // Don't prune the current active one

                // If a strategy is fully active (both positions open/base orders filled), it should not be pruned.
                if (stratInPool.IsActive())
                {
                    _log.Debug($"[PRUNE] HGS '{stratInPool.NameId}' is IsActive(). Skipping pruning.");
                    continue;
                }

                // The main condition for being a pruning candidate:
                // A strategy can be pruned if it's not activated (meaning no base orders are active or filled).
                bool canBePruned = !stratInPool.IsActive();

                if (canBePruned)
                {
                    decimal distance = Math.Abs(stratInPool.IntendedPrice - referencePrice);
                    // If a strategy is more than one step size away from the current active strategy, it's a candidate for pruning.
                    // Example: Active is 100, StepSize 50.
                    // A strategy at 0 (distance 100) or 200 (distance 100) should be pruned if they represent unfilled steps.
                    // A strategy at 50 (distance 50) or 150 (distance 50) should NOT be pruned by this logic.
                    // So, prune if distance > maxAllowedDistance (which is stepSize).
                    if (distance > maxAllowedDistance)
                    {
                        _log.Information($"[PRUNE] HGS '{stratInPool.NameId}' (IntendedPrice: {stratInPool.IntendedPrice}) is not Active (IsActive(): false) and distant (Dist: {distance} > MaxAllowed: {maxAllowedDistance}) from active HGS '{currentActiveStrategy.NameId}'. Marking for removal.");
                        strategiesToPrune.Add(stratInPool);
                    }
                }
            }

            foreach (var stratToPrune in strategiesToPrune)
            {
                _log.Information($"[PRUNE] Requesting consolidation and removal for distant HGS '{stratToPrune.NameId}'.");
                await stratToPrune.ConsolidateAsync(); // Attempt to cancel orders/close positions
                // HandleStrategyRemovalRequest will be called by HGS.Consolidate or its own OnRemovalRequest if it decides to self-remove.
                // If ConsolidateAsync doesn't trigger removal, we might need to call it explicitly.
                // For now, assume HGS handles its removal request or Consolidate is sufficient before direct removal.
                //HandleStrategyRemovalRequest(stratToPrune); // Explicitly remove after consolidation attempt
            }
        }

        private async Task<BaseExchangeAPI?> AcquireNextAvailableApiAsync(string? specificApiName = null)
        {
            BaseExchangeAPI? apiToReturn = null;
            if (!string.IsNullOrEmpty(specificApiName))
            {
                if (ApiPool.TryGetValue(specificApiName, out var apiInstance))
                {
                    apiToReturn = apiInstance;
                    ApiPool.Remove(apiInstance.Name); // Remove from available pool
                    _log.Information($"[API POOL] Acquired specific API '{specificApiName}'. Remaining available in pool: {ApiPool.Count}");
                }
                else
                {
                    _log.Warning($"[API POOL] Specific API '{specificApiName}' requested but not found in available pool.");
                    // For specific requests, we don't fall back to GetAndReleaseFirstApiAsync, as the caller wanted a particular one.
                }
            }
            else if (ApiPool.Count != 0)
            {
                apiToReturn = ApiPool.First().Value;
                ApiPool.Remove(apiToReturn.Name); // Remove from available pool
                _log.Information($"[API POOL] Acquired next available API '{apiToReturn.Name}' from pool. Remaining available: {ApiPool.Count}");
            }
            else
            {
                _log.Warning("[API POOL] No APIs available in the pool. Attempting to release one from an existing strategy.");
                apiToReturn = await GetAndReleaseFirstApiAsync(); // This now returns the released API directly if successful
                if (apiToReturn != null)
                {
                    // GetAndReleaseFirstApiAsync already put it in ApiPool and returned it.
                    // We now need to formally acquire it by removing it from ApiPool again.
                    if (ApiPool.ContainsKey(apiToReturn.Name))
                    {
                        ApiPool.Remove(apiToReturn.Name);
                        _log.Information($"[API POOL] Successfully acquired API '{apiToReturn.Name}' after it was released from a strategy and added to pool. Remaining available: {ApiPool.Count}");
                    }
                    else
                    {
                        _log.Error($"[API POOL] CRITICAL: API '{apiToReturn.Name}' was returned by GetAndReleaseFirstApiAsync but not found in ApiPool to be formally acquired. This should not happen.");
                        apiToReturn = null; // Cannot safely use this API
                    }
                }
                else
                {
                    _log.Warning("[API POOL] Failed to release and acquire an API from existing strategies.");
                }
            }
            return apiToReturn;
        }

        private bool ReleaseApiToPool(BaseExchangeAPI? api)
        {
            if (api == null)
            {
                _log.Warning("[API POOL] Attempted to release a null API.");
                return false;
            }
            // Ensure it's an API managed by this MainStrategy instance and not already in the available list
            if (!ApiPool.ContainsKey(api.Name))
            {
                ApiPool[api.Name] = api; // Add back to ApiPool
                _log.Information($"[API POOL] Released API '{api.Name}' back to available pool. Now available: {ApiPool.Count}");
                return true;
            }
            else
                _log.Warning($"[API POOL] API '{api.Name}' could not be released, it is already available.");
            return false;
        }

        // TODO: Don't forget to subscribe this to *all* created HGSs! (and used to display in UI)
        private void HandleHGSError(HedgeGridStrategy strategy, OrderPair? pair, OrderResult result)
        {
            string errorMessage = $"HGS Error: Strategy '{strategy.NameId}', Pair '{pair?.Name}', HGS State '{strategy.State}'. Error: '{result.Message}'. ClientOrderId: '{result.ClientOrderId}', OrderStatus: '{result.Status}'.";
            _log.Error(errorMessage);
            _recentErrors.Enqueue(errorMessage);

            if (strategy.State == StrategyState.Error)
            {
                _log.Information($"[{nameof(MainStrategy)}] HGS '{strategy.NameId}' is in Error state due to a runtime error. API '{strategy.ExchangeAPI?.Name}' will remain committed. HGS will be retained in Error state in the pool.");

                if (strategy.ExchangeAPI == null)
                {
                    _log.Error($"[{nameof(MainStrategy)}] Critical: HGS '{strategy.NameId}' in Error state but its ExchangeAPI is null. Cannot manage API commitment. Attempting removal and disposal.");
                    if (_strategyPool.TryRemove(strategy.NameId, out var removedHgs))
                    {
                        _log.Information($"[{nameof(MainStrategy)}] Errored HGS '{removedHgs.NameId}' removed from pool due to null API.");
                    }
                    CleanupHgs(strategy); // Dispose HGS
                    return;
                }

                // Ensure the API is considered "used" and not in the available ApiPool.
                if (ApiPool.ContainsKey(strategy.ExchangeAPI.Name))
                {
                    _log.Warning($"[{nameof(MainStrategy)}] API '{strategy.ExchangeAPI.Name}' for errored HGS '{strategy.NameId}' was unexpectedly found in ApiPool. Removing it now to prevent reuse.");
                    ApiPool.Remove(strategy.ExchangeAPI.Name);
                }

                // The HGS should already be in the _strategyPool if it reached a Running state and then errored.
                if (!_strategyPool.ContainsKey(strategy.NameId))
                {
                    _log.Warning($"[{nameof(MainStrategy)}] HGS '{strategy.NameId}' (State: {strategy.State}) reported a runtime error but was not found in _strategyPool. This is unexpected. It might have been removed by another process. Will not attempt to re-add.");
                    // Unlike initial placement failure, if an already-running HGS errors and is not in the pool,
                    // it's a more complex situation. We won't try to add it back. We also won't release its API
                    // here as the ownership/state is unclear. MainStrategy's overall shutdown/dispose should catch orphaned APIs if any.
                }
                else
                {
                    _log.Information($"[{nameof(MainStrategy)}] HGS '{strategy.NameId}' (State: {strategy.State}, API: {strategy.ExchangeAPI.Name}) which errored during runtime is confirmed in _strategyPool.");
                }

                if (CurrentStrategy == strategy)
                {
                    _log.Information($"[{nameof(MainStrategy)}] Errored HGS '{strategy.NameId}' was the CurrentStrategy. Nullifying CurrentStrategy.");
                    CurrentStrategy = null; // Setter handles unsubscription from HGS
                    // No direct call to EvaluateAndManageGridAsync here, as ProcessGlobalMarketData will trigger it if CurrentStrategy is null.
                }
                
                _log.Information($"[{nameof(MainStrategy)}] HGS '{strategy.NameId}' (API: {strategy.ExchangeAPI.Name}) is now in State: {strategy.State} and will be retained. Manual intervention or a restart command may be needed.");
                // DO NOT call CleanupHgs(strategy) to dispose it.
                // DO NOT call ReleaseApiToPool(strategy.ExchangeAPI).
                // Event unsubscriptions specific to MainStrategy's direct handling of THIS HGS instance (like OnStrategyError itself)
                // should remain, as the HGS object still exists in the pool. CleanupHgs would sever these if called.
            }
            else
            {
                _log.Warning($"[{nameof(MainStrategy)}] HandleHGSError called for HGS '{strategy.NameId}', but its state is '{strategy.State}' (not Error). MainStrategy will not alter its pool status based on this event alone.");
            }
        }

        // Yes, thi is practically the same as HandleHGSError, so this might be redundant in the future, EvaluateAndManageGridAsync() should 'auto-heal' 
        private void HandleInitialPlacementFailed(HedgeGridStrategy strategy, string reason)
        {
            _log.Error($"[{nameof(MainStrategy)}] HGS '{strategy.NameId}' reported OnInitialPlacementFailed. Reason: {reason}. HGS State: {strategy.State}. API '{strategy.ExchangeAPI?.Name}' will remain committed. HGS will be retained in Error state.");

            if (strategy.ExchangeAPI == null)
            {
                _log.Error($"[{nameof(MainStrategy)}] Critical: HGS '{strategy.NameId}' reported OnInitialPlacementFailed but its ExchangeAPI is null. Cannot manage API commitment. HGS will be disposed.");
                // Attempt to remove from pool if it was ever added, though unlikely for initial placement failure handler to find it there.
                if (_strategyPool.TryRemove(strategy.NameId, out var removedHgs))
                {
                    _log.Information($"[{nameof(MainStrategy)}] HGS '{removedHgs.NameId}' removed from pool due to null API during initial placement failure.");
                }
                CleanupHgs(strategy); // Dispose HGS as we can't even track its API
                return;
            }

            // Ensure the API is considered "used" by this failed HGS and is not in the available ApiPool.
            // The API should have been removed from ApiPool when the HGS was created/acquired it.
            // This check serves as a safeguard.
            if (ApiPool.ContainsKey(strategy.ExchangeAPI.Name))
            {
                _log.Warning($"[{nameof(MainStrategy)}] API '{strategy.ExchangeAPI.Name}' for HGS '{strategy.NameId}' (which failed initial placement) was unexpectedly found in ApiPool. Removing it now to prevent reuse.");
                ApiPool.Remove(strategy.ExchangeAPI.Name);
            }

            // Ensure the failed HGS instance is tracked in _strategyPool.
            // It's possible it failed before being formally added by the calling logic (e.g., in InitializeAsync's reconstruction loop).
            if (!_strategyPool.ContainsKey(strategy.NameId))
            {
                if (_strategyPool.TryAdd(strategy.NameId, strategy))
                {
                    _log.Information($"[{nameof(MainStrategy)}] HGS '{strategy.NameId}' (State: {strategy.State}) which failed initial placement has been added to _strategyPool for tracking.");
                }
                else
                {
                    // This is a very problematic state: HGS failed, and we can't even add it to the pool for tracking.
                    _log.Error($"[{nameof(MainStrategy)}] CRITICAL: Failed to add HGS '{strategy.NameId}' (State: {strategy.State}, API: {strategy.ExchangeAPI.Name}) to _strategyPool after initial placement failure. Proceeding with API release and HGS disposal as a fallback.");
                    ReleaseApiToPool(strategy.ExchangeAPI); // Release API because we can't associate it with a tracked HGS.
                    CleanupHgs(strategy); // Dispose HGS.
                    return;
                }
            }
            else
            {
                // If it's already in the pool, its state should reflect Error.
                _log.Information($"[{nameof(MainStrategy)}] HGS '{strategy.NameId}' (State: {strategy.State}) which failed initial placement was already present in _strategyPool.");
            }

            // If this failed HGS was somehow set as CurrentStrategy, clear it.
            if (CurrentStrategy == strategy)
            {
                _log.Information($"[{nameof(MainStrategy)}] HGS '{strategy.NameId}' (which failed initial placement) was CurrentStrategy. Nullifying CurrentStrategy.");
                CurrentStrategy = null; // Setter handles unsubscription from HGS
                // No direct call to EvaluateAndManageGridAsync here, as ProcessGlobalMarketData will trigger it if CurrentStrategy is null.
            }

            _log.Information($"[{nameof(MainStrategy)}] HGS '{strategy.NameId}' (API: {strategy.ExchangeAPI.Name}) is now in State: {strategy.State} and will be retained. Manual intervention or a restart command may be needed for this HGS.");
            // DO NOT call CleanupHgs(strategy) at this point.
            // DO NOT call ReleaseApiToPool(strategy.ExchangeAPI) at this point.
        }

        private async void HandleStrategyRemovalRequest(HedgeGridStrategy strategyToRemove)
        {
            if (strategyToRemove == null) return;

            _log.Information($"[{nameof(MainStrategy)}] Received OnRemovalRequest for HGS '{strategyToRemove.NameId}'.");

            // With HGS managing its own placement, _pendingPlacementStrategies is gone.
            // We only need to check the main strategy pool.
            if (_strategyPool.TryRemove(strategyToRemove.NameId, out HedgeGridStrategy? removedHgsFromPool))
            {
                _log.Information($"[{nameof(MainStrategy)}] HGS '{removedHgsFromPool.NameId}' removed from _strategyPool.");
                
                if (removedHgsFromPool.ExchangeAPI != null)
                {
                    ReleaseApiToPool(removedHgsFromPool.ExchangeAPI);
                }
                else
                {
                    _log.Warning($"[{nameof(MainStrategy)}] Removed HGS '{removedHgsFromPool.NameId}' did not have an associated ExchangeAPI instance to release.");
                }

                if (removedHgsFromPool == CurrentStrategy)
                {
                    _log.Information($"[{nameof(MainStrategy)}] Removed HGS was the CurrentStrategy ('{CurrentStrategy?.NameId}'). Setting CurrentStrategy to null.");
                    CurrentStrategy = null; // Setter handles unsubscription
                }
                
                CleanupHgs(removedHgsFromPool); // Unsubscribe events, dispose
                _log.Information($"[{nameof(MainStrategy)}] Strategy '{removedHgsFromPool.NameId}' fully removed and cleaned up. Remaining strategies in pool: {_strategyPool.Count}.");
            }
            else
            {
                _log.Warning($"[{nameof(MainStrategy)}] OnRemovalRequest for HGS '{strategyToRemove.NameId}', but it was not found in the strategy pool. Cleanup might have already occurred or it failed before being added.");
                CleanupHgs(strategyToRemove); 
            }
            // Call this AFTER potential CurrentStrategy change to re-evaluate, 
            // as removing a strategy might create a hole or change the frontier.
            await EvaluateAndManageGridAsync(); 
        }

        public async Task StartAsync()
        {
            if (State != StrategyState.Ready)
            {
                _log.Error($"[START] MainStrategy not Ready. State: {State}");
                return;
            }
            _log.Information("[START] Starting MainStrategy...");

            // First, evaluate the grid. This might create a blank slate HGS (initialized but not started)
            // or identify existing reconstructed HGSs.
            await EvaluateAndManageGridAsync();

            _log.Information("[START] Attempting to start sub-strategies in Ready state...");
            bool anyStrategyStartedOrIsRunning = false;
            foreach (var hgsKvp in _strategyPool.ToList()) 
            {
                var hgs = hgsKvp.Value;
                if (hgs.State == StrategyState.Ready)
                {
                    _log.Information($"[START] Attempting to start HGS '{hgs.NameId}' (State: {hgs.State}).");
                    await hgs.StartAsync();
                    if (hgs.State == StrategyState.Running)
                    {
                        _log.Information($"[START] HGS '{hgs.NameId}' successfully transitioned to Running.");
                        anyStrategyStartedOrIsRunning = true;
                    }
                    else
                    {
                        _log.Warning($"[START] HGS '{hgs.NameId}' did not transition to Running after StartAsync. Current state: {hgs.State}.");
                    }
                }
                else if (hgs.State == StrategyState.Running)
                {
                    _log.Information($"[START] HGS '{hgs.NameId}' is already Running.");
                    anyStrategyStartedOrIsRunning = true;
                }
            }

            if (anyStrategyStartedOrIsRunning)
            {
                State = StrategyState.Running;
                _log.Information("[START] MainStrategy is now Running with at least one sub-strategy.");
                
                _log.Information("Waiting for initial grid setup to complete...");
                try
                {
                    await _initialGridSetupTcs.Task.WaitAsync(TimeSpan.FromSeconds(15));
                    _log.Information("Initial grid setup confirmed complete.");
                }
                catch (TimeoutException)
                {
                    _log.Error("Timed out waiting for the initial grid setup to complete. The first strategy may not have activated correctly.");
                }
            }
            else
            {
                _log.Warning("[START] MainStrategy StartAsync completed, but no sub-strategies became Running or were already Running. State remains Ready.");
            }
        }

        // Optional ToDo: All the strategies to start in SetPause(true), and then RunAsync() will set them to SetPause(false)
        public async Task RunAsync(CancellationToken cancellationToken)
        {
            if (State == StrategyState.Error || State != StrategyState.Ready && State != StrategyState.Running)
            {
                _log.Warning($"RunAsync called but state is {State}");
                return;
            }
            if (State == StrategyState.Running)
            {
                _log.Warning("RunAsync called but already running.");
                // If it's already running, we assume the main loop is active.
                // This method primarily manages the lifetime via _runCts.
            }

            _log.Information("[RUN] MainStrategy RunAsync requested.");
            _runCts = CancellationTokenSource.CreateLinkedTokenSource(cancellationToken);

            try
            {
                if (State == StrategyState.Running)
                {
                    _log.Information("[RUN] MainStrategy is Running. Awaiting cancellation...");
                    await Task.Delay(Timeout.Infinite, _runCts.Token);
                }
                else
                {
                    _log.Warning($"[RUN] MainStrategy not in Running state after StartAsync logic (State: {State}). RunAsync will not block indefinitely but will await cancellation.");
                    await Task.Delay(Timeout.Infinite, _runCts.Token);
                }
            }
            catch (OperationCanceledException) when (_runCts.Token.IsCancellationRequested)
            {
                _log.Information("[RUN] MainStrategy RunAsync cancelled via _runCts.");
            }
            catch (Exception ex)
            {
                _log.Error(ex, "[RUN] Error in MainStrategy RunAsync.");
                State = StrategyState.Error;
            }
            finally
            {
                if (State == StrategyState.Running || State == StrategyState.Ready) 
                    State = StrategyState.Stopping;
                
                _log.Information($"[RUN] MainStrategy RunAsync finishing. (State: {State}).");
                _runCts?.Dispose(); 
                _runCts = null;
                _log.Information($"[RUN] MainStrategy RunAsync finished. Final State: {State}");
            }
        }

        public async Task StopAsync()
        {
            if (State == StrategyState.Stopped || State == StrategyState.Stopping)
            {
                _log.Warning($"MainStrategy already stopping/stopped.");
                return;
            }
            _log.Information("[STOP] MainStrategy stopping all sub-strategies...");
            State = StrategyState.Stopping;
            _runCts?.Cancel();

            List<Task> stopTasks = new List<Task>();
            foreach (var strategy in _strategyPool.Values.ToList())
            {
                stopTasks.Add(strategy.StopAsync());
            }
            try
            {
                await Task.WhenAll(stopTasks).WaitAsync(TimeSpan.FromSeconds(15));
            }
            catch (TimeoutException)
            {
                _log.Warning("[STOP] Timeout waiting for sub-strategies to stop.");
            }
            _log.Information("[STOP] MainStrategy processing finished.");
            State = StrategyState.Stopped;
        }

        private void CleanupHgs(HedgeGridStrategy hgs)
        {
            if (hgs == null) return;
            _log.Information($"[{nameof(MainStrategy)}] Cleaning up HGS '{hgs.NameId}'. Unsubscribing events and disposing.");
            hgs.OnInitialPlacementFailed -= HandleInitialPlacementFailed;
            hgs.OnActivated -= HandleActivated;
            hgs.OnFeeUpdated -= HandleFeeUpdated;
            hgs.OnPnLUpdated -= HandlePnLUpdated;
            hgs.OnStrategyError -= HandleHGSError;
            hgs.OnRemovalRequest -= HandleStrategyRemovalRequest;
            (hgs as IDisposable)?.Dispose();
        }

        private async Task<BaseExchangeAPI?> GetAndReleaseFirstApiAsync()
        {
            // If ApiPool is not empty, this method should ideally not be called by AcquireNextAvailableApiAsync's new logic.
            // However, if called directly and ApiPool has items, it indicates a logic flaw elsewhere or direct misuse.
            // For robustness, let's log if it's called when ApiPool isn't empty, but proceed to release from _strategyPool.
            if (ApiPool.Count > 0)
            {
                _log.Warning($"[GetAndReleaseFirstApiAsync] Called when ApiPool is not empty (Count: {ApiPool.Count}). This is unexpected. Proceeding to release from _strategyPool if necessary.");
            }

            if (_strategyPool.IsEmpty)
            {
                _log.Warning("[GetAndReleaseFirstApiAsync] No available APIs in pool and no strategies in _strategyPool to release an API from.");
                return null;
            }

            decimal? avgPrice = _latestAvgBidAskPrice ?? _latestGlobalFuturesData?.MarkPrice ?? _latestGlobalFuturesData?.LastPrice;
            if (avgPrice == null)
            {
                _log.Error("[GetAndReleaseFirstApiAsync] Cannot determine current average price. Aborting API release from strategy.");
                return null;
            }

            // Find the HGS with the maximum distance from avgPrice
            var furthestHgs = _strategyPool.Values
                .Where(hgs => hgs.IsValid && hgs.State != StrategyState.Error && hgs.ExchangeAPI != null) // Ensure HGS is in a state where its API can be taken
                .OrderByDescending(hgs => Math.Abs(hgs.IntendedPrice - avgPrice.Value))
                .FirstOrDefault();

            if (furthestHgs == null)
            {
                _log.Warning("[GetAndReleaseFirstApiAsync] No suitable HGS found in _strategyPool to release an API from (e.g., all errored, or no valid price).");
                return null;
            }

            _log.Information($"[GetAndReleaseFirstApiAsync] Attempting to stop and consolidate HGS '{furthestHgs.NameId}' (IntendedPrice: {furthestHgs.IntendedPrice}) to release its API '{furthestHgs.ExchangeAPI!.Name}'.");

            BaseExchangeAPI? apiToRelease = furthestHgs.ExchangeAPI; // Store before HGS is potentially altered

            try
            {
                await furthestHgs.ConsolidateAsync(); // this also does StopAsync()
            }
            catch (Exception ex)
            {
                _log.Error(ex, $"[GetAndReleaseFirstApiAsync] Exception while stopping/consolidating HGS '{furthestHgs.NameId}'. API '{apiToRelease?.Name}' might not be cleanly released.");
                // Decide if we should still attempt to remove HGS and release API or if the API is now in an unknown state.
                // For now, we'll proceed with removal and API release, assuming HGS tried its best.
            }

            // Remove from pool and cleanup HGS regardless of stop/consolidate outcome, as we intend to take its API.
            if (_strategyPool.TryRemove(furthestHgs.NameId, out var removedHgs))
            {
                _log.Information($"[GetAndReleaseFirstApiAsync] HGS '{removedHgs.NameId}' removed from _strategyPool.");
                CleanupHgs(removedHgs);
            }
            else
            {
                _log.Warning($"[GetAndReleaseFirstApiAsync] Failed to remove HGS '{furthestHgs.NameId}' from _strategyPool during API release. It might have been removed by another process.");
                CleanupHgs(furthestHgs); // Still attempt cleanup on the original instance
            }

            if (apiToRelease != null)
            {
                // Add the API back to the ApiPool, making it available.
                // AcquireNextAvailableApiAsync will then pick it up.
                if (ReleaseApiToPool(apiToRelease)) // ReleaseApiToPool adds it to ApiPool
                {
                    _log.Information($"[GetAndReleaseFirstApiAsync] API '{apiToRelease.Name}' released from HGS '{furthestHgs.NameId}' and added to ApiPool. It will be acquired by the caller.");
                    return apiToRelease; // Return the API that was just made available
                }
                else
                {
                    _log.Error($"[GetAndReleaseFirstApiAsync] Failed to release API '{apiToRelease.Name}' back to ApiPool (e.g., already there). This is a critical state for API '{apiToRelease.Name}'.");
                    return null; // Cannot safely return this API
                }
            }
            else
            {
                _log.Warning($"[GetAndReleaseFirstApiAsync] HGS '{furthestHgs.NameId}' had a null ExchangeAPI before release attempt. Cannot return an API.");
                return null;
            }
        }

        // TODO: Regading to this Snapshot, this should be adapted/refactored to show these more 'global' MainStrategy specific informations!
        public StrategyUIData? GetUIDataSnapshot()
        {
            if (_selectedStrategy == null && _strategyPool.IsEmpty)
            {
                // If no strategies exist at all, reflect this state.
                return new StrategyUIData
                {
                    StrategyStatus = $"MainStrategy: {this.State} (No active steps or selection)",
                    APIStatus = ExchangeState.Stopped, // No active API through a strategy
                    Timestamp = DateTime.UtcNow
                };
            }
            // Primarily report data from the _selectedStrategy (frontier strategy)
            if (_selectedStrategy != null)
            {
                var snap = _selectedStrategy.GetUIDataSnapshot();
                if (snap != null)
                {
                    snap.StrategyStatus = $"Main(SelHGS:{_selectedStrategy.NameId}): {snap.StrategyStatus}";
                }
                return snap;
            }
            // Fallback if no specific strategy is selected but pool exists (e.g. show aggregate or first)
            var firstStrat = _strategyPool.Values.FirstOrDefault();
            if (firstStrat != null)
            {
                var snap = firstStrat.GetUIDataSnapshot();
                if (snap != null)
                {
                    snap.StrategyStatus = $"Main(FirstHGS:{firstStrat.NameId}): {snap.StrategyStatus}";
                }
                return snap;
            }
            // Should ideally not be reached if the firstIsEmpty check is comprehensive
            return new StrategyUIData { StrategyStatus = $"MainStrategy: {this.State} (Error determining UI data)", Timestamp = DateTime.UtcNow };
        }

        public void SelectSubStrategy(string strategyNameId)
        {
            if (_strategyPool.TryGetValue(strategyNameId, out var strategyFromPool))
            {
                if (_selectedStrategy != null && _selectedStrategy != strategyFromPool)
                {
                    //_selectedStrategy.OnMarketDataUpdate -= OnMainStrategyFuturesMarketDataUpdate; // no, market data update is from _currentStrategy
                }
                _selectedStrategy = strategyFromPool;
                //_selectedStrategy.OnMarketDataUpdate += OnMainStrategyFuturesMarketDataUpdate; // no, market data update is from _currentStrategy
                _log.Information($"[SELECT] MainStrategy selected '{_selectedStrategy.NameId}' for UI and market data.");
            }
            else { _log.Warning($"[SELECT] Sub-strategy {strategyNameId} not found."); }
        }

        public async Task SetPauseAsync(bool isPaused)
        {
            _log.Information($"[PAUSE] MainStrategy setting pause to {isPaused} for sub-strategies.");
            foreach (var subStrategy in _strategyPool.Values.ToList())
            {
                await subStrategy.SetPauseAsync(isPaused);
            }
            if (isPaused && State == StrategyState.Running)
                State = StrategyState.Ready;
            // If unpausing and was Ready, it should go to Running after sub-strategies confirm they are running.
            // This might need more robust state transition logic based on sub-strategy states.
        }

        public void Dispose()
        {
            Dispose(true);
            GC.SuppressFinalize(this);
        }

        protected virtual void Dispose(bool disposing)
        {
            if (_disposed) return; // Check _disposed flag
            if (disposing)
            {
                _log.Information("[DISPOSE] Disposing MainStrategy resources...");
                if (State != StrategyState.Stopped && State != StrategyState.Error)
                {
                    _log.Warning($"[DISPOSE] MainStrategy Dispose called while state is {State}. Attempting to Stop...");
                    StopAsync().Wait(TimeSpan.FromSeconds(5)); // Blocking stop with timeout
                }
                foreach (var strat in _strategyPool.Values.ToList()) { (strat as IDisposable)?.Dispose(); }
                _strategyPool.Clear();
                ApiPool.Clear();
                _runCts?.Cancel(); _runCts?.Dispose();
                _activeMarketDataSubscriptionToken?.Dispose();
                _log?.Dispose();
            }
            State = StrategyState.Stopped; // Set to a final, non-Disposed state from StrategyState enum
            _disposed = true; // Set _disposed flag
        }

        private void SubscribeToGlobalMarketData()
        {
            if (_activeMarketDataSubscriptionToken == null && _marketDataService != null)
            {
                var primaryPairForSubscription = ApiPool.FirstOrDefault().Value?.TradingPair ?? MasterApiPool.FirstOrDefault()?.TradingPair ?? CommonPairs.BTCUSDT;
                _activeMarketDataSubscriptionToken = _marketDataService.SubscribeToFuturesMarketData(
                    primaryPairForSubscription,
                    ProcessGlobalMarketData // Global data now also flows through this single handler
                );
                _latestGlobalFuturesData = _marketDataService.GetLatestData().Futures;
                _log.Information($"[SubGlobal] MainStrategy now subscribed to IMarketDataService for pair {primaryPairForSubscription} via OnMainStrategyFuturesMarketDataUpdate.");
            }
        }

        private async Task<bool> ConsolidateNonActivatedStrategiesAsync()
        {
            _log.Information("[ConsolidateNonActivated] Checking for non-activated or errored strategies to consolidate...");
            var strategiesToConsolidate = _strategyPool.Values
                .Where(s => !s.IsActive() && s.State != StrategyState.Error)
                .ToList();

            if (!strategiesToConsolidate.Any())
            {
                _log.Information("[ConsolidateNonActivated] No non-activated or errored strategies found in the pool.");
                return false;
            }

            _log.Information($"[ConsolidateNonActivated] Found {strategiesToConsolidate.Count} non-activated/errored strategies to consolidate.");
            foreach (var hgs in strategiesToConsolidate)
            {
                _log.Information($"[ConsolidateNonActivated] Requesting consolidation for HGS '{hgs.NameId}' (State: {hgs.State}, IsValid: {hgs.IsValid}, IsActive(): {hgs.IsActive()}).");
                await hgs.ConsolidateAsync(); // this also does StopAsync()
                // ConsolidateAsync should trigger OnRemovalRequest, which calls HandleStrategyRemovalRequest.
                // HandleStrategyRemovalRequest will then remove from _strategyPool and release API.
            }
            _log.Information("[ConsolidateNonActivated] Finished attempting to consolidate non-activated/errored strategies.");
            return true;
        }
    }
}