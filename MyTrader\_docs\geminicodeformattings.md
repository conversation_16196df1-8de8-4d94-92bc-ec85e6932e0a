---
description: Mandatory code formatting requirements for Gemini models
globs: 
alwaysApply: true
---
** Gemini models code formatting rules **
 * ALWAYS STRIVE TO WRITE ELEGANT, EASY-TO-READ WELL FORMATTED CODE *
 * Avoid squeezing codes in one line, or overcompactize, like:

 *** Example 1:

 Wrong:
 if (condition) { things_done_here }

 Correct:
 if (condition)
 {
    things_done_here
 }

*** Example 2:
Wrong:
try { code_here } catch (Exception ex) { things_here } finally { final_things_here }

Correct:
try
{
    code_here
}
catch (Exception ex)
{
    things_here
}
finally
{
    final_things_here
}

** Identation consist of 4 (four!) Space! Characters! No tab, and not 5 spaces!, all the time:
Example 1:

Wrong:
if (condition)
{
     things_here // <- notice it is suffixed with 5 spaces: space-space-space-space-space-spacethings_here; // <- WRONG!
}

Correct
if (condition)
{
    things_here // <- notice it is suffixed with 4 spaces: space-space-space-space-spacethings_here; // <-CORRECT!
}

** Always leave space before a paranthesis or bracket is opened:
Wrong:
if(condition) { theing_here } // Notice no space between "if(" // <- WRONG!

Correct:
if (condition) // Notice the space between "if (" // <- CORRECT!
{
    thing_here
}
