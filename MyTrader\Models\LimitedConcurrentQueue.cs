using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Linq;

namespace MyTraderSpace.Models
{
    /// <summary>
    /// A thread-safe queue that maintains a maximum size by removing the oldest entries 
    /// when new ones are added.
    /// </summary>
    public class LimitedConcurrentQueue<T>
    {
        private readonly ConcurrentQueue<T> _queue;
        private readonly int _maxSize;
        private readonly object _lock = new object(); // For synchronizing trim operations
        public int Count => _queue.Count;
        public bool IsEmpty => _queue.IsEmpty;

        public LimitedConcurrentQueue(int maxSize)
        {
            if (maxSize <= 0)
                throw new ArgumentOutOfRangeException(nameof(maxSize), "Maximum size must be greater than zero.");

            _maxSize = maxSize;
            _queue = new ConcurrentQueue<T>();
        }

        public void Enqueue(T item)
        {
            _queue.Enqueue(item);

            // Trim excess items. This needs to be atomic.
            // While ConcurrentQueue.Enqueue is thread-safe, the count check and dequeue loop isn't atomic with Enqueue.
            if (_queue.Count > _maxSize) // Quick check to avoid locking if not necessary
            {
                lock (_lock)
                {
                    while (_queue.Count > _maxSize)
                    {
                        _queue.TryDequeue(out _);
                    }
                }
            }
        }

        public List<T> ToList()
        {
            // ConcurrentQueue.ToArray makes a snapshot, so this is thread-safe.
            return _queue.ToList(); 
        }
    }
} 