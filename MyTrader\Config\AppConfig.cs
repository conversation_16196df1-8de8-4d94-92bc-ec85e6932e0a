using System;
using System.Collections.Generic;
using MyTraderSpace.Exchanges;
using MyTraderSpace.Models;
using System.Text.Json.Serialization;

namespace MyTraderSpace.Configuration
{
    public class AppConfig
    {
        public string DefaultMode { get; set; } = "Demo";
        public string TradingPair { get; set; } = "BTCUSDT";
        
        [JsonIgnore]
        public CurrencyPair TradingPairParsed { get; internal set; }
        
        public Dictionary<string, TradingModeConfig> Modes { get; set; } = new Dictionary<string, TradingModeConfig>();
        public ParametersConfig Parameters { get; set; } = new ParametersConfig();
        public LoggingConfig Logging { get; set; } = new LoggingConfig();
    }

    public class TradingModeConfig
    {
        public MarketDataConfig MarketData { get; set; } = new MarketDataConfig();
        public List<ExchangeEntryConfig> Exchanges { get; set; } = new List<ExchangeEntryConfig>();
    }

    public class MarketDataConfig
    {
        public string Type { get; set; } = "Simulated";
        
        [JsonIgnore(Condition = JsonIgnoreCondition.WhenWritingNull)]
        public string? Environment { get; set; }
        
        [JsonIgnore(Condition = JsonIgnoreCondition.WhenWritingNull)]
        public string? DefaultDataDirectory { get; set; }
        
        [JsonIgnore(Condition = JsonIgnoreCondition.WhenWritingNull)]
        public decimal? InitialPrice { get; set; }
        
        [JsonIgnore(Condition = JsonIgnoreCondition.WhenWritingNull)]
        public int? UpdateIntervalMs { get; set; }
        
        [JsonIgnore(Condition = JsonIgnoreCondition.WhenWritingNull)]
        public decimal? PriceVolatility { get; set; }
        
        [JsonIgnore(Condition = JsonIgnoreCondition.WhenWritingNull)]
        public decimal? SpreadPercent { get; set; }
        
        [JsonIgnore(Condition = JsonIgnoreCondition.WhenWritingNull)]
        public bool? SimulateFutures { get; set; }
    }

    public class ExchangeEntryConfig
    {
        public string Type { get; set; } = "Simulated"; // "Simulated" or "Bybit"
        public string? Name { get; set; }
        public List<string> Pools { get; set; } = new List<string>(); // "Spot" or "Futures"
        public Dictionary<string, object> Config { get; set; } = new Dictionary<string, object>(); // TODO / Note: Later eventually a more proper merge class of SimulatedExchangeConfigOptions and BybitExchangeConfigOptions
    }

    // TODO / Note: A similar class like this and/or merged with the BybitExchangeConfigOptions class to be used in the above ExchangeEntryConfig's Config dictionary's 'object' value
    public class SimulatedExchangeConfigOptions
    {
        public decimal InitialBaseBalance { get; set; } = 1.0m;
        public decimal InitialQuoteBalance { get; set; } = 100_000m;
        public bool SimulateSlippage { get; set; } = false;
        public decimal MaxSlippagePercent { get; set; } = 0.001m;
        public string MarginMode { get; set; } = "RegularMargin";
        public string TradeMode { get; set; } = "CrossMargin";
        public string PositionMode { get; set; } = "BothSides";
    }

    // TODO / Note: A similar class like this and/or merged with the SimulatedExchangeConfigOptions class to be used in the above ExchangeEntryConfig's Config dictionary's 'object' value
    public class BybitExchangeConfigOptions
    {
        public string KeysFile { get; set; } = string.Empty;
        public string Environment { get; set; } = "DemoTrading";
        public string? MarginMode { get; set; }
        public string? TradeMode { get; set; }
        public string? PositionMode { get; set; }
        public decimal? Leverage { get; set; }
    }

    public class ParametersConfig
    {
        public decimal DummyParam1 { get; set; } = 0.001m;
        public decimal DummyParam2 { get; set; } = 0.005m;
        public decimal DummyParam3 { get; set; } = 0.001m;
        public CheckStuffConfig CheckStuffConfig { get; set; } = new CheckStuffConfig();
    }

    public class CheckStuffConfig
    {
        private int _intervalMinutes = 1;  // Default 1 minute

        public int IntervalMinutes
        {
            get => _intervalMinutes;
            set
            {
                if (value < 1)
                    throw new ArgumentException("Checking interval must be at least 1 minute");
                _intervalMinutes = value;
            }
        }
    }

    // TODO: Use this in main Program.cs to construct the LogManager logger (once done, remove this note)
    public class LoggingConfig
    {
        public string MinimumLevel { get; set; } = "Debug";
        public bool ConsoleEnabled { get; set; } = true;
        public bool FileEnabled { get; set; } = true;
        public string FilePath { get; set; } = "logs/mytrader-.txt";
        public string RollingInterval { get; set; } = "Day";
    }
} 