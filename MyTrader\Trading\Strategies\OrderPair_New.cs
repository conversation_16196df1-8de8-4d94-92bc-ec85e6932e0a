﻿using System;
using MyTraderSpace.Logging;
using MyTraderSpace.Models;
using MyTraderSpace.Utils;
using MyTraderSpace.Exchanges;

namespace MyTraderSpace.Trading.Strategies
{
    public class OrderPair_New
    {
        private readonly LogManager _log;
        private readonly HedgeGridStrategyConfig _strategyConfig;
        private readonly CurrencyPair _tradingPair;
        private readonly FeeConfig _feeRate;

        public bool IsLong { get; }

        private string _name = string.Empty;
        public string Name => _name + (IsLong ? "_LongSide" : "_ShortSide");

        private readonly bool _isInitialPair = false;

        // Order State - store the most recent update for each
        public OrderModelUpdate? BaseOrder { get; set; }
        public OrderModelUpdate? TakeProfitOrder { get; set; }

        public decimal IntendedPrice { get; set; } = 0;

        public bool IsBaseOrderActive => BaseOrder != null && BaseExchangeAPI.IsOrderActive(BaseOrder.Status);
        public bool IsBaseOrderFilled => BaseOrder?.Status == OrderStatus.Filled;

        public bool IsBaseOrderActiveOrFilled() => IsBaseOrderActive || IsBaseOrderFilled;

        public bool IsTakeProfitOrderActive => TakeProfitOrder != null && BaseExchangeAPI.IsOrderActive(TakeProfitOrder.Status);
        public bool IsTakeProfitOrderFilled => TakeProfitOrder?.Status == OrderStatus.Filled;
        public bool IsTakeProfitOrderActiveOrFilled() => IsTakeProfitOrderActive || IsTakeProfitOrderFilled;

        public OrderPair_New(string name, bool isLong, CurrencyPair tradingPair, FeeConfig feeRate, HedgeGridStrategyConfig strategyConfig, LogManager log, bool IsInitialPair = false)
        {
            _name = name;
            IsLong = isLong;
            _isInitialPair = IsInitialPair;
            _tradingPair = tradingPair;
            _feeRate = feeRate ?? throw new ArgumentNullException(nameof(feeRate));
            _log = log ?? throw new ArgumentNullException(nameof(log));
            _strategyConfig = strategyConfig ?? throw new ArgumentNullException(nameof(strategyConfig));
            _log.Information($"OrderPair '{Name}' (IsLong: {IsLong}) created for {_tradingPair.Symbol}.");
        }

        public bool IsThisOrder(string? clientId = null, string? Id = null)
        {
            // Prioritize ClientID if available and matches
            if (!string.IsNullOrEmpty(clientId) && ((BaseOrder?.ClientOrderId == clientId) || (TakeProfitOrder?.ClientOrderId == clientId)))
            {
                return true;
            }
            // Fallback to OrderID if ClientID didn't match or was not provided
            if (!string.IsNullOrEmpty(Id) && ((BaseOrder?.OrderId == Id) || (TakeProfitOrder?.OrderId == Id)))
            {
                return true;
            }
            return false;
        }

        public bool IsManagingOrder(string? clientOrderId, string? orderId)
        {
            if (!string.IsNullOrEmpty(clientOrderId))
            {
                if (BaseOrder?.ClientOrderId == clientOrderId && !string.IsNullOrEmpty(BaseOrder.ClientOrderId)) return true;
                if (TakeProfitOrder?.ClientOrderId == clientOrderId && !string.IsNullOrEmpty(TakeProfitOrder.ClientOrderId)) return true;
            }
            if (!string.IsNullOrEmpty(orderId))
            {
                if (BaseOrder?.OrderId == orderId && !string.IsNullOrEmpty(BaseOrder.OrderId)) return true;
                if (TakeProfitOrder?.OrderId == orderId && !string.IsNullOrEmpty(TakeProfitOrder.OrderId)) return true;
            }
            return false;
        }

        public FuturesOrderRequest? RequestBaseOrderPlacement()
        {
            bool canPlace = BaseOrder == null ||
                BaseOrder.Status == OrderStatus.Rejected ||
                BaseOrder.Status == OrderStatus.Cancelled;

            if (!canPlace)
            {
                _log.Information($"[{Name}][BASEREQ]: Base Order placement skipped. Current BaseOrder status: {BaseOrder?.Status}. Cannot place in this state.");
                return null;
            }

            _log.Debug($"[{Name}][BASEREQ]: Creating BaseOrder placement request.");

            var requestOrderType = _isInitialPair ? _strategyConfig.BaseOrderType : OrderType.Limit;
            decimal? requestPrice = null;
            if (requestOrderType == OrderType.Limit)
            {
                decimal priceAdjustment = _strategyConfig.BaseOrderLimitPriceAdjustment;
#if DEBUG
                priceAdjustment = 0m;
#else
                if (_strategyConfig.BaseOrderLimitPriceAdjustment > 0m)
                {
                    var random = new Random();
                    priceAdjustment = (decimal)random.NextDouble() * _strategyConfig.BaseOrderLimitPriceAdjustment;
                } else { priceAdjustment = 0m; }
#endif
                requestPrice = IsLong ? IntendedPrice + priceAdjustment : IntendedPrice - priceAdjustment;
                requestPrice = _tradingPair.RoundQuoteAmount(requestPrice.Value);
            }
            var requestTimeInForce = _isInitialPair ? _strategyConfig.BaseOrderTimeInForce : TimeInForce.PostOnly;

            var baseRequest = new FuturesOrderRequest()
            {
                ClientId = Guid.NewGuid().ToShortId(),
                IsBuy = IsLong,
                Amount = _tradingPair.RoundBaseAmount(_strategyConfig.PositionSizePerStep),
                OrderType = requestOrderType,
                Price = requestPrice,
                TimeInForce = requestTimeInForce,
                IsReduceOnly = false,
                PositionDirection = IsLong ? PositionDirection.Buy : PositionDirection.Sell
            };

            // Set a temporary placeholder. The caller (HGS) will update this with the real response.
            BaseOrder = new OrderModelUpdate { ClientOrderId = baseRequest.ClientId, Status = OrderStatus.Created, Symbol = _tradingPair.Symbol, Side = IsLong ? OrderSide.Buy : OrderSide.Sell, OrderType = baseRequest.OrderType, Quantity = baseRequest.Amount, Price = baseRequest.Price };

            _log.Information($"[{Name}][BASEREQ]: Created Base order request (Cloid: {baseRequest.ClientId}) @ {baseRequest.Price?.ToString("F2") ?? "Market"}.");
            return baseRequest;
        }

        public FuturesOrderRequest? RequestTakeProfitOrderPlacement()
        {
            bool canPlace = TakeProfitOrder == null ||
                TakeProfitOrder.Status == OrderStatus.Rejected ||
                TakeProfitOrder.Status == OrderStatus.Cancelled;

            if (!canPlace)
            {
                _log.Debug($"[{Name}][TPREQ]: Take Profit Order placement skipped. Current TP Order status: {TakeProfitOrder?.Status}. Cannot place in this state.");
                return null;
            }

            if (!IsBaseOrderFilled)
            {
                _log.Warning($"[{Name}][TPREQ]: Cannot request Take Profit because Base order is not filled.");
                return null;
            }

            var takeProfitPrice = CalculateTakeProfitPrice();
            if (takeProfitPrice == null)
            {
                _log.Error($"[{Name}][TPREQ]: Cannot create Take Profit order request. Calculated TP price is null (Base order avg px: {BaseOrder?.AveragePrice}).");
                return null;
            }

            _log.Information($"[{Name}][TPREQ]: Creating TakeProfitOrder placement request.");

            var requestOrderType = OrderType.Limit;
            var requestTimeInForce = TimeInForce.PostOnly;
            var requestPrice = requestOrderType == OrderType.Limit ? takeProfitPrice : (decimal?)null;
            var requestAmount = BaseOrder.QuantityFilled!.Value;

            var tpRequest = new FuturesOrderRequest()
            {
                ClientId = Guid.NewGuid().ToShortId(),
                IsBuy = !IsLong,
                Amount = _tradingPair.RoundBaseAmount(requestAmount),
                OrderType = requestOrderType,
                Price = requestPrice,
                TimeInForce = requestTimeInForce,
                IsReduceOnly = true,
                PositionDirection = IsLong ? PositionDirection.Buy : PositionDirection.Sell
            };

            TakeProfitOrder = new OrderModelUpdate { ClientOrderId = tpRequest.ClientId, Status = OrderStatus.Created, Symbol = _tradingPair.Symbol, Side = !IsLong ? OrderSide.Buy : OrderSide.Sell, OrderType = tpRequest.OrderType, Quantity = tpRequest.Amount, Price = tpRequest.Price };

            _log.Information($"[{Name}][TPREQ]: Created Take Profit order request (Cloid: {tpRequest.ClientId}) for price {tpRequest.Price?.ToString("F2") ?? "Market"}.");
            return tpRequest;
        }

        public (Fee? calculated, Fee? reported) GetCurrentFees()
        {
            Fee? calculatedFee = null;
            Fee? reportedFee = null;

            void ProcessOrder(OrderModelUpdate? order)
            {
                if (order?.Status != OrderStatus.Filled || !order.AveragePrice.HasValue || !order.QuantityFilled.HasValue || order.QuantityFilled.Value <= 0)
                {
                    return;
                }

                // Calculated fee
                bool isMaker = order.OrderType == OrderType.Limit || order.TimeInForce == TimeInForce.PostOnly;
                decimal feeRate = _feeRate.GetFuturesFee(isMaker);
                decimal feeAmount = order.QuantityFilled.Value * order.AveragePrice.Value * feeRate;
                var calculatedFeeForOrder = new Fee(new CurrencyAmount(_tradingPair.BaseCoin, 0m), new CurrencyAmount(_tradingPair.QuoteCoin, feeAmount)).RoundTo(_tradingPair);

                if (calculatedFee == null) calculatedFee = new Fee(_tradingPair);
                calculatedFee += calculatedFeeForOrder;

                // Reported fee
                var reportedFeeForOrder = order.ExecutedFee ?? new Fee(_tradingPair);
                if (reportedFeeForOrder.Quote.Amount < 0)
                {
                    reportedFeeForOrder = new Fee(reportedFeeForOrder.Base, new CurrencyAmount(reportedFeeForOrder.Quote.Currency, Math.Abs(reportedFeeForOrder.Quote.Amount)));
                }
                if (reportedFeeForOrder.Base.Amount < 0)
                {
                    reportedFeeForOrder = new Fee(new CurrencyAmount(reportedFeeForOrder.Base.Currency, Math.Abs(reportedFeeForOrder.Base.Amount)), reportedFeeForOrder.Quote);
                }

                if (reportedFee == null) reportedFee = new Fee(_tradingPair);
                reportedFee += reportedFeeForOrder;
            }

            ProcessOrder(BaseOrder);
            ProcessOrder(TakeProfitOrder);

            return (calculatedFee, reportedFee);
        }

        public (decimal? calculated, decimal? reported) GetCurrentPnL()
        {
            if (BaseOrder?.Status != OrderStatus.Filled || !(BaseOrder?.AveragePrice.HasValue ?? false) || !(BaseOrder?.QuantityFilled.HasValue ?? false))
            {
                return (null, null);
            }
            if (TakeProfitOrder?.Status != OrderStatus.Filled || !(TakeProfitOrder?.AveragePrice.HasValue ?? false) || !(TakeProfitOrder?.QuantityFilled.HasValue ?? false))
            {
                return (null, null);
            }

            decimal entryPrice = BaseOrder.AveragePrice.Value;
            decimal exitPrice = TakeProfitOrder.AveragePrice.Value;
            decimal quantity = TakeProfitOrder.QuantityFilled.Value;

            decimal calculatedPnl;
            if (IsLong) // Base was Buy, TP is Sell
            {
                calculatedPnl = (exitPrice - entryPrice) * quantity;
            }
            else // Base was Sell, TP is Buy
            {
                calculatedPnl = (entryPrice - exitPrice) * quantity;
            }

            decimal? reportedPnl = TakeProfitOrder.ClosedPnl;

            return (calculatedPnl, reportedPnl);
        }

        public void ReconstructOrders(OrderModelUpdate? reconstructedBaseOrder, OrderModelUpdate? reconstructedTpOrder)
        {
            _log.Information($"OrderPair '{Name}': Reconstructing state. Base: {reconstructedBaseOrder?.ClientOrderId ?? reconstructedBaseOrder?.OrderId}, TP: {reconstructedTpOrder?.ClientOrderId ?? reconstructedTpOrder?.OrderId}");
            if (reconstructedBaseOrder == null && reconstructedTpOrder == null)
            {
                _log.Warning($"OrderPair '{Name}': Both reconstructed orders are null. Cannot reconstruct state.");
                return;
            }
            BaseOrder = reconstructedBaseOrder;
            TakeProfitOrder = reconstructedTpOrder;
        }

        public decimal? CalculateTakeProfitPrice()
        {
            if (!IsBaseOrderFilled || !(BaseOrder?.AveragePrice.HasValue ?? false))
                return null;

            var calcTakeProfitPrice = IsLong ? BaseOrder.AveragePrice.Value + _strategyConfig.ProfitTargetDistance : BaseOrder.AveragePrice.Value - _strategyConfig.ProfitTargetDistance;

            if (calcTakeProfitPrice <= 0)
            {
                return null;
            }
            return _tradingPair.RoundQuoteAmount(calcTakeProfitPrice);
        }
    }
}
