using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using MyTraderSpace.Models;
using Newtonsoft.Json;

// Note: MarketDataFileManager also has a 'simple' 'FileSaver'/ 'FileLoader'
namespace MyTraderSpace.Exchanges.Simulated
{
    public class SimulatedMarketDataConfig
    {
        public string? LoadDirectory { get; set; }
        //public string? SaveDirectory { get; set; } // probably not be meaningful

        public decimal InitialPrice { get; set; } = 100000m;
        public TimeSpan UpdateInterval { get; set; } = TimeSpan.FromMilliseconds(500);
        public decimal PriceVolatility { get; set; } = 0.002m;  // 0.2% default volatility
        public decimal SpreadPercent { get; set; } = 0.0002m;   // 0.02% default spread
        public bool SimulateFutures { get; set; } = true;       // Whether to simulate futures market data

        public SimulatedMarketDataConfig()
        {
        }
    }
}