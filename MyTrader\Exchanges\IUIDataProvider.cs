using MyTraderSpace.Models;

namespace MyTraderSpace.Exchanges
{
    /// <summary>
    /// Interface for components that can provide a structured snapshot
    /// of their state for UI display.
    /// </summary>
    public interface IUIDataProvider
    {
        /// <summary>
        /// Gets the latest snapshot of UI-relevant data.
        /// </summary>
        /// <returns>A StrategyUIData object or null if data is unavailable.</returns>
        StrategyUIData? GetUIDataSnapshot();
    }
} 