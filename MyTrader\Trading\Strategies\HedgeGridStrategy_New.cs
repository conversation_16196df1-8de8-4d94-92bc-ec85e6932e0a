using System;
using System.Linq;
using System.Threading;
using System.Threading.Channels;
using System.Threading.Tasks;
using MyTraderSpace.Exchanges;
using MyTraderSpace.Logging;
using MyTraderSpace.Models;
using MyTraderSpace.Utils;

namespace MyTraderSpace.Trading.Strategies
{
    public class HedgeGridStrategy_New : BaseStrategy, IDisposable
    {
        // --- Core State ---
        private readonly LogManager _log;
        private readonly HedgeGridStrategyConfig _strategyConfig;
        public decimal IntendedPrice { get; private set; } = 0m;

        public OrderPair_New LongSide { get; private set; }
        public OrderPair_New ShortSide { get; private set; }

        // --- Events ---
        public event Action<HedgeGridStrategy_New, OrderModelUpdate>? OrderUpdate;
        public event Action<HedgeGridStrategy_New, string>? InitialPlacementFailed;
        public event Action<HedgeGridStrategy_New>? Activated;
        public event Action<HedgeGridStrategy_New, string>? StrategyError;

        // --- Serialized Processing Pipeline ---
        private readonly Channel<IStrategyCommand> _commandChannel;
        private Task _processingTask;
        private CancellationTokenSource _cts;

        // --- Command Definition ---
        private interface IStrategyCommand { }
        private sealed record ProcessMarketDataCommand(FuturesMarketData Data) : IStrategyCommand;
        private sealed record ProcessOrderUpdateCommand(OrderModelUpdate Data) : IStrategyCommand;
        private sealed record InitializeCommand : IStrategyCommand;
        private sealed record StartCommand : IStrategyCommand;
        private sealed record StopCommand : IStrategyCommand;
        
        // --- Placement State ---
        private abstract class PlacementState
        {
            public DateTime StartTime { get; } = DateTime.UtcNow;
            public abstract string CurrentClientOrderIdForLog { get; }
        }

        private class SingleOrderPlacementState : PlacementState
        {
            public FuturesOrderRequest Request { get; }
            public string OrderDescription { get; }
            public string ClientOrderId { get; }
            public int AttemptCount { get; set; } = 1;
            public override string CurrentClientOrderIdForLog => ClientOrderId;

            public SingleOrderPlacementState(FuturesOrderRequest request, string description)
            {
                Request = request;
                OrderDescription = description;
                ClientOrderId = request.ClientId ?? throw new InvalidOperationException("Placement request must have a Client ID.");
            }
        }
        
        private class InitialPairPlacementState : PlacementState
        {
            public FuturesOrderRequest LongRequest { get; }
            public FuturesOrderRequest ShortRequest { get; }
            public int LongAttemptCount { get; set; } = 1;
            public int ShortAttemptCount { get; set; } = 1;
            public bool IsLongPlaced { get; set; } = false;
            public bool IsShortPlaced { get; set; } = false;
            public bool IsComplete => IsLongPlaced && IsShortPlaced;
            public override string CurrentClientOrderIdForLog => $"Long({LongRequest.ClientId}), Short({ShortRequest.ClientId})";

            public InitialPairPlacementState(FuturesOrderRequest longRequest, FuturesOrderRequest shortRequest)
            {
                LongRequest = longRequest;
                ShortRequest = shortRequest;
            }
        }
        private PlacementState? _activePlacementState = null;

        // --- Action Management ---
        private bool IsActionInProgress => _activePlacementState != null;
        private FuturesMarketData? _latestMarketData;
        private bool _marketDataCommandEnqueued = false;
        
        private readonly bool _isInitial;
        private bool _disposed = false;

        public HedgeGridStrategy_New(string nameId, decimal intendedPrice, BaseExchangeAPI exchangeAPI, IMarketDataService marketDataService, HedgeGridStrategyConfig config, bool isInitialBlankSlateStep = false)
            : base(nameId, exchangeAPI, marketDataService)
        {
            _isInitial = isInitialBlankSlateStep;
            _log = new LogManager($"{nameof(HedgeGridStrategy_New)}-{nameId.Split('_').LastOrDefault() ?? nameId}", config.LogLevel);
            _strategyConfig = config ?? throw new ArgumentNullException(nameof(config));
            IntendedPrice = intendedPrice;

            // Initialize OrderPairs
            LongSide = new OrderPair_New(NameId, true, ExchangeAPI.TradingPair, ExchangeAPI.Config.Fees, _strategyConfig, _log);
            ShortSide = new OrderPair_New(NameId, false, ExchangeAPI.TradingPair, ExchangeAPI.Config.Fees, _strategyConfig, _log);

            // Initialize the processing pipeline
            _cts = new CancellationTokenSource();
            _commandChannel = Channel.CreateUnbounded<IStrategyCommand>(new UnboundedChannelOptions
            {
                SingleReader = true,
                SingleWriter = false // Multiple sources can post commands
            });
            _processingTask = RunProcessingLoopAsync(_cts.Token);
            
            _log.Information($"[CONSTRUCTOR] HedgeGridStrategy_New '{NameId}' created.");
        }

        // --- Public Methods (Input Points) ---

        public void OnMarketDataUpdate(FuturesMarketData data)
        {
            // Always store the latest data.
            _latestMarketData = data;

            // If a command to process market data is already in the queue, do nothing.
            // The loop will use the _latestMarketData when it gets to it.
            if (_marketDataCommandEnqueued)
            {
                return;
            }
            
            // Enqueue the command and set the flag.
            if (_commandChannel.Writer.TryWrite(new ProcessMarketDataCommand(data)))
            {
                _marketDataCommandEnqueued = true;
            }
        }

        public void OnOrderUpdate(OrderModelUpdate update)
        {
            // Instead of processing, we enqueue a command. Non-blocking.
            _commandChannel.Writer.TryWrite(new ProcessOrderUpdateCommand(update));
        }
        
        public override Task InitializeAsync()
        {
            _log.Information($"[{NameId}] Enqueuing initialization command...");
            _commandChannel.Writer.TryWrite(new InitializeCommand());
            return Task.CompletedTask;
        }

        public override Task StartAsync()
        {
            _log.Information($"[{NameId}] Starting...");
            _commandChannel.Writer.TryWrite(new StartCommand());
            return Task.CompletedTask;
        }

        public override Task StopAsync()
        {
            _log.Information($"[{NameId}] Stopping...");
            _commandChannel.Writer.TryWrite(new StopCommand());
            return Task.CompletedTask;
        }

        // --- Core Processing Loop ---

        private async Task RunProcessingLoopAsync(CancellationToken token)
        {
            _log.Information($"[{NameId}] Processing loop started.");
            await foreach (var command in _commandChannel.Reader.ReadAllAsync(token))
            {
                try
                {
                    switch (command)
                    {
                        case ProcessMarketDataCommand cmd:
                            await HandleMarketDataAsync(cmd.Data);
                            break;
                        case ProcessOrderUpdateCommand cmd:
                            await HandleOrderUpdateAsync(cmd.Data);
                            break;
                        case InitializeCommand:
                            await HandleInitializeAsync();
                            break;
                        case StartCommand:
                            await HandleStartAsync();
                            break;
                        case StopCommand:
                            await HandleStopAsync();
                            // After a stop command is processed, we can exit the loop.
                            _commandChannel.Writer.Complete();
                            break;
                        default:
                            _log.Warning($"[{NameId}] Unknown command type received: {command.GetType().Name}");
                            break;
                    }
                }
                catch (Exception ex)
                {
                    EnterErrorState($"Unhandled exception in processing loop for command {command.GetType().Name}.", ex);
                }
            }
            _log.Information($"[{NameId}] Processing loop finished.");
        }

        // --- Command Handlers (The State Machine Logic) ---

        private async Task HandleInitializeAsync()
        {
            _log.Information($"[{NameId}][RECON]: Processing INITIALIZE command. Attempting to reconstruct state.");
            if (State != StrategyState.Initializing)
            {
                _log.Warning($"[{NameId}][RECON]: Initialize called but strategy is already in state {State}.");
                return;
            }

            try
            {
                var positionsResult = await FetchPositionsAsync();
                var orders = (await FetchOrdersAsync()).ToList();

                _log.Information($"[{NameId}][RECON]: Fetched {positionsResult.LongPositionQuantity} Long, {positionsResult.ShortPositionQuantity} Short positions and {orders.Count} active orders.");

                // --- Reconstruct Sides ---
                var (longBaseRecon, longTpRecon) = ReconstructSide(true, positionsResult.LongPosition, orders);
                LongSide.ReconstructOrders(longBaseRecon, longTpRecon);

                var (shortBaseRecon, shortTpRecon) = ReconstructSide(false, positionsResult.ShortPosition, orders);
                ShortSide.ReconstructOrders(shortBaseRecon, shortTpRecon);

                // --- Determine Intended Price ---
                DetermineIntendedPrice(longBaseRecon, shortBaseRecon);

                // --- Handle Initial Blank Slate Case ---
                if (_isInitial && IntendedPrice == 0)
                {
                    _log.Information($"[{NameId}][RECON]: Initial blank slate with IntendedPrice=0. Getting latest market data to set IntendedPrice.");
                    var latestData = _marketDataService.GetLatestData().Futures;
                    if (latestData != null && latestData.MarkPrice.HasValue && latestData.MarkPrice.Value > 0)
                    {
                        IntendedPrice = latestData.MarkPrice.Value;
                        _log.Information($"[{NameId}][RECON]: Set IntendedPrice to current MarkPrice: {IntendedPrice}");
                    }
                    else
                    {
                        EnterErrorState("Cannot get valid market data for initial blank slate.");
                        return;
                    }
                }

                LongSide.IntendedPrice = IntendedPrice;
                ShortSide.IntendedPrice = IntendedPrice;

                // --- Validate IntendedPrice ---
                if (IntendedPrice <= 0)
                {
                    EnterErrorState($"IntendedPrice is {IntendedPrice}, which is invalid.");
                    return;
                }

                State = StrategyState.Ready;
                _log.Information($"[{NameId}][RECON]: State reconstruction complete. Strategy is now READY. Final IntendedPrice: {IntendedPrice}");

                // Trigger reconciliation to handle any immediate actions needed
                await ReconcileStateAsync();
            }
            catch (Exception ex)
            {
                EnterErrorState("State reconstruction failed.", ex);
            }
        }

        private async Task HandleStartAsync()
        {
            _log.Information($"[{NameId}] Processing START command.");
            if (State == StrategyState.Ready)
            {
                State = StrategyState.Running;
                _log.Information($"[{NameId}] State changed to Running.");

                // Trigger initial reconciliation to start order placements if needed
                await ReconcileStateAsync();
            }
            else
            {
                _log.Warning($"[{NameId}] Cannot start strategy, current state is {State} (expected Ready).");
            }
        }

        private Task HandleStopAsync()
        {
            _log.Information($"[{NameId}] Processing STOP command.");
            State = StrategyState.Stopping;
            
            // This is where we would initiate consolidation (cancel orders, close positions).
            // For example: await ConsolidateStrategyAsync();

            State = StrategyState.Stopped;
            _log.Information($"[{NameId}] State changed to Stopped.");
            return Task.CompletedTask;
        }
        
        private async Task HandleMarketDataAsync(FuturesMarketData data)
        {
            // This command has been dequeued, so we can reset the flag.
            _marketDataCommandEnqueued = false;

            if (State != StrategyState.Running)
            {
                return; // Don't process market data if not running
            }
            
            if (IsActionInProgress)
            {
                // Check for placement timeout
                if (DateTime.UtcNow > _activePlacementState!.StartTime + TimeSpan.FromMilliseconds(_strategyConfig.PlacementOverallTimeoutMs))
                {
                    var timeoutMessage = $"Placement action has TIMED OUT after {_strategyConfig.PlacementOverallTimeoutMs}ms while waiting for Cloid(s) '{_activePlacementState.CurrentClientOrderIdForLog}'.";
                    EnterErrorState(timeoutMessage);
                }
                _log.Verbose($"[{NameId}] Skipping market data processing, an action is already in progress for Cloid(s) {_activePlacementState.CurrentClientOrderIdForLog}.");
                return;
            }

            // Use the most recently stored market data, not necessarily the one that triggered the command.
            var dataToProcess = _latestMarketData;
            if (dataToProcess == null)
            {
                _log.Warning($"[{NameId}] Market data handler triggered, but latest market data is null.");
                return;
            }
            
            _log.Verbose($"[{NameId}] Processing market data. MarkPrice: {dataToProcess.MarkPrice}");
            // This is where the "reconciliation loop" logic from ValidateStrategyStateAsync would go.
            await ReconcileStateAsync();
        }

        private async Task HandleOrderUpdateAsync(OrderModelUpdate update)
        {
             _log.Information($"[{NameId}] Processing order update for Cloid: {update.ClientOrderId}, Status: {update.Status}");

            if (!IsActionInProgress)
            {
                _log.Warning($"[{NameId}] Received an order update for Cloid {update.ClientOrderId} but no action is in progress. Ignoring.");
                return;
            }

            switch (_activePlacementState)
            {
                case SingleOrderPlacementState single:
                    await HandleSingleOrderUpdate(update, single);
                    break;
                case InitialPairPlacementState pair:
                    await HandleInitialPairUpdate(update, pair);
                    break;
            }
        }

        private async Task HandleSingleOrderUpdate(OrderModelUpdate update, SingleOrderPlacementState placement)
        {
            if (placement.ClientOrderId != update.ClientOrderId)
            {
                _log.Warning($"[{NameId}] Received an order update for Cloid {update.ClientOrderId} but was expecting one for {placement.ClientOrderId}. Ignoring.");
                UpdateOrderState(update);
                return;
            }

            UpdateOrderState(update);

            if (update.Status == OrderStatus.New || update.Status == OrderStatus.Untriggered || update.Status == OrderStatus.Filled)
            {
                _log.Information($"[{NameId}] Placement of {placement.OrderDescription} (Cloid: {update.ClientOrderId}) successful on attempt {placement.AttemptCount}.");

                // Calculate and log fees/PnL for completed placements
                if (update.Status == OrderStatus.Filled)
                {
                    CalculateAndLogFeesAndPnL(update, placement.OrderDescription);
                }

                _activePlacementState = null; // Unlock the strategy

                // Check if both sides are now activated
                CheckAndFireActivatedEvent();

                await ReconcileStateAsync(); // Immediately check for the next action
                return;
            }

            if (update.Status == OrderStatus.Rejected || update.Status == OrderStatus.Cancelled)
            {
                _log.Warning($"[{NameId}] Placement attempt {placement.AttemptCount} for {placement.OrderDescription} (Cloid: {update.ClientOrderId}) failed with status {update.Status}.");
                if (placement.AttemptCount >= _strategyConfig.PlacementMaxAttempts)
                {
                    EnterErrorState($"Final placement attempt for {placement.OrderDescription} failed. Max attempts reached.");
                    return;
                }
                placement.AttemptCount++;
                _log.Information($"[{NameId}] Delaying {_strategyConfig.PlacementAttemptDelayMs}ms before attempt {placement.AttemptCount}...");
                await Task.Delay(_strategyConfig.PlacementAttemptDelayMs);
                await SendPlacementAttemptAsync(placement);
            }
        }

        private async Task HandleInitialPairUpdate(OrderModelUpdate update, InitialPairPlacementState placement)
        {
            UpdateOrderState(update);

            bool isLongUpdate = update.ClientOrderId == placement.LongRequest.ClientId;
            bool isShortUpdate = update.ClientOrderId == placement.ShortRequest.ClientId;

            if (!isLongUpdate && !isShortUpdate)
            {
                _log.Warning($"[{NameId}] Received an order update for Cloid {update.ClientOrderId} that doesn't match the initial pair placement Cloid(s) {placement.CurrentClientOrderIdForLog}. Ignoring.");
                return;
            }
            
            var side = isLongUpdate ? "Long" : "Short";
            
            if (update.Status == OrderStatus.New || update.Status == OrderStatus.Untriggered || update.Status == OrderStatus.Filled)
            {
                _log.Information($"[{NameId}] Initial {side} placement successful (Cloid: {update.ClientOrderId}).");

                // Calculate and log fees/PnL for filled orders
                if (update.Status == OrderStatus.Filled)
                {
                    CalculateAndLogFeesAndPnL(update, $"Initial {side} Base");
                }

                if (isLongUpdate) placement.IsLongPlaced = true;
                if (isShortUpdate) placement.IsShortPlaced = true;
            }
            else if (update.Status == OrderStatus.Rejected || update.Status == OrderStatus.Cancelled)
            {
                _log.Warning($"[{NameId}] Initial {side} placement failed (Cloid: {update.ClientOrderId}) with status {update.Status}.");
                var attemptCount = isLongUpdate ? placement.LongAttemptCount : placement.ShortAttemptCount;
                if (attemptCount >= _strategyConfig.PlacementMaxAttempts)
                {
                    EnterErrorState($"Final placement attempt for initial {side} side failed. Max attempts reached.");
                    return;
                }
                
                // Retry only the failed side
                await Task.Delay(_strategyConfig.PlacementAttemptDelayMs);
                if (isLongUpdate)
                {
                    placement.LongAttemptCount++;
                    _log.Information($"[{NameId}] Retrying initial Long placement, attempt {placement.LongAttemptCount}.");
                    await SendPlacementAttemptAsync(new SingleOrderPlacementState(placement.LongRequest, "Initial Long Base"));
                }
                else
                {
                    placement.ShortAttemptCount++;
                    _log.Information($"[{NameId}] Retrying initial Short placement, attempt {placement.ShortAttemptCount}.");
                    await SendPlacementAttemptAsync(new SingleOrderPlacementState(placement.ShortRequest, "Initial Short Base"));
                }
                // Note: The main InitialPairPlacementState remains active until the retry resolves.
            }

            if (placement.IsComplete)
            {
                _log.Information($"[{NameId}] Both initial orders placed successfully.");
                _activePlacementState = null;

                // If this was an initial blank slate step and we're still in Initializing state,
                // we can now transition to Ready state since initial placement is complete
                if (_isInitial && State == StrategyState.Initializing)
                {
                    State = StrategyState.Ready;
                    _log.Information($"[{NameId}] Initial pair placement completed successfully. Strategy is now READY.");
                }

                // Check if both sides are now filled (activated) and fire the Activated event
                CheckAndFireActivatedEvent();

                await ReconcileStateAsync();
            }
        }

        private void UpdateOrderState(OrderModelUpdate update)
        {
            if (LongSide.IsManagingOrder(update.ClientOrderId, update.OrderId))
            {
                _log.Information($"[{NameId}] Update matches LongSide.");
                if (LongSide.BaseOrder?.ClientOrderId == update.ClientOrderId)
                    LongSide.BaseOrder = update;
                else if (LongSide.TakeProfitOrder?.ClientOrderId == update.ClientOrderId)
                    LongSide.TakeProfitOrder = update;
            }
            else if (ShortSide.IsManagingOrder(update.ClientOrderId, update.OrderId))
            {
                _log.Information($"[{NameId}] Update matches ShortSide.");
                if (ShortSide.BaseOrder?.ClientOrderId == update.ClientOrderId)
                    ShortSide.BaseOrder = update;
                else if (ShortSide.TakeProfitOrder?.ClientOrderId == update.ClientOrderId)
                    ShortSide.TakeProfitOrder = update;
            }

            // Fire the OrderUpdate event for MainStrategy to observe
            OrderUpdate?.Invoke(this, update);
        }

        /// <summary>
        /// This is the core state machine. It checks the current state of the order pairs
        /// and determines if any action (placing an order) needs to be taken.
        /// It will only execute one action at a time.
        /// </summary>
        private async Task ReconcileStateAsync()
        {
            if (IsActionInProgress) return; // Safeguard

            // --- SPECIAL: Initial Concurrent Placement ---
            bool needsLongBase = LongSide.BaseOrder == null || LongSide.BaseOrder.Status == OrderStatus.Cancelled || LongSide.BaseOrder.Status == OrderStatus.Rejected;
            bool needsShortBase = ShortSide.BaseOrder == null || ShortSide.BaseOrder.Status == OrderStatus.Cancelled || ShortSide.BaseOrder.Status == OrderStatus.Rejected;

            if (_isInitial && needsLongBase && needsShortBase)
            {
                var longRequest = LongSide.RequestBaseOrderPlacement();
                var shortRequest = ShortSide.RequestBaseOrderPlacement();
                if (longRequest != null && shortRequest != null)
                {
                    await InitiateInitialPairPlacementAsync(longRequest, shortRequest);
                    return; // Action initiated, wait for updates
                }
            }

            // --- STANDARD: Serial Placement ---
            FuturesOrderRequest? request = null;
            string? description = null;

            // --- Base Order Placement ---
            if (needsLongBase)
            {
                request = LongSide.RequestBaseOrderPlacement();
                description = "Long Base";
            }
            else if (needsShortBase)
            {
                request = ShortSide.RequestBaseOrderPlacement();
                description = "Short Base";
            }
            // --- Take Profit Order Placement ---
            else if (LongSide.IsBaseOrderFilled && !LongSide.IsTakeProfitOrderActiveOrFilled())
            {
                request = LongSide.RequestTakeProfitOrderPlacement();
                description = "Long TakeProfit";
            }
            else if (ShortSide.IsBaseOrderFilled && !ShortSide.IsTakeProfitOrderActiveOrFilled())
            {
                request = ShortSide.RequestTakeProfitOrderPlacement();
                description = "Short TakeProfit";
            }
            // --- Re-opening Logic (after a TP is filled) ---
            else if (LongSide.IsTakeProfitOrderFilled)
            {
                _log.Information($"[{NameId}] Long side TP filled. Resetting for next cycle.");
                LongSide.BaseOrder = null;
                LongSide.TakeProfitOrder = null;
                await ReconcileStateAsync(); // Re-run immediately to place new base order.
                return;
            }
            else if (ShortSide.IsTakeProfitOrderFilled)
            {
                _log.Information($"[{NameId}] Short side TP filled. Resetting for next cycle.");
                ShortSide.BaseOrder = null;
                ShortSide.TakeProfitOrder = null;
                await ReconcileStateAsync();
                return;
            }

            if (request != null && description != null)
            {
                await InitiateSingleOrderPlacementAsync(request, description);
            }
        }

        private async Task InitiateSingleOrderPlacementAsync(FuturesOrderRequest request, string orderDescription)
        {
            _log.Information($"[{NameId}] Initiating placement process for {orderDescription} (Cloid: {request.ClientId}). Locking for action.");
            _activePlacementState = new SingleOrderPlacementState(request, orderDescription);
            await SendPlacementAttemptAsync(new SingleOrderPlacementState(request, orderDescription));
        }

        private async Task InitiateInitialPairPlacementAsync(FuturesOrderRequest longRequest, FuturesOrderRequest shortRequest)
        {
            _log.Information($"[{NameId}] Initiating CONCURRENT placement for initial pair. LongCloid: {longRequest.ClientId}, ShortCloid: {shortRequest.ClientId}.");
            _activePlacementState = new InitialPairPlacementState(longRequest, shortRequest);
            
            var longPlacement = SendSinglePlacementRequestAsync(longRequest, "Initial Long Base");
            var shortPlacement = SendSinglePlacementRequestAsync(shortRequest, "Initial Short Base");
            
            await Task.WhenAll(longPlacement, shortPlacement);
        }

        private async Task SendPlacementAttemptAsync(SingleOrderPlacementState placementState)
        {
            _log.Information($"[{NameId}] Attempt {placementState.AttemptCount}/{_strategyConfig.PlacementMaxAttempts} to place {placementState.OrderDescription} order (Cloid: {placementState.ClientOrderId}).");
            await SendSinglePlacementRequestAsync(placementState.Request, placementState.OrderDescription);
        }

        private async Task SendSinglePlacementRequestAsync(FuturesOrderRequest request, string orderDescription)
        {
            try
            {
                var result = await ExchangeAPI.OpenFuturesPositionAsync(request);
                if (!result.IsSuccess)
                {
                     _log.Warning($"[{NameId}] Synchronous failure placing {orderDescription} (Cloid: {request.ClientId}): {result.Message}. Expecting a 'Rejected' order update.");
                }
            }
            catch (Exception ex)
            {
                EnterErrorState($"Exception while placing {orderDescription} order (Cloid: {request.ClientId})", ex);
            }
        }

        private (OrderModelUpdate? baseOrder, OrderModelUpdate? tpOrder) ReconstructSide(bool isLong, PositionModel? position, List<OrderModel> orders)
        {
            OrderModelUpdate? baseRecon = null;
            OrderModelUpdate? tpRecon = null;
            var sideName = isLong ? "LONG" : "SHORT";

            if (position != null && position.Quantity != 0)
            {
                _log.Information($"[{NameId}][RECON]: Found existing {sideName} position (Qty: {position.Quantity}, AvgPx: {position.AveragePrice}). Treating as filled base.");
                baseRecon = position.ToOrderModelUpdate();
                decimal reconPrice = baseRecon.AveragePrice ?? 0m;
                decimal reconQty = baseRecon.QuantityFilled ?? 0m;

                tpRecon = orders.FirstOrDefault(o =>
                    o.Side == (isLong ? OrderSide.Sell : OrderSide.Buy) &&
                    o.IsReduceOnly &&
                    o.Quantity == reconQty &&
                    IsTpOrderPlausible(o, reconPrice, isLong)
                )?.ToOrderModelUpdate();

                if (tpRecon != null)
                    _log.Information($"[{NameId}][RECON]: Matched active order {tpRecon.ClientOrderId ?? tpRecon.OrderId} as {sideName} TP.");
            }
            else
            {
                var pendingBases = orders.Where(o => o.Side == (isLong ? OrderSide.Buy : OrderSide.Sell) && !o.IsReduceOnly).ToList();
                if (pendingBases.Count == 1)
                {
                    baseRecon = pendingBases.First().ToOrderModelUpdate();
                    _log.Information($"[{NameId}][RECON]: Found PENDING {sideName} base order {baseRecon.ClientOrderId ?? baseRecon.OrderId}. Px: {baseRecon.Price}, Qty: {baseRecon.Quantity}");
                }
                else if (pendingBases.Count > 1)
                {
                    _log.Warning($"[{NameId}][RECON]: Found {pendingBases.Count} pending {sideName} base orders. Ambiguous. Will not reconstruct side from pending orders.");
                }
            }
            return (baseRecon, tpRecon);
        }

        private void DetermineIntendedPrice(OrderModelUpdate? longBase, OrderModelUpdate? shortBase)
        {
            decimal longPrice = (longBase?.Status == OrderStatus.Filled ? longBase.AveragePrice : longBase?.Price) ?? 0m;
            decimal shortPrice = (shortBase?.Status == OrderStatus.Filled ? shortBase.AveragePrice : shortBase?.Price) ?? 0m;

            if (longBase?.Status == OrderStatus.Filled && shortBase?.Status == OrderStatus.Filled && longPrice > 0 && shortPrice > 0)
            {
                IntendedPrice = (longPrice + shortPrice) / 2m;
                _log.Information($"[{NameId}][RECON]: Both sides FILLED. HGS IntendedPrice: {IntendedPrice} (from LongAvgPx: {longPrice}, ShortAvgPx: {shortPrice}).");
            }
            else if (longBase?.Status == OrderStatus.Filled && longPrice > 0)
            {
                IntendedPrice = longPrice;
                _log.Information($"[{NameId}][RECON]: Only Long side FILLED. HGS IntendedPrice: {IntendedPrice} (from LongAvgPx).");
            }
            else if (shortBase?.Status == OrderStatus.Filled && shortPrice > 0)
            {
                IntendedPrice = shortPrice;
                _log.Information($"[{NameId}][RECON]: Only Short side FILLED. HGS IntendedPrice: {IntendedPrice} (from ShortAvgPx).");
            }
            else if (longBase != null && shortBase != null && longPrice > 0 && shortPrice > 0) // Both have pending orders
            {
                IntendedPrice = (longPrice + shortPrice) / 2m;
                _log.Information($"[{NameId}][RECON]: Both sides PENDING. HGS IntendedPrice: {IntendedPrice} (avg of L_Px: {longPrice}, S_Px: {shortPrice}).");
            }
            else if (longBase != null && longPrice > 0) // Only Long pending
            {
                IntendedPrice = longPrice;
                _log.Information($"[{NameId}][RECON]: Only Long PENDING. HGS IntendedPrice: {IntendedPrice} (from LongPendingPx).");
            }
            else if (shortBase != null && shortPrice > 0) // Only Short pending
            {
                IntendedPrice = shortPrice;
                _log.Information($"[{NameId}][RECON]: Only Short PENDING. HGS IntendedPrice: {IntendedPrice} (from ShortPendingPx).");
            }
            else if (IntendedPrice > 0 && !_isInitial)
            {
                _log.Information($"[{NameId}][RECON]: No reconstructible state on exchange. Using pre-set HGS IntendedPrice: {IntendedPrice} (for new, non-blank-slate step).");
            }
            else if (_isInitial && IntendedPrice == 0)
            {
                _log.Information($"[{NameId}][RECON]: Initial Blank Slate step, no exchange state found, HGS IntendedPrice remains 0. It will be set from market data.");
            }
            else
            {
                _log.Warning($"[{NameId}][RECON]: Could not determine a definitive IntendedPrice. It remains {IntendedPrice}. This HGS might not become Ready if IntendedPrice is 0.");
            }
        }

        private bool IsTpOrderPlausible(OrderModel order, decimal baseEntryPrice, bool isBaseLong)
        {
            if (order.Price == null || baseEntryPrice <= 0) return false;
            decimal tpPrice = order.Price.Value;
            if (isBaseLong)
            {
                // TP Sell price should be above the base Buy price
                return tpPrice > baseEntryPrice * (1 - _strategyConfig.MaxInitialStepSpreadTolerancePercentage);
            }
            else
            {
                // TP Buy price should be below the base Sell price
                return tpPrice < baseEntryPrice * (1 + _strategyConfig.MaxInitialStepSpreadTolerancePercentage);
            }
        }

        private void CheckAndFireActivatedEvent()
        {
            // Check if both sides have filled base orders (activated)
            bool longSideActivated = LongSide.BaseOrder?.Status == OrderStatus.Filled;
            bool shortSideActivated = ShortSide.BaseOrder?.Status == OrderStatus.Filled;

            if (longSideActivated && shortSideActivated)
            {
                _log.Information($"[{NameId}] Both sides are now activated (base orders filled). Firing Activated event.");
                Activated?.Invoke(this);
            }
        }

        private void CalculateAndLogFeesAndPnL(OrderModelUpdate filledOrder, string orderDescription)
        {
            if (filledOrder.Status != OrderStatus.Filled ||
                !filledOrder.AveragePrice.HasValue ||
                !filledOrder.QuantityFilled.HasValue ||
                filledOrder.QuantityFilled.Value <= 0)
            {
                return;
            }

            // Calculate fees for this filled order
            bool isMaker = filledOrder.OrderType == OrderType.Limit || filledOrder.TimeInForce == TimeInForce.PostOnly;
            decimal feeRate = ExchangeAPI.Config.Fees.GetFuturesFee(isMaker);
            decimal calculatedFeeAmount = filledOrder.QuantityFilled.Value * filledOrder.AveragePrice.Value * feeRate;
            Fee calculatedFee = new Fee(new CurrencyAmount(ExchangeAPI.TradingPair.BaseCoin, 0m),
                                       new CurrencyAmount(ExchangeAPI.TradingPair.QuoteCoin, calculatedFeeAmount))
                                       .RoundTo(ExchangeAPI.TradingPair);

            // Get reported fee (exchange fees are typically negative, but we store as positive)
            Fee reportedFee = filledOrder.ExecutedFee ?? new Fee(ExchangeAPI.TradingPair);
            if (reportedFee.Quote.Amount < 0)
            {
                reportedFee = new Fee(reportedFee.Base, new CurrencyAmount(reportedFee.Quote.Currency, Math.Abs(reportedFee.Quote.Amount)));
            }
            if (reportedFee.Base.Amount < 0)
            {
                reportedFee = new Fee(new CurrencyAmount(reportedFee.Base.Currency, Math.Abs(reportedFee.Base.Amount)), reportedFee.Quote);
            }

            _log.Information($"[{NameId}] Fee for {orderDescription} (Cloid: {filledOrder.ClientOrderId}). Calc: {calculatedFee}, Rep: {reportedFee}");

            // Calculate PnL if this is a take profit order
            if (orderDescription.Contains("TP") || orderDescription.Contains("TakeProfit"))
            {
                CalculateAndLogPnLForTakeProfit(filledOrder, orderDescription);
            }
        }

        private void CalculateAndLogPnLForTakeProfit(OrderModelUpdate takeProfitOrder, string orderDescription)
        {
            // Determine which side this TP belongs to and get the corresponding base order
            OrderModelUpdate? baseOrder = null;
            bool isLongSide = false;

            if (LongSide.TakeProfitOrder?.ClientOrderId == takeProfitOrder.ClientOrderId)
            {
                baseOrder = LongSide.BaseOrder;
                isLongSide = true;
            }
            else if (ShortSide.TakeProfitOrder?.ClientOrderId == takeProfitOrder.ClientOrderId)
            {
                baseOrder = ShortSide.BaseOrder;
                isLongSide = false;
            }

            if (baseOrder?.Status != OrderStatus.Filled ||
                !baseOrder.AveragePrice.HasValue ||
                !baseOrder.QuantityFilled.HasValue ||
                !takeProfitOrder.AveragePrice.HasValue ||
                !takeProfitOrder.QuantityFilled.HasValue)
            {
                _log.Warning($"[{NameId}] Cannot calculate PnL for {orderDescription}: Base order not properly filled or missing data.");
                return;
            }

            decimal entryPrice = baseOrder.AveragePrice.Value;
            decimal exitPrice = takeProfitOrder.AveragePrice.Value;
            decimal quantity = takeProfitOrder.QuantityFilled.Value;

            decimal calculatedPnl;
            if (isLongSide) // Base was Buy, TP is Sell
            {
                calculatedPnl = (exitPrice - entryPrice) * quantity;
            }
            else // Base was Sell, TP is Buy
            {
                calculatedPnl = (entryPrice - exitPrice) * quantity;
            }

            decimal reportedPnl = takeProfitOrder.ClosedPnl ?? 0m;

            _log.Information($"[{NameId}] PnL for {orderDescription} (Cloid: {takeProfitOrder.ClientOrderId}). Calc: {calculatedPnl:F4}, Rep: {reportedPnl:F4}");
        }

        private async Task<(PositionModel? LongPosition, PositionModel? ShortPosition, decimal LongPositionQuantity, decimal ShortPositionQuantity)> FetchPositionsAsync()
        {
            var positions = await _exchangeAPI.GetPositionsAsync(Category.Linear, Symbol);
            var longPos = positions.FirstOrDefault(p => (p.Direction == PositionDirection.Buy || p.Side == Models.PositionSide.Buy) && p.Quantity != 0);
            var shortPos = positions.FirstOrDefault(p => (p.Direction == PositionDirection.Sell || p.Side == Models.PositionSide.Sell) && p.Quantity != 0);
            return (longPos, shortPos, longPos?.Quantity ?? 0, shortPos?.Quantity ?? 0);
        }

        private async Task<IEnumerable<OrderModel>> FetchOrdersAsync()
        {
            return await _exchangeAPI.GetActiveOrdersForCategoryAsync(Category.Linear, Symbol);
        }

        private void EnterErrorState(string message, Exception? ex = null)
        {
            if (ex != null)
                _log.Error(ex, $"[{NameId}] {message}. Strategy entering ERROR state and halting.");
            else
                _log.Error($"[{NameId}] {message}. Strategy entering ERROR state and halting.");

            _activePlacementState = null;
            State = StrategyState.Error;
            _commandChannel.Writer.Complete();

            // Fire the StrategyError event for MainStrategy to handle
            StrategyError?.Invoke(this, message);

            // If this was during initial placement, also fire InitialPlacementFailed
            if (_isInitial && State == StrategyState.Error)
            {
                InitialPlacementFailed?.Invoke(this, message);
            }
        }

        public void Dispose()
        {
            Dispose(true);
            GC.SuppressFinalize(this);
        }

        protected virtual void Dispose(bool disposing)
        {
            if (_disposed) return;
            if (disposing)
            {
                _log.Information($"[{NameId}] Disposing HedgeGridStrategy_New...");
                
                // Signal the processing loop to stop and complete the channel
                if (!_cts.IsCancellationRequested)
                {
                    _cts.Cancel();
                    _commandChannel.Writer.Complete();
                }

                // Wait for the processing task to finish gracefully
                _processingTask?.Wait(); // Or use a timeout
                _cts?.Dispose();
            }
            _disposed = true;
        }
    }
}
