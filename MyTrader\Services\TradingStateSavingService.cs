using System;
using System.Threading;
using System.Threading.Tasks;
using MyTraderSpace.Logging;
using MyTraderSpace.Models;

namespace MyTraderSpace.Services
{
    /// <summary>
    /// Service responsible for managing the saving of trading state at meaningful checkpoints.
    /// Implements debouncing to prevent excessive disk I/O when multiple events occur in quick succession.
    /// </summary>
    public class TradingStateSavingService : IDisposable
    {
        private readonly LogManager _log;
        private readonly TradingStateFileManager _fileManager;
        private readonly TimeSpan _debounceInterval;
        private readonly SemaphoreSlim _saveSemaphore = new SemaphoreSlim(1, 1);
        private Timer? _debounceTimer;
        private bool _saveRequested = false;
        private Func<Task<dynamic?>>? _stateProviderFunc; // ToDo / Note: Remember to replace 'dynamic' with the actual state class
        private bool _disposed = false;

        /// <summary>
        /// Initializes a new instance of the TradingStateSavingService.
        /// </summary>
        /// <param name="fileManager">The file manager to use for saving state files.</param>
        /// <param name="debounceIntervalMs">The debounce interval in milliseconds. Default is 2000ms (2 seconds).</param>
        public TradingStateSavingService(TradingStateFileManager fileManager, int debounceIntervalMs = 2000)
        {
            _log = new LogManager(nameof(TradingStateSavingService));
            _fileManager = fileManager ?? throw new ArgumentNullException(nameof(fileManager));
            _debounceInterval = TimeSpan.FromMilliseconds(debounceIntervalMs);
            _debounceTimer = new Timer(OnDebounceTimerElapsed, null, Timeout.Infinite, Timeout.Infinite);
            
            _log.Information($"TradingStateSavingService initialized with debounce interval of {_debounceInterval.TotalMilliseconds}ms.");
        }

        /// <summary>
        /// Sets the function that provides the current trading state.
        /// </summary>
        /// <param name="stateProviderFunc">A function that returns the current trading state.</param>
        public void SetStateProvider(Func<Task<dynamic?>> stateProviderFunc) // ToDo / Note: Remember to replace 'dynamic' with the actual state class
        {
            _stateProviderFunc = stateProviderFunc ?? throw new ArgumentNullException(nameof(stateProviderFunc));
            _log.Information("State provider function set.");
        }

        /// <summary>
        /// Requests a save of the current trading state.
        /// The save operation is debounced to prevent excessive disk I/O.
        /// </summary>
        /// <param name="reason">The reason for the save request, for logging purposes.</param>
        public void RequestSave(string reason)
        {
            if (_disposed)
            {
                _log.Warning("RequestSave called after disposal. Ignoring request.");
                return;
            }

            if (_stateProviderFunc == null)
            {
                _log.Warning("RequestSave called but no state provider function is set. Ignoring request.");
                return;
            }

            _log.Debug($"Save requested due to: {reason}");
            _saveRequested = true;
            
            // Reset the timer to start the debounce period
            _debounceTimer?.Change(_debounceInterval, Timeout.InfiniteTimeSpan);
        }

        /// <summary>
        /// Called when the debounce timer elapses, indicating it's time to save the state.
        /// </summary>
        private async void OnDebounceTimerElapsed(object? state)
        {
            if (!_saveRequested || _stateProviderFunc == null)
                return;

            try
            {
                await _saveSemaphore.WaitAsync();
                
                if (!_saveRequested) // Check again after acquiring the semaphore
                    return;

                _saveRequested = false;
                
                _log.Information("Debounce period elapsed. Saving trading state...");
                
                try
                {
                    var tradingState = await _stateProviderFunc();
                    if (tradingState != null)
                    {
                        await _fileManager.SaveStateAsync(tradingState);
                        _log.Information("Trading state saved successfully.");
                    }
                    else
                    {
                        _log.Warning("State provider returned null. Nothing to save.");
                    }
                }
                catch (Exception ex)
                {
                    _log.Error(ex, "Error saving trading state after debounce period.");
                }
            }
            finally
            {
                _saveSemaphore.Release();
            }
        }

        /// <summary>
        /// Forces an immediate save of the trading state, bypassing the debounce mechanism.
        /// </summary>
        /// <returns>A task representing the asynchronous save operation.</returns>
        public async Task ForceSaveAsync()
        {
            if (_disposed)
            {
                _log.Warning("ForceSaveAsync called after disposal. Ignoring request.");
                return;
            }

            if (_stateProviderFunc == null)
            {
                _log.Warning("ForceSaveAsync called but no state provider function is set. Ignoring request.");
                return;
            }

            try
            {
                await _saveSemaphore.WaitAsync();
                _saveRequested = false; // Cancel any pending debounced save
                
                _log.Information("Force saving trading state...");
                
                try
                {
                    var tradingState = await _stateProviderFunc();
                    if (tradingState != null)
                    {
                        await _fileManager.SaveStateAsync(tradingState);
                        _log.Information("Trading state force-saved successfully.");
                    }
                    else
                    {
                        _log.Warning("State provider returned null. Nothing to force-save.");
                    }
                }
                catch (Exception ex)
                {
                    _log.Error(ex, "Error force-saving trading state.");
                }
            }
            finally
            {
                _saveSemaphore.Release();
            }
        }

        public void Dispose()
        {
            if (_disposed)
                return;

            _disposed = true;
            _debounceTimer?.Dispose();
            _debounceTimer = null;
            _saveSemaphore.Dispose();
            _log.Dispose();
        }
    }
}
