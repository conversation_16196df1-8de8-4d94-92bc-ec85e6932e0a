namespace MyTraderSpace.Exchanges.Simulated
{
    public class SimulatedExchangeConfig : ExchangeConfig
    {
        // Initial balances
        public decimal InitialBaseBalance { get; set; } = 1.0m;
        public decimal InitialQuoteBalance { get; set; } = 100_000m;

        // Simulation behavior
        public bool SimulateSlippage { get; set; } = false;
        public decimal MaxSlippagePercent { get; set; } = 0.001m;  // 0.1%
        public decimal MarketOrderSlippage { get; set; } = 0.001m; // 0.1% slippage for market orders

        // Margin Settings
        public decimal MaintenanceMarginRate { get; set; } = 0.005m; // 0.5% Maintenance Margin Rate

        // Latency for simulated API calls
        public TimeSpan MinIntervalBetweenMarketTickProcessing { get; set; } = TimeSpan.Zero; // TickerProcessingDelay
        public TimeSpan ApiCallLatency { get; set; } = TimeSpan.FromMilliseconds(0);
    }
} 