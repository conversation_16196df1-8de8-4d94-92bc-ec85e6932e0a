﻿# Description of the New Architecture

## Overall architecture
It consist of main Program.cs which runs an ExchageTrader.cs main strategy runner class. 
This <PERSON><PERSON><PERSON><PERSON> runs a "main strategy" which in his own order runs multiple sub-strategies.
In the code it is called MainStrategy.cs and the sub-strategy is HedgeGridStrategy.cs
MainStrategy is a some kind of main orchestrator, managing the sub-strategies, but HedgeGridStrategy sub-strategies are also full strategies on their owm.

Each sub-strategy HedgeStrategy is initialized with an instance of IMarketDataService ticker market data provider, and a BaseExchangeAPI descendant classed - BybitExchangeAPI or SimulatedExchangeAPI - class which manages the API operations.
Both `IMarketDataService` and `BaseExchangeAPI` have an `ExchangeType` property, ensuring that strategies are built with components for the same exchange.
A single `IMarketDataService` instance for a specific exchange (e.g., Bybit) can be shared across multiple `BaseExchangeAPI` instances for that same exchange.
Actually these are sub-accounts of a main account of the same 'real' exchange like Bybit (so we have to each an apiKey and apiSecret, etc.)
These multiple `BaseExchangeAPI` instances form a "pool" from which elements can be taken out or released back. These are used to construct `HedgeGridStrategy` instances, pairing an API with the corresponding `IMarketDataService`.
This mean ExchangeTrader itself has to read a json config file, and in this way initializes and constructs this initial pool.
This ExchangeTrader pool can contain elements for multiple `ExchangeType`s.

These ExchangeTrader pool elements are passed to MainStrategy, but MainStrategy itself has a json config file to define its own variables.
For example defines the list of pool elements wishing to have from ExchangeTrader's pool list. (but other MainStrategy level parameters are also defined there)

This means MainStrategy will have a pool of BaseExchangeAPI instances, and these are used to instantiate elements of sub-strategy HedgeStrategies.
When created one HedgeGridStrategy the corresponding BaseExchangeAPI is removed from the pool, and when the HedgeGridStrategy is finished/completed/marked for removal, the BaseExchangeAPI is released back to the pool.

`MainStrategy`, the orchestrator, dynamically subscribes to market data. It listens to the `OnMarketDataUpdate` event of the `CurrentStrategy` (the HGS instance closest to the current market price). If there is no `CurrentStrategy`, it falls back to listening to a global market data feed provided by `IMarketDataService`. This ensures it always has a relevant price feed to make decisions.

Each HedgeStrategy will use a config class HedgeGridStrategyConfig.cs to define its own parameters, however the same ExhangeType's HedgeGridStrategies will use the same config parameters,
for example although multiple typed exhanges Bybit, Binance, etc. will have (ideally) similar parameters, SimulatedExchangeAPI has extra simulation-related parameters.

