﻿using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using MyTraderSpace.Exchanges;
using MyTraderSpace.Logging;
using MyTraderSpace.Models;
using MyTraderSpace.Utils;

namespace MyTraderSpace.Trading.Strategies
{
    public class HedgeGridStrategy : BaseStrategy, IDisposable
    {
        private readonly LogManager _log;
        private readonly HedgeGridStrategyConfig _strategyConfig;
        public decimal IntendedPrice { get; private set; } = 0m;
        private FuturesMarketData? _latestFuturesData;
        public FuturesMarketData? GetLatestFuturesData() => _latestFuturesData;
        private Task _processingTask = Task.CompletedTask;
        private PositionModelUpdate? _currentLongPosition;  // so far this is mostly for the UI (might be removed in the future)
        private PositionModelUpdate? _currentShortPosition; // so far this is mostly for the UI (might be removed in the future)
        public record FetchedPositionsResult(PositionModel? LongPosition, PositionModel? ShortPosition)
        {
            public bool IsEmpty => (LongPosition == null || LongPosition.Quantity == 0) && (ShortPosition == null || ShortPosition.Quantity == 0);
        }
        private const int MaxRecentErrors = 5;
        private readonly LimitedConcurrentQueue<string> _recentErrors = new LimitedConcurrentQueue<string>(MaxRecentErrors);
        private readonly object _positionLock = new object();
        private readonly object _activationStateLock = new object();
        private readonly bool _isInitial = false;
        private bool _disposed = false;

        public OrderPair LongSide { get; private set; }
        public OrderPair ShortSide { get; private set; }

        public bool IsAnyPairPlacing => (LongSide != null && LongSide.IsCurrentlyPlacingOrder) || (ShortSide != null && ShortSide.IsCurrentlyPlacingOrder); // alternatively: (LongSide?.IsCurrentlyPlacingOrder) ?? false) || (ShortSide.IsCurrentlyPlacingOrder ?? false);

        public bool IsReady => (State == StrategyState.Ready || State == StrategyState.Running) && (IntendedPrice > 0);

        /// <summary>
        /// Indicates if the strategy is valid (IntendedPrice != 0) and and not Error state. (Reconstruction was successful)
        /// </summary>
        public bool IsValid => IsReady && State != StrategyState.Error; // marks if reconstruction was successful

        /// <summary>
        /// Note: Introduced new status: IsPlaced. When the BaseOrders of the two (Short & Long) OrderPairs is placed (New, not filled, partially filled, etc.)
        /// This occurs on step-up and step-down HGS elements relative to CurrentStrategy 'frontier' strategy in MainStrategy. Only these types of elements can be 'pruned'
        /// </summary>
        public bool IsPlaced => (LongSide?.IsBaseOrderActive ?? false) && (ShortSide?.IsBaseOrderActive ?? false);

        /// <summary>
        /// A strategy is 'Activated' when *at least* one of the positions is Open (Long OR Short) ((Quantity != 0) && AveragePrice != 0) (or the could "not/should not happen" checking way: if one of the sides (LongSide OR ShortSide)'s BaseOrder is Filled (Side.IsBaseOrderFilled)) 
        /// Note: An Activated strategy doesn't mean it is IsActive() as it might not have both positions open (Long AND Short) (or both BaseOrders filled)
        /// </summary>
        public bool IsActivated => (LongSide?.IsBaseOrderFilled ?? false) || (ShortSide?.IsBaseOrderFilled ?? false);

        /// <summary>
        /// A strategy is 'Active' if it has *both* positions open (Long AND Short) or if the base order is filled on either side (in this case the corresponding position also has to be open!)
        /// Note: Only Not Active sub-strategies can be removed when the priced moved more than StepSize away (pruneds) from the "last frontier" sub-strategy price
        /// Note2: An IsActive() strategy is surely IsActivated
        /// </summary>
        /// <returns>bool</returns>
        public bool IsActive()
        {
            bool longPositionOpen = false;
            bool shortPositionOpen = false;

            lock (_positionLock)
            {
                longPositionOpen = (_currentLongPosition?.Quantity ?? 0) != 0 && (_currentLongPosition?.AveragePrice ?? 0) != 0;
                shortPositionOpen = (_currentShortPosition?.Quantity ?? 0) != 0 && (_currentShortPosition?.AveragePrice ?? 0) != 0;
            }
            if (!(longPositionOpen && shortPositionOpen))
            {
                // If not both positions are open, we can still be active if the base orders are filled (possible delay in position opening)
                longPositionOpen = LongSide.IsBaseOrderFilled;
                shortPositionOpen = ShortSide.IsBaseOrderFilled;
            }
            return longPositionOpen && shortPositionOpen; // Strict definition: both positions must be open
        }

        // Make sure we can be only once 'truly' first time filled both base orders and never again
        private bool _internal_IsWasBothBaseOrdersFilledFirstTime = false; // This is the internal flag ensuring the "first time" logic runs only once.
        public bool IsWasBothBaseOrdersFilledFirstTime { get; private set; } = false; // this can be true only once during the lifetime of the strategy! (and never false again)

        public decimal AccumulatedReportedRealizedPnL { get; private set; } = 0m;
        public decimal AccumulatedCalculatedRealizedPnL { get; private set; } = 0m;
        public decimal PeakStrategyCalculatedRealizedPnL { get; private set; } = 0m;
        public decimal PeakStrategyReportedRealizedPnL { get; private set; } = 0m;
        public decimal TroughStrategyCalculatedRealizedPnL { get; private set; } = decimal.MaxValue;
        public decimal TroughStrategyReportedRealizedPnL { get; private set; } = decimal.MaxValue;
        public Fee AccumulatedReportedFees { get; private set; } = new Fee();
        public Fee AccumulatedCalculatedFees { get; private set; } = new Fee();

        public event Action<FuturesMarketData>? OnMarketDataUpdate;
        public event Action<HedgeGridStrategy>? OnRemovalRequest;
        //public event Action<HedgeGridStrategy>? OnBothSidesCreated;
        //public event Action<HedgeGridStrategy>? OnBothSidesPlaced;
        public event Action<HedgeGridStrategy>? OnActivated; // IsActive() became true
        //public event Action<HedgeGridStrategy, OrderPair>? OnBaseOrderPairFilled;
        //public event Action<HedgeGridStrategy, OrderPair>? OnTakeProfitOrderPairFilled;
        public event Action<HedgeGridStrategy, OrderPair?, OrderResult>? OnStrategyError; // Forward errors to MainStrategy
        public event Action<HedgeGridStrategy, string>? OnInitialPlacementFailed; // string reason
        public event Action<HedgeGridStrategy, OrderPair, Fee, Fee>? OnFeeUpdated; // Forward the just received Fees to MainStrategy for global summarization
        public event Action<HedgeGridStrategy, OrderPair, decimal, decimal>? OnPnLUpdated;  // Forward the just received PnL to MainStrategy for global summarization

        /// <summary>
        /// 
        /// </summary>
        /// <param name="nameId"></param>
        /// <param name="intendedPrice"></param>
        /// <param name="exchangeAPI"></param>
        /// <param name="marketDataService"></param>
        /// <param name="config"></param>
        /// <param name="isInitialBlankSlateStep">If this is the "First Special Case" (Blank Slate) meaning the very first sub-strategy point (no previous points, and no points from reconstruction either)
        /// </param>
        /// <exception cref="ArgumentNullException"></exception>
        public HedgeGridStrategy(string nameId, decimal intendedPrice, BaseExchangeAPI exchangeAPI, IMarketDataService marketDataService, HedgeGridStrategyConfig config, bool isInitialBlankSlateStep = false)
            : base(nameId, exchangeAPI, marketDataService)
        {
            _isInitial = isInitialBlankSlateStep; // Store if this is the very first step on a blank slate
            _log = new LogManager($"{nameof(HedgeGridStrategy)}-{nameId.Split('_').LastOrDefault() ?? nameId}", config.LogLevel);
            _strategyConfig = config ?? throw new ArgumentNullException(nameof(config));
            IntendedPrice = intendedPrice; // This might be 0 if MainStrategy expects reconstruction to set it, or a not zero target price for a new step
            AccumulatedReportedFees = new Fee(ExchangeAPI.TradingPair);
            AccumulatedCalculatedFees = new Fee(ExchangeAPI.TradingPair);
            _log.Information($"[CONSTRUCTOR] HedgeGridStrategy '{NameId}' created. InitialIntendedPrice: {IntendedPrice}, IsInitialBlankSlate: {_isInitial}, Symbol: {Symbol}.");
        }

        public override async Task InitializeAsync()
        {
            // ToDo: eventually prevent double call of InitializeAsync() ... ?!
            if (State != StrategyState.Initializing)
            {
                _log.Warning($"[{NameId}] InitializeAsync called but strategy is {State}.");
                return;
            }
            _log.Information($"[INIT] Initializing HedgeGridStrategy '{NameId}'...");

            // --- Create OrderPair instances ---
            // Ensure this happens after _log and _strategyConfig are initialized, and ExchangeAPI is available.
            // NameId is the HGS NameId.
            // _isInitial is true for the initial blank slate HGS.
            LongSide = new OrderPair(NameId, true, ExchangeAPI.TradingPair, ExchangeAPI.Config.Fees, _strategyConfig, _log, _isInitial);
            ShortSide = new OrderPair(NameId, false, ExchangeAPI.TradingPair, ExchangeAPI.Config.Fees, _strategyConfig, _log, _isInitial);

            // --- Subscribe to OrderPair events ---
            SubscribeToOrderPairEvents(LongSide);
            SubscribeToOrderPairEvents(ShortSide);

            SubscribeToExchangeEvents();

            var positions = await FetchPositionsAsync();
            var orders = (await FetchOrdersAsync()).ToList();

            await ReConstructStateAsync(positions, orders); // This might change IntendedPrice

            if (IntendedPrice > 0)
            {
                _log.Information($"[{NameId}][INIT] Pre-setting OrderPair IntendedPrice to {IntendedPrice} as this is a non-initial step or blank slate with price.");
                LongSide.IntendedPrice = IntendedPrice;
                ShortSide.IntendedPrice = IntendedPrice;
                //State = StrategyState.Ready;
                //return;
            }

            // StartStepOrdersAsync() has to run all the time for initial pending BaseOrder placements
            //if (_isInitial /* && IntendedPrice == 0*/)
            //{
                _log.Information($"[{NameId}][INIT] Blank slate detected. Proceeding with initial base order placement within InitializeAsync.");
                bool placementSuccess = await StartStepOrdersAsync();
                if (!placementSuccess)
                {
                    _log.Error($"[{NameId}][INIT] Initial blank slate placement failed. Strategy will be in Error state.");
                    State = StrategyState.Error;
                    OnInitialPlacementFailed?.Invoke(this, "Initial blank slate placement failed during InitializeAsync.");
                    return; // Stop initialization
                }
                _log.Information($"[{NameId}][INIT] Blank slate initial placement initiated successfully.");
            //}

            State = StrategyState.Ready;
            _log.Information($"[{NameId}][INIT] HedgeGridStrategy initialized and Ready state. Final IntendedPrice after init/recon: {IntendedPrice}. IsInitialBlankSlate: {_isInitial}.");
        }

        private void SubscribeToOrderPairEvents(OrderPair orderPair)
        {
            ArgumentNullException.ThrowIfNull(orderPair);
            orderPair.OnOrderUpdated += HandleOrderUpdated;
            orderPair.OnOrderPlacementRequest += HandleOrderPlacementRequest;
            //orderPair.OnBaseOrderFilled += HandleBaseOrderPairFilled; // For Re-Opening
            //orderPair.OnTakeProfitOrderFilled += HandleTakeProfitOrderPairFilled;  // For Re-Opening
            //orderPair.OnActivated += HandleOrderPairActivated;
            orderPair.OnError += HandleOrderPairError;
            orderPair.OnFeeUpdated += HandleFeeUpdated;
            orderPair.OnPnLUpdated += HandlePnLUpdated;
        }

        private void UnsubscribeFromOrderPairEvents(OrderPair orderPair)
        {
            if (orderPair == null) return;
            //ArgumentNullException.ThrowIfNull(orderPair);
            orderPair.OnOrderUpdated -= HandleOrderUpdated;
            orderPair.OnOrderPlacementRequest -= HandleOrderPlacementRequest;
            //orderPair.OnBaseOrderFilled -= HandleBaseOrderPairFilled;
            //orderPair.OnTakeProfitOrderFilled -= HandleTakeProfitOrderPairFilled;
            //orderPair.OnActivated -= HandleOrderPairActivated;
            orderPair.OnError -= HandleOrderPairError;
            orderPair.OnFeeUpdated -= HandleFeeUpdated;
            orderPair.OnPnLUpdated -= HandlePnLUpdated;
        }

        private async Task<FetchedPositionsResult> FetchPositionsAsync()
        {
            try
            {
                var positions = await _exchangeAPI.GetPositionsAsync(Category.Linear, Symbol);
                var longPosition = positions.FirstOrDefault(p => (p.Direction == PositionDirection.Buy || p.Side == Models.PositionSide.Buy) && p.Quantity != 0);
                var shortPosition = positions.FirstOrDefault(p => (p.Direction == PositionDirection.Sell || p.Side == Models.PositionSide.Sell) && p.Quantity != 0);
                _log.Debug($"[FETCH POSITIONS] '{NameId}' successfully fetched positions. Long: {longPosition?.Quantity}, Short: {shortPosition?.Quantity}");
                return new FetchedPositionsResult(longPosition, shortPosition);
            }
            catch (Exception ex)
            {
                _log.Error(ex, $"[FETCH POSITIONS] '{NameId}' failed to fetch open positions from exchange.");
                throw;
            }
        }

        private async Task<IEnumerable<OrderModel>> FetchOrdersAsync()
        {
            try
            {
                var orders = await _exchangeAPI.GetActiveOrdersForCategoryAsync(Category.Linear, Symbol);
                _log.Debug($"[FETCH ORDERS] '{NameId}' successfully fetched {orders.Count()} active orders for {Symbol} from exchange.");
                return orders;
            }
            catch (Exception ex)
            {
                _log.Error(ex, $"[FETCH ORDERS] '{NameId}' failed to fetch active orders from exchange.");
                throw;
            }
        }

        private async Task ReConstructStateAsync(FetchedPositionsResult? positions, List<OrderModel>? orders)
        {
            _log.Information($"[{NameId}][RECON]: Attempting to reconstruct state. Current HGS IntendedPrice: {IntendedPrice}, IsInitial: {_isInitial}");

            OrderModelUpdate? longBaseRecon = null;
            OrderModelUpdate? longTpRecon = null;
            OrderModelUpdate? shortBaseRecon = null;
            OrderModelUpdate? shortTpRecon = null;

            decimal reconLongPrice = 0m;
            decimal reconShortPrice = 0m;
            decimal reconLongQty = 0m;
            decimal reconShortQty = 0m;

            // --- Try to Reconstruct Long Side ---
            if (positions?.LongPosition != null && positions.LongPosition.Quantity != 0)
            {
                _log.Information($"[{NameId}][RECON]: Found existing LONG position (Qty: {positions.LongPosition.Quantity}, AvgPx: {positions.LongPosition.AveragePrice}). Treating as filled base.");
                longBaseRecon = positions.LongPosition.ToOrderModelUpdate();
                reconLongPrice = longBaseRecon.AveragePrice ?? 0m;
                reconLongQty = longBaseRecon.QuantityFilled ?? 0m;

                longTpRecon = orders?.FirstOrDefault(o =>
                    o.Side == Models.OrderSide.Sell &&
                    o.IsReduceOnly &&
                    (o.PositionDirection == PositionDirection.Buy || o.PositionDirection == PositionDirection.OneWay) &&
                    o.Quantity == reconLongQty &&
                    IsTpOrderPlausible(o, reconLongPrice, true)
                )?.ToOrderModelUpdate();
                if (longTpRecon != null)
                    _log.Information($"[{NameId}][RECON]: Matched active order {longTpRecon.ClientOrderId ?? longTpRecon.OrderId} as LongSide TP.");
            }
            else
            {
                var pendingLongBases = orders?.Where(o => o.Side == Models.OrderSide.Buy && !o.IsReduceOnly && o.PositionDirection == Models.PositionDirection.Buy).ToList();
                if (pendingLongBases?.Count == 1)
                {
                    longBaseRecon = pendingLongBases.First().ToOrderModelUpdate();
                    reconLongPrice = longBaseRecon.Price ?? 0m;
                    reconLongQty = longBaseRecon.Quantity;
                    _log.Information($"[{NameId}][RECON]: Found PENDING long base order {longBaseRecon.ClientOrderId ?? longBaseRecon.OrderId}. Px: {reconLongPrice}, Qty: {reconLongQty}");
                }
                else if (pendingLongBases?.Count > 1)
                {
                    _log.Warning($"[{NameId}][RECON]: Found {pendingLongBases.Count} pending long base orders. Ambiguous. Will not reconstruct LongSide from pending orders.");
                }
            }
            LongSide.ReconstructOrders(longBaseRecon, longTpRecon);

            // --- Try to Reconstruct Short Side ---
            if (positions?.ShortPosition != null && positions.ShortPosition.Quantity != 0)
            {
                _log.Information($"[{NameId}][RECON]: Found existing SHORT position (Qty: {positions.ShortPosition.Quantity}, AvgPx: {positions.ShortPosition.AveragePrice}). Treating as filled base.");
                shortBaseRecon = positions.ShortPosition.ToOrderModelUpdate();
                reconShortPrice = shortBaseRecon.AveragePrice ?? 0m;
                reconShortQty = shortBaseRecon.QuantityFilled ?? 0m;

                shortTpRecon = orders?.FirstOrDefault(o =>
                    o.Side == Models.OrderSide.Buy &&
                    o.IsReduceOnly &&
                    (o.PositionDirection == PositionDirection.Sell || o.PositionDirection == PositionDirection.OneWay) &&
                    o.Quantity == reconShortQty &&
                     IsTpOrderPlausible(o, reconShortPrice, false)
                )?.ToOrderModelUpdate();
                if (shortTpRecon != null) _log.Information($"[{NameId}][RECON]: Matched active order {shortTpRecon.ClientOrderId ?? shortTpRecon.OrderId} as ShortSide TP.");
            }
            else
            {
                var pendingShortBases = orders?.Where(o => o.Side == Models.OrderSide.Sell && !o.IsReduceOnly && o.PositionDirection == Models.PositionDirection.Sell).ToList();
                if (pendingShortBases?.Count == 1)
                {
                    shortBaseRecon = pendingShortBases.First().ToOrderModelUpdate();
                    reconShortPrice = shortBaseRecon.Price ?? 0m;
                    reconShortQty = shortBaseRecon.Quantity;
                    _log.Information($"[{NameId}][RECON]: Found PENDING short base order {shortBaseRecon.ClientOrderId ?? shortBaseRecon.OrderId}. Px: {reconShortPrice}, Qty: {reconShortQty}");
                }
                else if (pendingShortBases?.Count > 1)
                {
                    _log.Warning($"[{NameId}][RECON]: Found {pendingShortBases.Count} pending short base orders. Ambiguous. Will not reconstruct ShortSide from pending orders.");
                }
            }
            ShortSide.ReconstructOrders(shortBaseRecon, shortTpRecon);

            // --- Determine HGS IntendedPrice --- 
            // Priority: 1. Both bases filled, 2. One base filled, 3. Both bases pending, 4. One base pending, 5. Pre-set by MainStrategy (if new step), 6. Blank slate (remains 0)
            if (longBaseRecon?.Status == OrderStatus.Filled && shortBaseRecon?.Status == OrderStatus.Filled && reconLongPrice > 0 && reconShortPrice > 0)
            {
                IntendedPrice = (reconLongPrice + reconShortPrice) / 2m;
                _log.Information($"[{NameId}][RECON]: Both sides filled. HGS IntendedPrice: {IntendedPrice} (from LongAvgPx: {reconLongPrice}, ShortAvgPx: {reconShortPrice}).");
            }
            else if (longBaseRecon?.Status == OrderStatus.Filled && reconLongPrice > 0)
            {
                IntendedPrice = reconLongPrice;
                _log.Information($"[{NameId}][RECON]: Only Long side filled. HGS IntendedPrice: {IntendedPrice} (from LongAvgPx).");
            }
            else if (shortBaseRecon?.Status == OrderStatus.Filled && reconShortPrice > 0)
            {
                IntendedPrice = reconShortPrice;
                _log.Information($"[{NameId}][RECON]: Only Short side filled. HGS IntendedPrice: {IntendedPrice} (from ShortAvgPx).");
            }
            else if (longBaseRecon != null && shortBaseRecon != null && reconLongPrice > 0 && reconShortPrice > 0) // Both have pending orders
            {
                IntendedPrice = (reconLongPrice + reconShortPrice) / 2m;
                _log.Information($"[{NameId}][RECON]: Both sides PENDING. HGS IntendedPrice: {IntendedPrice} (avg of L_Px: {reconLongPrice}, S_Px: {reconShortPrice}).");
            }
            else if (longBaseRecon != null && reconLongPrice > 0) // Only Long pending
            {
                IntendedPrice = reconLongPrice;
                _log.Information($"[{NameId}][RECON]: Only Long PENDING. HGS IntendedPrice: {IntendedPrice} (from LongPendingPx).");
            }
            else if (shortBaseRecon != null && reconShortPrice > 0) // Only Short pending
            {
                IntendedPrice = reconShortPrice;
                _log.Information($"[{NameId}][RECON]: Only Short PENDING. HGS IntendedPrice: {IntendedPrice} (from ShortPendingPx).");
            }
            else if (IntendedPrice > 0 && !_isInitial) // Was set by MainStrategy for a new (non-blank-slate) step, and no exchange state found.
            {
                _log.Information($"[{NameId}][RECON]: No reconstructible state on exchange. Using pre-set HGS IntendedPrice: {IntendedPrice} (for new, non-blank-slate step).");
            }
            else if (_isInitial && IntendedPrice == 0) // Blank slate, no state found, HGS IntendedPrice was 0 from MainStrategy.
            {
                _log.Information($"[{NameId}][RECON]: Initial Blank Slate step, no exchange state found, HGS IntendedPrice remains 0. MainStrategy/market data will drive actual price setting.");
            }
            else
            {
                _log.Warning($"[{NameId}][RECON]: Could not determine a definitive IntendedPrice from exchange state or pre-set values. HGS IntendedPrice remains {IntendedPrice}. This HGS might not become Ready if IntendedPrice is 0.");
            }

            if (IntendedPrice > 0)
            {
                LongSide.IntendedPrice = IntendedPrice;
                ShortSide.IntendedPrice = IntendedPrice;
            }
            await Task.CompletedTask;
            _log.Information($"[{NameId}][RECON] ReConstructStateAsync: Finished. Final HGS IntendedPrice: {IntendedPrice}.");
        }

        private bool IsTpOrderPlausible(OrderModel order, decimal baseEntryPrice, bool isBaseLong)
        {
            if (order.Price == null || baseEntryPrice <= 0) return false;
            decimal tpPrice = order.Price.Value;
            if (isBaseLong)
            {
                return tpPrice > baseEntryPrice * (1 - _strategyConfig.MaxInitialStepSpreadTolerancePercentage);
            }
            else
            {
                return tpPrice < baseEntryPrice * (1 + _strategyConfig.MaxInitialStepSpreadTolerancePercentage);
            }
        }

        public override async Task StartAsync()
        {
            if (State == StrategyState.Running)
            {
                _log.Warning($"[{NameId}][START] StartAsync called but already running.");
                return;
            }
            if (State != StrategyState.Ready)
            {
                _log.Error($"[{NameId}] Cannot start HedgeGridStrategy. State is {State}, expected Ready. Initialization might have failed.");
                OnInitialPlacementFailed?.Invoke(this, $"Cannot start, HGS state is {State}.");
                State = StrategyState.Error;
                return;
            }

            _log.Information($"[{NameId}][START] Starting exchange state validation:");
            if (!await ValidateExchangeState())
            {
                _log.Error($"[{NameId}][START] Exchange state validation failed. Cannot start strategy.");
                OnInitialPlacementFailed?.Invoke(this, "Exchange state validation failed.");
                State = StrategyState.Error;
                return;
            }
            _log.Information($"[{NameId}][START] Exchange state validation completed successfully");

            _log.Information($"[{NameId}][START] Starting HedgeGridStrategy (Current IntendedPrice: {IntendedPrice}, IsInitialBlankSlate: {_isInitial}). Attempting initial base order placements via StartStepOrdersAsync.");

            State = StrategyState.Running;
            _log.Information($"[{NameId}][START] HedgeGridStrategy successfully Started and is now Running (State: {State}). Awaiting order confirmations if not yet final.");

            // Double-check if we are in valid consistent state
            await ValidateStrategyStateAsync();
        }

        public override async Task StopAsync()
        {
            if (State == StrategyState.Stopped || State == StrategyState.Stopping)
            {
                _log.Warning($"[{NameId}][STOP] StopAsync called but already stopping/stopped.");
                return;
            }
            _log.Information($"[{NameId}][STOP] Stopping HedgeGridStrategy...");
            State = StrategyState.Stopping;
            UnsubscribeFromExchangeEvents();
            // OrderPair events are unsubscribed when HGS is disposed if not done before.
            // UnsubscribeFromOrderPairEvents(LongSide); // Or do it here explicitly
            // UnsubscribeFromOrderPairEvents(ShortSide);
            _log.Information($"[{NameId}][STOP] HedgeGridStrategy stopped.");
            State = StrategyState.Stopped;
            await Task.CompletedTask;
        }

        // Important Note: this is the good use and implementation. The reason is, that although we can get complete/full response from ExchangeAPI.OpenFuturesPositionAsync immediately,
        // ExchangeAPI still sends out the OrderUpdate and/or PositionUpdate events, leading us to double-result, and just confusion.
        // This means better stick always to the results received from OnOrderUpdate and/or OnPositionUpdate events
        private async void HandleOrderPlacementRequest(OrderPair sender, FuturesOrderRequest request)
        {
            if (State == StrategyState.Error || State == StrategyState.Stopping || State == StrategyState.Stopped)
            {
                _log.Warning($"[{NameId}][OPReq] Order placement request for {request.ClientId} (Pair: {sender.Name}) ignored. HGS State: {State}");
                var rejectedUpdate = CreateFailedOrderModelUpdate(request, $"Strategy in {State} state.");
                sender.HandleOrderUpdate(rejectedUpdate);
                return;
            }

            _log.Information($"[{NameId}][OPReq] Strategy handling placement request for ClientID: {request.ClientId} (Pair: {sender.Name})");
            var result = await ExchangeAPI.OpenFuturesPositionAsync(request);

            // If OpenFuturesPositionAsync itself returns an immediate, final, and unsuccessful/non-fill status,
            // we need to inform the OrderPair directly, as an exchange event might not cover this specific scenario
            // (e.g., synchronous rejection by the API method before hitting the exchange mock).
            if (!result.IsSuccess || (result.IsSuccess && BaseExchangeAPI.IsOrderInFinalState(result.Status) && result.Status != OrderStatus.Filled))
            {
                _log.Information($"[{NameId}][OPReq] Placement request for {request.ClientId} (Pair: {sender.Name}) resulted in direct final/failed status from OpenFuturesPositionAsync. IsSuccess: {result.IsSuccess}, Status: {result.Status}. Informing OrderPair.");
                var updateToRelay = CreateOrderModelUpdateFromResult(result, request);
                sender.HandleOrderUpdate(updateToRelay);
            }
            else if (result.IsSuccess && result.Status == OrderStatus.Filled)
            {
                _log.Debug($"[{NameId}][OPReq] Placement for {request.ClientId} (Pair: {sender.Name}) reported as Filled by OpenFuturesPositionAsync. Exchange event (OnOrderUpdate) is expected to update OrderPair. Status from result: {result.Status}.");
                // Rely on the ExchangeAPI.OnOrderUpdate event to propagate this fill to the OrderPair.
                // Calling sender.HandleOrderUpdate here could be redundant if the event is prompt and guaranteed.
            }
            else if (result.IsSuccess && !BaseExchangeAPI.IsOrderInFinalState(result.Status))
            {
                _log.Debug($"[{NameId}][OPReq] Placement for {request.ClientId} (Pair: {sender.Name}) resulted in active status {result.Status} from OpenFuturesPositionAsync. Exchange event (OnOrderUpdate) will provide further updates to OrderPair.");
                // Order is active (e.g., New). The ExchangeAPI.OnOrderUpdate event stream will provide subsequent updates.
            }
            // If result.IsSuccess and result.Status is a final state like Cancelled (but not Filled),
            // the first 'if' block would handle it.
        }

        // If any from the 2 OrderPairs errors out (did the re-placement attempts and/or timed out) that qualifys as error state worth stopping
        private async void HandleOrderPairError(OrderPair sender, OrderResult? errorResult)
        {
            if (errorResult == null)
                return;
            _log.Error($"[{NameId}] ({sender.Name}): Critical error reported from OrderPair: {errorResult.Message}. ClientOrderId: {errorResult.ClientOrderId}, Status: {errorResult.Status}. This often indicates persistent placement failure.");
            _recentErrors.Enqueue($"HGS:{NameId},Pair:{sender.Name},CriticalErr:{errorResult.Message}");

            // If the strategy was running and encounters a critical error from an OrderPair (like persistent placement failure),
            // transition to Error state and stop.
            if (State == StrategyState.Running)
            {
                _log.Error($"[{NameId}] Critical error in OrderPair {sender.Name} while HGS was Running. Transitioning HGS to Error state and stopping.");
                State = StrategyState.Error;
                await StopAsync(); // Stop processing, unsubscribe. Do NOT request removal.
            }
            else if (State == StrategyState.Initializing) // If error occurs during initial placement via StartStepOrdersAsync
            {
                _log.Error($"[{NameId}] Critical error in OrderPair {sender.Name} during HGS Initialization/Startup. Transitioning HGS to Error state.");
                State = StrategyState.Error;
                OnInitialPlacementFailed?.Invoke(this, $"OrderPair {sender.Name} failed placement during HGS initialization: {errorResult.Message}");
                // No need to call StopAsync() here if it's still initializing.
            }
            // Forward the error to MainStrategy
            OnStrategyError?.Invoke(this, sender, errorResult);
        }

        private void OnPositionUpdate(PositionModelUpdate update)
        {
            if (update == null || update.Symbol != ExchangeAPI.TradingPair.Symbol) return;
            _log.Information($"[{NameId}] Position Update: {update.Symbol} {update.Direction?.ToString() ?? update.Side?.ToString()} Qty:{update.Quantity} AvgPx:{update.AveragePrice} Status:{update.PositionStatus}");
            lock (_positionLock)
            {
                bool isLongPosition = (update.Direction == PositionDirection.Buy) ||
                                    (update.Direction == PositionDirection.OneWay && update.Side == Models.PositionSide.Buy) ||
                                    (update.Side == Models.PositionSide.Buy && update.Direction == null);

                bool isShortPosition = (update.Direction == PositionDirection.Sell) ||
                                     (update.Direction == PositionDirection.OneWay && update.Side == Models.PositionSide.Sell) ||
                                     (update.Side == Models.PositionSide.Sell && update.Direction == null);

                if (isLongPosition)
                {
                    _currentLongPosition = (update.Quantity != 0) ? update : null;
                    if (_currentLongPosition == null)
                        _log.Information($"[{NameId}] Long position closed or quantity zero.");
                }
                else if (isShortPosition)
                {
                    _currentShortPosition = (update.Quantity != 0) ? update : null;
                    if (_currentShortPosition == null)
                        _log.Information($"[{NameId}] Short position closed or quantity zero.");
                }
            }
            // Abandone ... we check it when we are in Ready / Running
            //CheckActivationAndFirstFill();
        }

        // This is *only* to forward the OrderUpdate to the OrderPair, no handling here!
        private void OnOrderUpdated(OrderModelUpdate update)
        {
            _log.Information($"[HGS_{NameId}] Order Update: {update.ClientOrderId} Status:{update.Status} FilledQty:{update.QuantityFilled}");

            bool shouldForward = State == StrategyState.Running || State == StrategyState.Ready || State == StrategyState.Initializing;

            if (shouldForward)
            {
                _log.Debug($"[HGS_{NameId}][ORDERUPDATE] Strategy is {State}. Forwarding update.");
                if (update.Category == Category.Linear) // Assuming HedgeGrid works with Linear futures
                {
                    // Determine which OrderPair this update belongs to
                    if (LongSide.IsThisOrder(update.ClientOrderId, update.OrderId))
                    {
                        _log.Debug($"[HGS_{NameId}][ORDERUPDATE] Forwarding order update to LongSide.");
                        LongSide.HandleOrderUpdate(update);
                    }
                    else if (ShortSide.IsThisOrder(update.ClientOrderId, update.OrderId))
                    {
                        _log.Debug($"[HGS_{NameId}][ORDERUPDATE] Forwarding order update to ShortSide.");
                        ShortSide.HandleOrderUpdate(update);
                    }
                    else
                    {
                        // This could be an old order if not cleaned up, or an unexpected update.
                        // For now, we log. If it's problematic, further logic might be needed.
                        _log.Warning($"[HGS_{NameId}][ORDERUPDATE] Received update for an order not actively managed by current LongSide/ShortSide (Cloid: {update.ClientOrderId}, OID: {update.OrderId}). It might be an old/orphaned order.");
                    }
                }
                else
                {
                    _log.Warning($"[HGS_{NameId}][ORDERUPDATE] Received order update for non-Linear category '{update.Category}'. Ignoring for HGS.");
                }
            }
            else
            {
                _log.Information($"[HGS_{NameId}][ORDERUPDATE] Order update for Cloid {update.ClientOrderId} ignored. HGS State: '{State}'. Update not forwarded.");
            }
        }

        // Handle the OrderUpdate, 'as if it would' have been originated from OrderPair - handling IS done here!
        private void HandleOrderUpdated(OrderPair sender, OrderModelUpdate update)
        {
            if (update == null || update.Symbol != ExchangeAPI.TradingPair.Symbol) return;
            // Allow updates even if not Running, e.g., during initial placement in Initializing state, or if stopping.
            if (State == StrategyState.Stopped)
            {
                _log.Debug($"[{NameId}] Order Update for {update.ClientOrderId ?? update.OrderId} (Status: {update.Status}) received but HGS state is {State}. Not processing further.");
                return;
            }

            _log.Information($"[{NameId}] Order Update from {sender.Name}: {update.ClientOrderId ?? update.OrderId} Status:{update.Status} FilledQty:{update.QuantityFilled}");

            bool handledByLong = false;
            if (LongSide != null && LongSide.IsManagingOrder(update.ClientOrderId, update.OrderId))
            {
                handledByLong = true;
            }

            bool handledByShort = false;
            if (ShortSide != null && ShortSide.IsManagingOrder(update.ClientOrderId, update.OrderId))
            {
                handledByShort = true;
            }

            if (handledByLong || handledByShort)
            {
                if (LongSide!.IsBaseOrderFilled && ShortSide!.IsBaseOrderFilled)
                {
                    if (IntendedPrice == 0 && LongSide.BaseOrder?.AveragePrice != null && ShortSide.BaseOrder?.AveragePrice != null)
                    {
                        IntendedPrice = (LongSide.BaseOrder.AveragePrice.Value + ShortSide.BaseOrder.AveragePrice.Value) / 2m;
                        if (LongSide != null) LongSide.IntendedPrice = IntendedPrice;
                        if (ShortSide != null) ShortSide.IntendedPrice = IntendedPrice;
                        _log.Warning($"[{NameId}] Both base orders filled. IntendedPrice was 0, now set to {IntendedPrice}.");
                    }
                }

                if ((State == StrategyState.Initializing || State == StrategyState.Ready) &&  // only during initial startup
                    (LongSide?.BaseOrder?.Status == OrderStatus.Untriggered || LongSide?.BaseOrder?.Status == OrderStatus.New || LongSide?.BaseOrder?.Status == OrderStatus.Filled) &&
                    (ShortSide?.BaseOrder?.Status == OrderStatus.Untriggered || ShortSide?.BaseOrder?.Status == OrderStatus.New || ShortSide?.BaseOrder?.Status == OrderStatus.Filled))
                {
                    _log.Warning($"[{NameId}] HGS State still was '{State}' state when both sides became activated. State is set to Running.");
                    State = StrategyState.Running;
                }

                //// Abandone ... we check it when we are Ready / Running
                //if (handledByLong && (ShortSide != null) && (ShortSide.IsBaseOrderFilled))
                //{
                //    CheckActivationAndFirstFill();
                //}
                //else if (handledByShort && (LongSide != null) && (LongSide.IsBaseOrderFilled))
                //{
                //    CheckActivationAndFirstFill();
                //}
            }
            else
            {
                if (State != StrategyState.Stopped && State != StrategyState.Error) // Don't log if stopped/errored as other strategies might have orders
                    _log.Debug($"[{NameId}] Order update for {update.ClientOrderId ?? update.OrderId} does not belong to LongSide or ShortSide of this HGS instance based on IsManagingOrder.");
            }
        }

        //// For Re-Opening - ValidateStrategyStateAsync does the re-openings
        //private async void HandleBaseOrderPairFilled(OrderPair sender)
        //{
        //    _log.Information($"[{NameId}] ({sender.Name}): Base order filled. Cloid: {sender.BaseOrder?.ClientOrderId}, OID: {sender.BaseOrder?.OrderId}, AvgPx: {sender.BaseOrder?.AveragePrice}");

        //    if ((sender == LongSide && LongSide.IsBaseOrderFilled) && (LongSide.IsTakeProfitOrderFilled))
        //    {
        //        _log.Information($"[{NameId}] LongSide base filled. Queuing new TP placement.");
        //        LongSide.TakeProfitOrder = null; // Signal for new TP placement for this cycle
        //        await LongSide.RequestTakeProfitOrderPlacementAsync();
        //    }
        //    else if ((sender == ShortSide && ShortSide.IsBaseOrderFilled) && (ShortSide.IsTakeProfitOrderFilled))
        //    {
        //        _log.Information($"[{NameId}] ShortSide base filled. Queuing new TP placement.");
        //        ShortSide.TakeProfitOrder = null; // Signal for new TP placement for this cycle
        //        await ShortSide.RequestTakeProfitOrderPlacementAsync();
        //    }

        //    // Note: This part could be alternatively in OnActivated()
        //    if (LongSide.IsBaseOrderFilled && ShortSide.IsBaseOrderFilled)
        //    {
        //        if (IntendedPrice == 0 && LongSide.BaseOrder?.AveragePrice != null && ShortSide.BaseOrder?.AveragePrice != null)
        //        {
        //            IntendedPrice = (LongSide.BaseOrder.AveragePrice.Value + ShortSide.BaseOrder.AveragePrice.Value) / 2m;
        //            if (LongSide != null) LongSide.IntendedPrice = IntendedPrice;
        //            if (ShortSide != null) ShortSide.IntendedPrice = IntendedPrice;
        //            _log.Warning($"[{NameId}] Both base orders filled. IntendedPrice was 0, now set to {IntendedPrice}.");
        //        }
        //    }
        //    //CheckActivationAndFirstFill();
        //    OnBaseOrderPairFilled?.Invoke(this, sender);
        //}

        //// For Re-Opening - ValidateStrategyStateAsync does the re-openings
        //private async void HandleTakeProfitOrderPairFilled(OrderPair sender)
        //{
        //    _log.Information($"[{NameId}] ({sender.Name}): TP filled. Cloid: {sender.TakeProfitOrder?.ClientOrderId}, OID: {sender.TakeProfitOrder?.OrderId}, AvgPx: {sender.TakeProfitOrder?.AveragePrice}");
        //    OnTakeProfitOrderPairFilled?.Invoke(this, sender);

        //    if (sender == LongSide && LongSide.IsTakeProfitOrderFilled)
        //    {
        //        _log.Information($"[{NameId}] LongSide TakeProfit filled. Clearing old Base and requesting new BaseOrder placement.");
        //        LongSide.BaseOrder = null; // Signal for re-placement of base order
        //        await LongSide.RequestBaseOrderPlacementAsync();
        //    }
        //    else if (sender == ShortSide && ShortSide.IsTakeProfitOrderFilled)
        //    {
        //        _log.Information($"[{NameId}] ShortSide TakeProfit filled. Clearing old Base and requesting new BaseOrder placement.");
        //        ShortSide.BaseOrder = null; // Signal for re-placement of base order
        //        await ShortSide.RequestBaseOrderPlacementAsync();
        //    }
        //    //OnTakeProfitOrderPairFilled?.Invoke(this, sender);
        //}

        //private void HandleOrderPairActivated(OrderPair sender)
        //{
        //    _log.Information($"[{NameId}] ({sender.Name}) OnActivated received from OrderPair. Checking if HGS itself is now Active.");
        //}

        private void HandleFeeUpdated(OrderPair sender, Fee calcFee, Fee reportedFee)
        {
            _log.Information($"[{NameId}] Fee Update from {sender.Name}. Calc: {calcFee}, Rep: {reportedFee}");
            AccumulatedCalculatedFees += calcFee;
            AccumulatedReportedFees += reportedFee;
            OnFeeUpdated?.Invoke(this, sender, calcFee, reportedFee);
            UpdateDrawdownAndLog();
        }

        private void HandlePnLUpdated(OrderPair sender, decimal calcPnl, decimal reportedPnl)
        {
            _log.Information($"[{NameId}] PnL Update from {sender.Name}. Calc: {calcPnl}, Rep: {reportedPnl}");
            AccumulatedCalculatedRealizedPnL += calcPnl;
            AccumulatedReportedRealizedPnL += reportedPnl;
            OnPnLUpdated?.Invoke(this, sender, calcPnl, reportedPnl);
            UpdateDrawdownAndLog();
        }

        private void UpdateDrawdownAndLog()
        {
            if (AccumulatedCalculatedRealizedPnL > PeakStrategyCalculatedRealizedPnL)
                PeakStrategyCalculatedRealizedPnL = AccumulatedCalculatedRealizedPnL;
            if (TroughStrategyCalculatedRealizedPnL == decimal.MaxValue || AccumulatedCalculatedRealizedPnL < TroughStrategyCalculatedRealizedPnL)
                TroughStrategyCalculatedRealizedPnL = AccumulatedCalculatedRealizedPnL;

            if (AccumulatedReportedRealizedPnL > PeakStrategyReportedRealizedPnL)
                PeakStrategyReportedRealizedPnL = AccumulatedReportedRealizedPnL;
            if (TroughStrategyReportedRealizedPnL == decimal.MaxValue || AccumulatedReportedRealizedPnL < TroughStrategyReportedRealizedPnL)
                TroughStrategyReportedRealizedPnL = AccumulatedReportedRealizedPnL;

            _log.Information($"[{NameId}] HGS AGGREGATE PnL: AccumCalcPnL: {AccumulatedCalculatedRealizedPnL:F4}, AccumRepPnL: {AccumulatedReportedRealizedPnL:F4}, AccumRepFees: {AccumulatedReportedFees.Quote.Amount:F4}, AccumCalcFees: {AccumulatedCalculatedFees.Quote.Amount:F4}");
        }

        private void SubscribeToExchangeEvents()
        {
            if (MarketDataService != null)
                MarketDataService.SubscribeToFuturesMarketData(ExchangeAPI.TradingPair, OnFuturesMarketDataUpdate);
            if (ExchangeAPI != null)
            {
                ExchangeAPI.OnPositionUpdate += OnPositionUpdate;
                ExchangeAPI.OnOrderUpdate += OnOrderUpdated;
            }
        }

        private void UnsubscribeFromExchangeEvents()
        {
            if (_exchangeAPI != null)
            {
                _exchangeAPI.OnPositionUpdate -= OnPositionUpdate;
                _exchangeAPI.OnOrderUpdate -= OnOrderUpdated;
            }
        }

        private void OnFuturesMarketDataUpdate(FuturesMarketData data)
        {
            if (State != StrategyState.Running || data == null)
                return;
            _latestFuturesData = data;
            OnMarketDataUpdate?.Invoke(data);

            if (IsAnyPairPlacing)
            {
                _log.Verbose($"[{NameId}] OnFuturesMarketDataUpdate: An OrderPair is actively placing orders. Skipping ProcessStrategyLevelMarketUpdateAsync.");
                return;
            }

            if (_processingTask.IsCompleted)
            {
                _processingTask = Task.Run(() => ProcessStrategyLevelMarketUpdateAsync(data));
            }
            else
            {
                _log.Verbose($"[{NameId}] OnFuturesMarketDataUpdate: Processing task is still running. Skipping this update.");
            }
        }

        private async Task ProcessStrategyLevelMarketUpdateAsync(FuturesMarketData data)
        {
            if (data == null)
            {
                _log.Warning($"[{NameId}] ProcessStrategyLevelMarketUpdateAsync: Invalid market data received. Skipping processing.");
                return;
            }

            if (IntendedPrice <= 0 && _isInitial)
            {
                decimal? newIntendedPrice = null;
                if (data.HighestBid.HasValue && data.LowestAsk.HasValue && data.HighestBid.Value > 0 && data.LowestAsk.Value > 0)
                {
                    newIntendedPrice = (data.HighestBid.Value + data.LowestAsk.Value) / 2m;
                    _log.Information($"[{NameId}][BLANK SLATE] Calculated potential IntendedPrice from Bid/Ask: {newIntendedPrice.Value}");
                }
                else if (data.MarkPrice.HasValue && data.MarkPrice.Value > 0)
                {
                    newIntendedPrice = data.MarkPrice.Value;
                    _log.Information($"[{NameId}][BLANK SLATE] Calculated potential IntendedPrice from MarkPrice (Bid/Ask unavailable): {newIntendedPrice.Value}");
                }
                else if (data.LastPrice.HasValue && data.LastPrice.Value > 0)
                {
                    newIntendedPrice = data.LastPrice.Value;
                    _log.Information($"[{NameId}][BLANK SLATE] Calculated potential IntendedPrice from LastPrice (Bid/Ask/Mark unavailable): {newIntendedPrice.Value}");
                }

                if (newIntendedPrice.HasValue && newIntendedPrice.Value > 0)
                {
                    IntendedPrice = newIntendedPrice.Value;
                    if (LongSide != null) LongSide.IntendedPrice = IntendedPrice;
                    if (ShortSide != null) ShortSide.IntendedPrice = IntendedPrice;
                    _log.Information($"[{NameId}][BLANK SLATE] IntendedPrice set from market data: {IntendedPrice}");
                }
            }

            await ValidateStrategyStateAsync();

            if (_strategyConfig.StopOnLiquidationPriceRatio > 0 && (_currentLongPosition != null || _currentShortPosition != null))
            {
                var positionToCheck = _currentLongPosition ?? _currentShortPosition;
                if (positionToCheck != null && positionToCheck.LiquidationPrice.HasValue && data.MarkPrice.HasValue && positionToCheck.LiquidationPrice > 0 && positionToCheck.AveragePrice.HasValue)
                {
                    decimal distanceToLiq = Math.Abs(data.MarkPrice.Value - positionToCheck.LiquidationPrice.Value);
                    decimal entryToLiq = Math.Abs(positionToCheck.AveragePrice.Value - positionToCheck.LiquidationPrice.Value);
                    if (entryToLiq > 0)
                    {
                        decimal ratio = distanceToLiq / entryToLiq;
                        if (ratio < (1 - _strategyConfig.StopOnLiquidationPriceRatio))
                        {
                            State = StrategyState.Error; // Mark as Errored!
                            _log.Warning($"[{NameId}] STOP TRIGGERED: Liq ratio for {positionToCheck.Direction?.ToString() ?? "N/A"} is {ratio:P2} < threshold {1 - _strategyConfig.StopOnLiquidationPriceRatio:P2}. MarkPx: {data.MarkPrice}, LiqPx: {positionToCheck.LiquidationPrice}. Stopping & requesting removal.");
                            await StopAsync();
                            OnStrategyError?.Invoke(this, null, new OrderResult() { Message = "LIQ RATIO STOP" });
                            //OnRemovalRequest?.Invoke(this); // Attention / Warning: Errored strategies remains on _strategyPool stopped preventing their BaseExchangeAPI to be re-used!
                            return;
                        }
                    }
                }
            }
            await Task.CompletedTask;
        }

        private async Task ValidateStrategyStateAsync()
        {
            //if (State != StrategyState.Ready && State != StrategyState.Running)
            //{
            //    _log.Warning($"[{NameId}][VALIDATESTATE]: Strategy not Ready/Running. State: {State}");
            //    return;
            //}
            if (State == StrategyState.Error)
                return;

            if (IsAnyPairPlacing)
            {
                _log.Debug($"[{NameId}][VALIDATESTATE]: Cannot validate/place new orders while an OrderPair is already placing.");
                return;
            }

            await StartStepOrdersAsync(); // This only does intial both Base Orders (re)construction (+ TakeProfit, but only if the BaseOrders got filled immediately being Market)

            if (State == StrategyState.Error)
                return; // Stop if StartStepOrdersAsync caused an error

            if (IsAnyPairPlacing) // No suppose to happen after StartStepOrdersAsync, since the containing RequestOrderPlacements are properly awaited asynchronous functions
            {
                _log.Debug($"[{NameId}][VALIDATESTATE] **UNEXPECTED**: An OrderPair started placing after StartStepOrdersAsync. Skipping further TP checks for now.");
                return;
            }

            if ((State == StrategyState.Initializing || State == StrategyState.Ready) &&  // only during initial startup
                (LongSide?.BaseOrder?.Status == OrderStatus.Untriggered || LongSide?.BaseOrder?.Status == OrderStatus.New || LongSide?.BaseOrder?.Status == OrderStatus.Filled) &&
                (ShortSide?.BaseOrder?.Status == OrderStatus.Untriggered || ShortSide?.BaseOrder?.Status == OrderStatus.New || ShortSide?.BaseOrder?.Status == OrderStatus.Filled))
            {
                _log.Warning($"[{NameId}] HGS State still was '{State}' state when both sides became activated. State is set to Running.");
                State = StrategyState.Running;
            }

            if (State == StrategyState.Error)
                return; // Stop if StartStepOrdersAsync caused an error

            if (IsAnyPairPlacing) // Re-check after StartStepOrdersAsync
            {
                _log.Debug($"[{NameId}][VALIDATESTATE]: An OrderPair started placing after StartStepOrdersAsync. Skipping further TP checks for now.");
                return;
            }

            PostOnlyFyPendingOrders();

            if (IsAnyPairPlacing) // Re-check after StartStepOrdersAsync
            {
                _log.Debug($"[{NameId}][VALIDATESTATE]: An OrderPair started placing after StartStepOrdersAsync. Skipping further TP checks for now.");
                return;
            }
            // Re-Opening handling
            bool tpRequestAttempted = false;

            if (LongSide != null && LongSide.IsBaseOrderFilled && (_currentLongPosition?.Quantity ?? 0) != 0 &&
                (LongSide.TakeProfitOrder == null || (!LongSide.IsTakeProfitOrderActiveOrFilled())))
            {
                _log.Information($"[{NameId}][VALIDATESTATE]: LongSide base is filled and position exists. Attempting to place LongSide TP.");
                LongSide.TakeProfitOrder = null;
                bool longTpSuccess = await LongSide.RequestTakeProfitOrderPlacementAsync();
                if (!longTpSuccess && State == StrategyState.Running) // Check if still running, as error might have occurred
                {
                    _log.Warning($"[{NameId}][VALIDATESTATE]: LongSide TP placement reported failure by OrderPair. HGS state: {State}.");
                    // HandleOrderPairError would have set state to Error and stopped if it was critical.
                }
                if (State == StrategyState.Error)
                    return; // Stop if TP placement caused an error
                if (longTpSuccess)
                    tpRequestAttempted = true;
            }

            if (IsAnyPairPlacing)
            {
                _log.Debug($"[{NameId}][VALIDATESTATE]: Long TP attempt may have started placement. Re-checking.");
                return;
            }

            if (ShortSide != null && ShortSide.IsBaseOrderFilled && (_currentShortPosition?.Quantity ?? 0) != 0 &&
                (ShortSide.TakeProfitOrder == null || (!ShortSide.IsTakeProfitOrderActiveOrFilled())))
            {
                _log.Information($"[{NameId}][VALIDATESTATE]: ShortSide base is filled and position exists. Attempting to place ShortSide TP.");
                ShortSide.TakeProfitOrder = null;
                bool shortTpSuccess = await ShortSide.RequestTakeProfitOrderPlacementAsync();
                if (!shortTpSuccess && State == StrategyState.Running)
                {
                    _log.Warning($"[{NameId}][VALIDATESTATE]: ShortSide TP placement reported failure by OrderPair. HGS state: {State}.");
                }
                if (State == StrategyState.Error)
                    return; // Stop if TP placement caused an error
                if (shortTpSuccess)
                    tpRequestAttempted = true;
            }

            if (tpRequestAttempted)
            {
                _log.Information($"[{NameId}][VALIDATESTATE]: One or more TP placement requests processed.");
            }

            if (IsAnyPairPlacing)
            {
                _log.Debug($"[{NameId}][VALIDATESTATE]: Short TP attempt may have started placement. Re-checking.");
                return;
            }
            CheckActivationAndFirstFill();
        }

        // This works only on Initializing phase, when the 2 BaseOrders needs to be placed
        public async Task<bool> StartStepOrdersAsync()
        {
            if (State != StrategyState.Initializing)
            {
                _log.Debug($"[{NameId}][STARTSTEP] Step Orders are done only in Initializing state. State is {State}. Skipping.");
                return false;
            }

            bool longSideInitiated = false;
            bool shortSideInitiated = false;

            // Attempt Long Side
            if (LongSide != null)
            {
                _log.Information($"[{NameId}][STARTSTEP]: Attempting to place/retry LongSide base order for IntendedPrice: {IntendedPrice}. Current BaseOrder: {LongSide.BaseOrder?.Status.ToString() ?? "None"}");
                if (LongSide.IsBaseOrderActiveOrFilled())
                {
                    _log.Information($"[{NameId}][STARTSTEP]: LongSide base order is already active or filled. Skipping placement.");
                    longSideInitiated = true; // Consider it initiated if already active/filled
                }
                else
                {
                    longSideInitiated = await LongSide.RequestBaseOrderPlacementAsync();
                    if (!longSideInitiated)
                    {
                        _log.Error($"[{NameId}][STARTSTEP]: LongSide base order placement initiation failed.");
                        // Don't return false immediately, try short side as well.
                        // The overall success depends on both.
                    }
                    else
                    {
                        _log.Information($"[{NameId}][STARTSTEP]: LongSide base order placement successfully initiated.");
                        if (LongSide.IsBaseOrderFilled) // we need to do this here, because here we are decoupled from RequestBaseOrderPlacement, but in HandleBaseOrderPairFilled event we are not, and it is prevented to be placed the TakeProfitOrder part
                        {
                            if ((LongSide.TakeProfitOrder == null) || (!LongSide.IsTakeProfitOrderActiveOrFilled()))
                                longSideInitiated = await LongSide.RequestTakeProfitOrderPlacementAsync();
                            if (longSideInitiated)
                                _log.Information($"[{NameId}][STARTSTEP]: Long TakeProfit Order successfully placed after Filled BaseOrder");
                            else
                                _log.Error($"[{NameId}][STARTSTEP]: FAILED to place Long TakeProfit Order after Filled BaseOrder");
                        }
                    }
                }
            }
            else
            {
                _log.Error($"[{NameId}][STARTSTEP]: LongSide OrderPair is null. Cannot place order.");
                // longSideInitiated remains false
            }

            // Attempt Short Side
            if (ShortSide != null)
            {
                _log.Information($"[{NameId}][STARTSTEP]: Attempting to place/retry ShortSide base order for IntendedPrice: {IntendedPrice}. Current BaseOrder: {ShortSide.BaseOrder?.Status.ToString() ?? "None"}");
                if (ShortSide.IsBaseOrderActiveOrFilled())
                {
                    _log.Information($"[{NameId}][STARTSTEP]: ShortSide base order is already active or filled. Skipping placement.");
                    shortSideInitiated = true; // Consider it initiated if already active/filled
                }
                else
                {
                    shortSideInitiated = await ShortSide.RequestBaseOrderPlacementAsync();
                    if (!shortSideInitiated)
                    {
                        _log.Error($"[{NameId}][STARTSTEP]: ShortSide base order placement initiation failed.");
                    }
                    else
                    {
                        _log.Information($"[{NameId}][STARTSTEP]: ShortSide base order placement successfully initiated.");
                        if (ShortSide.IsBaseOrderFilled) // we need to do this here, because here we are decoupled from RequestBaseOrderPlacement, but in HandleBaseOrderPairFilled event we are not, and it is prevented to be placed the TakeProfitOrder part
                        {
                            if ((ShortSide.TakeProfitOrder == null) || (!ShortSide.IsTakeProfitOrderActiveOrFilled()))
                                shortSideInitiated = await ShortSide.RequestTakeProfitOrderPlacementAsync();
                            if (shortSideInitiated)
                                _log.Information($"[{NameId}][STARTSTEP]: Short TakeProfit Order successfully placed after Filled BaseOrder");
                            else
                                _log.Error($"[{NameId}][STARTSTEP]: FAILED to place Short TakeProfit Order after Filled BaseOrder");
                        }
                    }
                }
            }
            else
            {
                _log.Error($"[{NameId}][STARTSTEP]: ShortSide OrderPair is null. Cannot place order.");
                // shortSideInitiated remains false
            }

            bool bothInitiated = longSideInitiated && shortSideInitiated;

            if (bothInitiated)
            {
                _log.Information($"[{NameId}][STARTSTEP] Both LongSide and ShortSide base order placements initiated successfully.");
                // Check if they are already placed/active to fire OnBothSidesPlaced
                // This event is crucial for MainStrategy to know the HGS is "live"
                if ((LongSide?.IsBaseOrderActiveOrFilled() ?? false) && (ShortSide?.IsBaseOrderActiveOrFilled() ?? false))
                {
                    if ((State == StrategyState.Ready) || (State == StrategyState.Ready)) // Typically during initial startup, but might be not just
                    {
                        _log.Information($"[{NameId}][STARTSTEP] Both base orders are already active/filled after placement requests. Invoking OnBothSidesPlaced.");
                        //OnBothSidesPlaced?.Invoke(this);
                        
                    }
                }
            }
            else
            {
                _log.Error($"[{NameId}][STARTSTEP] Failed to initiate placement for one or both sides. Long: {longSideInitiated}, Short: {shortSideInitiated}.");
                // If placement failed, HGS might go into an error state via OrderPair error events.
                State = StrategyState.Error;
            }
            return bothInitiated; // Return true if both were successfully *initiated*
        }

        private async void PostOnlyFyPendingOrders()
        {
            if (!_strategyConfig.AutoPostOnlyFyOrders || (State != StrategyState.Running && State != StrategyState.Ready)) // Allow during Ready as well
            {
                return;
            }

            if (IsAnyPairPlacing)
            {
                _log.Verbose($"[{NameId}][POSTONLYFY] Skipping because an OrderPair is currently placing an order.");
                return;
            }

            var currentMarketData = _latestFuturesData;
            if (currentMarketData == null || !currentMarketData.HighestBid.HasValue || !currentMarketData.LowestAsk.HasValue || currentMarketData.HighestBid.Value <= 0 || currentMarketData.LowestAsk.Value <= 0)
            {
                _log.Verbose($"[{NameId}][POSTONLYFY] Skipping PostOnlyFy due to missing or invalid market data (Bid/Ask).");
                return;
            }

            List<Task> cancelTasks = new List<Task>();
            decimal nudgePercentage = _strategyConfig.PostOnlyRePlaceMinNudgePercentage;

            foreach (var orderPair in new[] { LongSide, ShortSide })
            {
                if (orderPair == null) continue;

                // Check Base Order
                var baseOrder = orderPair.BaseOrder;
                if ((baseOrder != null) && (baseOrder.TimeInForce != TimeInForce.PostOnly) && (baseOrder.Status != OrderStatus.Filled) && baseOrder.Price.HasValue)
                {
                    decimal orderPrice = baseOrder.Price.Value;
                    bool shouldCancel = false;
                    if (orderPair.IsLong)
                    {
                        // If lowest ask is sufficiently above our buy price, market moved favorably for PostOnly re-placement
                        if (currentMarketData.LowestAsk.Value > orderPrice * (1 + nudgePercentage))
                        {
                            _log.Information($"[{NameId}][POSTONLYFY] Long base {baseOrder.ClientOrderId ?? baseOrder.OrderId} (Px: {orderPrice}) candidate for PostOnly-fy. Market Ask: {currentMarketData.LowestAsk.Value}");
                            shouldCancel = true;
                        }
                    }
                    else // Active Base Sell Limit, non-PostOnly
                    {
                        // If highest bid is sufficiently below our sell price, market moved favorably
                        if (currentMarketData.HighestBid.Value < orderPrice * (1 - nudgePercentage))
                        {
                            _log.Information($"[{NameId}][POSTONLYFY] Short base {baseOrder.ClientOrderId ?? baseOrder.OrderId} (Px: {orderPrice}) candidate for PostOnly-fy. Market Bid: {currentMarketData.HighestBid.Value}");
                            shouldCancel = true;
                        }
                    }
                    if (shouldCancel)
                    {
                        cancelTasks.Add(ExchangeAPI.CancelOrderAsync(Symbol, baseOrder.OrderId, baseOrder.ClientOrderId));
                    }
                }

                // Check TakeProfit Order
                var tpOrder = orderPair.TakeProfitOrder;
                if ((tpOrder != null) && (tpOrder.TimeInForce != TimeInForce.PostOnly) && (tpOrder.Status != OrderStatus.Filled) && orderPair.IsBaseOrderFilled && tpOrder.Price.HasValue)
                {
                    decimal orderPrice = tpOrder.Price.Value;
                    bool shouldCancel = false;
                    if (orderPair.IsLong)
                    {
                        // If highest bid is sufficiently below our sell price
                        if (currentMarketData.HighestBid.Value < orderPrice * (1 - nudgePercentage))
                        {
                            _log.Information($"[{NameId}][POSTONLYFY] Long TP {tpOrder.ClientOrderId ?? tpOrder.OrderId} (Px: {orderPrice}) candidate for PostOnly-fy. Market Bid: {currentMarketData.HighestBid.Value}");
                            shouldCancel = true;
                        }
                    }
                    else // Active TP is Short
                    {
                        // If lowest ask is sufficiently above our buy price
                        if (currentMarketData.LowestAsk.Value > orderPrice * (1 + nudgePercentage))
                        {
                            _log.Information($"[{NameId}][POSTONLYFY] Short TP {tpOrder.ClientOrderId ?? tpOrder.OrderId} (Px: {orderPrice}) candidate for PostOnly-fy. Market Ask: {currentMarketData.LowestAsk.Value}");
                            shouldCancel = true;
                        }
                    }
                    if (shouldCancel)
                    {
                        cancelTasks.Add(ExchangeAPI.CancelOrderAsync(Symbol, tpOrder.OrderId, tpOrder.ClientOrderId));
                    }
                }
            }

            if (cancelTasks.Count != 0)
            {
                _log.Information($"[{NameId}][POSTONLYFY] Attempting to cancel {cancelTasks.Count} non-PostOnly orders to allow re-placement.");
                // We don't await these directly. OnOrderUpdate will receive the cancellation
                // and trigger ValidateStrategyStateAsync, which will then re-place the orders.
                await Task.Run(async () =>
                {
                    await Task.WhenAll(cancelTasks);
                    _log.Information($"[{NameId}][POSTONLYFY] Finished sending {cancelTasks.Count} cancel requests.");
                });
            }
        }

        private OrderModelUpdate CreateOrderModelUpdateFromResult(OrderResult result, FuturesOrderRequest request)
        {
            if (result == null)
            {
                _log.Error($"CreateOrderModelUpdateFromResult called with null result for ClientID {request.ClientId}");
                return CreateFailedOrderModelUpdate(request, "Internal error: OrderResult was null");
            }

            var update = new OrderModelUpdate
            {
                OrderId = result.OrderId,
                ClientOrderId = request.ClientId,
                Symbol = ExchangeAPI.TradingPair.Symbol,
                Status = result.Status,
                Side = request.IsBuy ? OrderSide.Buy : OrderSide.Sell,
                OrderType = request.OrderType,
                Quantity = request.Amount,
                Price = request.Price,
                QuantityFilled = result.ExecutedQuantity,
                AveragePrice = result.ExecutedPrice,
                IsReduceOnly = request.IsReduceOnly,
                PositionDirection = request.PositionDirection,
                Timestamp = result.Timestamp,
                RejectReason = result.IsSuccess ? null : result.Message
            };
            return update;
        }

        private OrderModelUpdate CreateFailedOrderModelUpdate(FuturesOrderRequest request, string message)
        {
            return new OrderModelUpdate
            {
                OrderId = string.Empty,
                ClientOrderId = request.ClientId,
                Symbol = ExchangeAPI.TradingPair.Symbol,
                Status = OrderStatus.Rejected,
                Side = request.IsBuy ? OrderSide.Buy : OrderSide.Sell,
                OrderType = request.OrderType,
                Quantity = request.Amount,
                Price = request.Price,
                IsReduceOnly = request.IsReduceOnly,
                PositionDirection = request.PositionDirection,
                Timestamp = DateTime.UtcNow,
                RejectReason = message,
                QuantityFilled = 0,
                AveragePrice = null,
                ExecutedFee = null
            };
        }

        private async Task<bool> ValidateExchangeState()
        {
            _log.Information($"[{NameId}][VALIDATE] Starting exchange state validation:");

            if (ExchangeAPI.State != ExchangeState.Ready)
            {
                _log.Error($"[{NameId}][VALIDATE] ExchangeAPI not Ready. Current state: {ExchangeAPI.State}");
                return false;
            }
            try
            {
                _log.Information($"[{NameId}][VALIDATE] Checking position mode");
                var positionMode = await ExchangeAPI.GetPositionModeAsync();
                _log.Information($"[{NameId}][VALIDATE] Current position mode: {positionMode}");

                if (positionMode != PositionMode.BothSides)
                {
                    _log.Error($"[{NameId}][VALIDATE] Exchange is not in Hedge Mode (BothSides). Current mode: {positionMode}. Please configure the exchange account.");
                    return false;
                }

                _log.Information($"[{NameId}][VALIDATE] Position mode is correctly set to BothSides (Hedge Mode)");
            }
            catch (Exception ex)
            {
                _log.Error(ex, $"[{NameId}][VALIDATE] Failed to validate exchange settings (Position Mode/Leverage).");
                return false;
            }

            _log.Information($"[{NameId}][VALIDATE] Exchange state validation completed successfully");
            return true;
        }

        public bool IsValidApiForReconstruction(FetchedPositionsResult positions, IEnumerable<OrderModel> orders)
        {
            // For now just return true, not decided how strict or loose rules to apply
            // *However* if no positions and no orders found, with that there is *surely* nothing to be constructed
            if (positions.IsEmpty && !orders.Any())
                return false;
            return true; // Simplified for now
        }

        public StrategyUIData? GetUIDataSnapshot()
        {
            var snapshot = new StrategyUIData
            {
                StrategyStatus = this.State.ToString(),
                APIStatus = this.ExchangeAPI?.State ?? ExchangeState.Stopped,
                Timestamp = DateTime.UtcNow
            };

            var latestMarketData = _marketDataService?.GetLatestData();
            var latestSpot = latestMarketData?.Spot;

            snapshot.SpotLastPrice = latestSpot?.LastPrice;
            snapshot.SpotBid = latestSpot?.HighestBid;
            snapshot.SpotAsk = latestSpot?.LowestAsk;

            snapshot.FuturesLastPrice = _latestFuturesData?.LastPrice;
            snapshot.FuturesMarkPrice = _latestFuturesData?.MarkPrice;
            snapshot.FuturesBid = _latestFuturesData?.HighestBid;
            snapshot.FuturesAsk = _latestFuturesData?.LowestAsk;
            snapshot.FuturesFundingRate = _latestFuturesData?.FundingRate;

            PositionModel? longPosSnapshot = null;
            PositionModel? shortPosSnapshot = null;
            lock (_positionLock)
            {
                longPosSnapshot = _currentLongPosition;
                shortPosSnapshot = _currentShortPosition;
            }

            snapshot.LongPositionSize = longPosSnapshot?.Quantity;
            snapshot.LongPositionEntry = longPosSnapshot?.AveragePrice;
            snapshot.ShortPositionSize = shortPosSnapshot?.Quantity;
            snapshot.ShortPositionEntry = shortPosSnapshot?.AveragePrice;

            bool isActiveStepForUI = (this.LongSide != null && this.ShortSide != null &&
                                 (this.LongSide.IsBaseOrderActive || this.LongSide.IsBaseOrderFilled ||
                                  this.ShortSide.IsBaseOrderActive || this.ShortSide.IsBaseOrderFilled));
            snapshot.OpenStepCount = isActiveStepForUI ? 1 : 0; // This HGS represents one step
            snapshot.HighestStepPrice = isActiveStepForUI ? this.IntendedPrice : (decimal?)null;
            snapshot.LowestStepPrice = isActiveStepForUI ? this.IntendedPrice : (decimal?)null;

            if (isActiveStepForUI)
            {
                Func<OrderPair?, string> getStatus = (pair) => {
                    if (pair == null) return "ERR";
                    if (pair.IsBaseOrderFilled)
                    {
                        if (pair.TakeProfitOrder == null && !pair.IsCurrentlyPlacingOrder) return "TP_P";
                        if (pair.IsCurrentlyPlacingOrder && pair.TakeProfitOrder?.Status == OrderStatus.Created) return "TP_CR";
                        if (pair.IsTakeProfitOrderActive) return "TP_A";
                        if (pair.IsTakeProfitOrderFilled) return "TP_F_CYCLE";
                        return "F_TP_UNK";
                    }
                    if (pair.IsCurrentlyPlacingOrder && pair.BaseOrder?.Status == OrderStatus.Created) return "B_CR";
                    if (pair.IsBaseOrderActive) return "B_A";
                    return "B_P";
                };
                snapshot.LastFiveStepsInfo = new List<string> {
                    $"Step @ {this.IntendedPrice:F2} L:{getStatus(this.LongSide)} S:{getStatus(this.ShortSide)}"
                };
            }
            else
            {
                snapshot.LastFiveStepsInfo = new List<string>();
            }

            var lastWalletUpdate = this.ExchangeAPI?.GetLastWalletUpdate();
            snapshot.TotalEquity = lastWalletUpdate?.TotalEquity;

            decimal? currentStepUnrealizedPnl = 0m;
            if (longPosSnapshot?.UnrealizedPnl != null) currentStepUnrealizedPnl += longPosSnapshot.UnrealizedPnl;
            if (shortPosSnapshot?.UnrealizedPnl != null) currentStepUnrealizedPnl += shortPosSnapshot.UnrealizedPnl;
            snapshot.UnrealizedPnL = (currentStepUnrealizedPnl == 0m && longPosSnapshot == null && shortPosSnapshot == null) ? (decimal?)null : currentStepUnrealizedPnl;

            snapshot.RealizedPnL = this.ExchangeAPI?.GetSessionRealizedPnl(); // This is exchange-level session PnL

            snapshot.TotalWalletBalance = lastWalletUpdate?.TotalWalletBalance;
            snapshot.TotalAvailableBalance = lastWalletUpdate?.TotalAvailableBalance;

            if (lastWalletUpdate?.Assets != null)
            {
                var usdtAsset = lastWalletUpdate.Assets.FirstOrDefault(a => a.Asset.Equals("USDT", StringComparison.OrdinalIgnoreCase));
                if (usdtAsset != null)
                {
                    snapshot.USDTWalletBalance = usdtAsset.Total;
                    snapshot.USDTAvailableBalance = usdtAsset.Available;
                }
            }

            snapshot.StrategyCalculatedRealizedPnL = this.AccumulatedCalculatedRealizedPnL;
            snapshot.PeakStrategyRealizedPnL = this.PeakStrategyCalculatedRealizedPnL;
            snapshot.TroughStrategyRealizedPnL = (this.TroughStrategyCalculatedRealizedPnL == decimal.MaxValue) ? (decimal?)null : this.TroughStrategyCalculatedRealizedPnL;
            snapshot.TotalStrategyFees = this.AccumulatedReportedFees.Quote.Amount;


            snapshot.LastErrorMessages = _recentErrors.ToList();

            return snapshot;
        }

        public async Task ConsolidateAsync()
        {
            _log.Information($"[{NameId}] Attempting to consolidate (cancel HGS orders, close HGS positions)...");
            var originalState = State; // Store original state if needed for subsequent logic
            UnsubscribeFromExchangeEvents(); // this is the 'essence' from StopAsync()
            State = StrategyState.Stopping; // Indicate it's trying to shut down

            List<Task> consolidationTasks = new List<Task>();

            // 1. Cancel known active orders for this HGS
            // We iterate through the OrderPair's orders.
            // OrderPair itself doesn't have a "cancel" method that HGS can call,
            // so HGS needs to use the ExchangeAPI directly with known order details.

            Action<OrderModelUpdate?, string> addCancelTask = (order, sideName) =>
            {
                if (order != null && BaseExchangeAPI.IsOrderActive(order.Status))
                {
                    _log.Information($"[{NameId}] Adding {sideName} order ({order.ClientOrderId ?? order.OrderId}, Status: {order.Status}) to cancellation tasks.");
                    consolidationTasks.Add(ExchangeAPI.CancelOrderAsync(Symbol, order.OrderId, order.ClientOrderId));
                }
            };

            if (LongSide != null)
            {
                addCancelTask(LongSide.BaseOrder, "LongSide Base");
                addCancelTask(LongSide.TakeProfitOrder, "LongSide TP");
            }
            if (ShortSide != null)
            {
                addCancelTask(ShortSide.BaseOrder, "ShortSide Base");
                addCancelTask(ShortSide.TakeProfitOrder, "ShortSide TP");
            }

            // 2. Close open positions associated with this HGS
            // This relies on _currentLongPosition and _currentShortPosition being accurate.
            lock (_positionLock)
            {
                if (_currentLongPosition != null && (_currentLongPosition.Quantity != 0))
                {
                    _log.Information($"[{NameId}] Adding task to close Long position (Quantity: {_currentLongPosition.Quantity}).");
                    // In Hedge Mode, a Buy position is closed by a Sell order for that direction.
                    // The ClosePositionAsync should handle placing the appropriate market order.
                    consolidationTasks.Add(ExchangeAPI.ClosePositionAsync(Symbol, PositionDirection.Buy));
                }
                if (_currentShortPosition != null && (_currentShortPosition.Quantity != 0))
                {
                    _log.Information($"[{NameId}] Adding task to close Short position (Quantity: {_currentShortPosition.Quantity}).");
                    // In Hedge Mode, a Sell position is closed by a Buy order for that direction.
                    consolidationTasks.Add(ExchangeAPI.ClosePositionAsync(Symbol, PositionDirection.Sell));
                }
            }

            if (consolidationTasks.Any())
            {
                _log.Information($"[{NameId}] Consolidating {consolidationTasks.Count} items (orders/positions)...");
                try
                {
                    // Wait for all cancellations and position close requests to be processed.
                    // The timeout should be generous enough for API calls.
                    await Task.WhenAll(consolidationTasks).WaitAsync(TimeSpan.FromSeconds(30));
                    _log.Information($"[{NameId}] Consolidation tasks submitted/completed.");
                    // Note: Success of individual tasks isn't checked here, but errors should be logged by ExchangeAPI.
                }
                catch (TimeoutException)
                {
                    _log.Error($"[{NameId}] Timeout during HGS consolidation tasks. Some orders/positions might not be closed/cancelled.");
                }
                catch (Exception ex)
                {
                    _log.Error(ex, $"[{NameId}] Exception during HGS consolidation tasks.");
                }
            }
            else
            {
                _log.Information($"[{NameId}] No active orders or open positions found for this HGS to consolidate.");
            }

            // After attempting consolidation, mark as stopped and request removal.
            State = StrategyState.Stopped;
            _log.Information($"[{NameId}] Consolidation process finished. Current HGS State: {State}. Invoking OnRemovalRequest.");
            OnRemovalRequest?.Invoke(this);
        }

        // These might become private helpers for ConsolidateAsync or removed if not used elsewhere directly.
        public async Task CancelAllOrdersAsync() // This cancels ALL orders for the symbol on the exchange
        {
            _log.Warning($"[{NameId}] CancelAllOrdersAsync called - this will cancel ALL orders for symbol {Symbol}, not just for this HGS.");
            try
            {
                await _exchangeAPI.CancelAllOrdersAsync(Symbol, Category.Linear);
            }
            catch (Exception ex)
            {
                _log.Error(ex, $"[{NameId}] Error in CancelAllOrdersAsync.");
                throw;
            }
        }

        public async Task CloseAllPostionsAsync() // This closes ALL positions for the symbol on the exchange
        {
            _log.Warning($"[{NameId}] CloseAllPostionsAsync called - this will close ALL positions for symbol {Symbol}, not just for this HGS.");
            try
            {
                await _exchangeAPI.CloseAllPositionsAsync(); // BaseExchangeAPI.CloseAllPositionsAsync doesn't take a symbol.
            }
            catch (Exception ex)
            {
                _log.Error(ex, $"[{NameId}] Error in CloseAllPostionsAsync.");
                throw;
            }
        }

        public void Dispose()
        {
            Dispose(true);
            GC.SuppressFinalize(this);
        }

        protected virtual void Dispose(bool disposing)
        {
            if (_disposed)
                return;

            if (disposing)
            {
                _log.Information($"[{NameId}] Disposing HedgeGridStrategy...");
                // Unsubscribe from OrderPair events
                if (LongSide != null)
                {
                    UnsubscribeFromOrderPairEvents(LongSide);
                    // If OrderPair itself becomes IDisposable, dispose it:
                    // (LongSide as IDisposable)?.Dispose();
                }
                if (ShortSide != null)
                {
                    UnsubscribeFromOrderPairEvents(ShortSide);
                    // (ShortSide as IDisposable)?.Dispose();
                }

                // Unsubscribe from exchange events (already handled in StopAsync, but good for belt-and-suspenders if Dispose is called independently)
                UnsubscribeFromExchangeEvents();

                // Dispose any other managed resources.
                _processingTask?.Dispose();
            }

            // Free unmanaged resources (unmanaged objects) and override a finalizer below.
            // Set large fields to null.
            _disposed = true;
            _log.Information($"[{NameId}] HedgeGridStrategy disposed.");
        }

        // Optional: Override finalizer only if you have unmanaged resources
        // ~HedgeGridStrategy()
        // {
        //     Dispose(false);
        // }

        private void CheckActivationAndFirstFill()
        {
            // This method is called whenever there's a possibility that the strategy became active,
            // to centralize activation logic and prevent race conditions.
            if (!IsActive())
            {
                return;
            }

            bool shouldFireOnActivated = false;

            lock (_activationStateLock)
            {
                if (!_internal_IsWasBothBaseOrdersFilledFirstTime)
                {
                    _internal_IsWasBothBaseOrdersFilledFirstTime = true;
                    IsWasBothBaseOrdersFilledFirstTime = true;
                    shouldFireOnActivated = true;
                    _log.Debug($"[{NameId}] HGS is active for the first time. IsWasBothBaseOrdersFilledFirstTime set to true.");
                }
                else
                {
                    // It has been active before. This is a subsequent cycle.
                    IsWasBothBaseOrdersFilledFirstTime = false;
                }
            }

            if (shouldFireOnActivated)
            {
                _log.Information($"[{NameId}] HGS IsActive() is now true. Invoking OnActivated for MainStrategy.");
                OnActivated?.Invoke(this);
            }
        }

        public void ResetFirstActivation()
        {
            _internal_IsWasBothBaseOrdersFilledFirstTime = false; // This is the internal flag ensuring the "first time" logic runs only once.
            IsWasBothBaseOrdersFilledFirstTime = false; // this can be true only once during the lifetime of the strategy! (and never false again)
        }
    }
}
