# Async/Await State Management Gotchas

## Core Issue: State Visibility Across Async Boundaries

State changes in concurrent or shared data structures might not be immediately visible across async boundaries, even with proper await usage.

### Common Problem Scenarios

1. **ConcurrentDictionary Operations**:
```csharp
// DANGEROUS:
await ConsolidateAllTrades();
if (dictionary.Count != 0)  // Might see stale data!
    throw new Exception("Should be empty!");

// SAFE:
await stateLock.WaitAsync();
try 
{
    await ConsolidateAllTrades();
    if (dictionary.Count != 0)
        throw new Exception("Should be empty!");
}
finally 
{
    stateLock.Release();
}
```

2. **Shared Static Fields**:
```csharp
public static class GlobalState 
{
    private static List<string> items = new();
    private static readonly SemaphoreSlim syncLock = new(1, 1);
    
    // DANGEROUS:
    public static async Task UpdateAsync()
    {
        await Task.Delay(100);
        items.Add("item");  // Other threads might not see this
    }

    // SAFE:
    public static async Task UpdateSafeAsync()
    {
        await syncLock.WaitAsync();
        try
        {
            await Task.Delay(100);
            items.Add("item");
        }
        finally
        {
            syncLock.Release();
        }
    }
}
```

3. **Event Handler State**:
```csharp
private volatile bool isProcessing;

// DANGEROUS:
public async Task OnEventAsync(object sender, EventArgs e)
{
    if (isProcessing) return;  // Race condition!
    isProcessing = true;
    await ProcessAsync();
    isProcessing = false;
}

// SAFE:
private readonly SemaphoreSlim eventLock = new(1, 1);
public async Task OnEventSafeAsync(object sender, EventArgs e)
{
    if (!await eventLock.WaitAsync(0)) return;
    try
    {
        await ProcessAsync();
    }
    finally
    {
        eventLock.Release();
    }
}
```

4. **Lazy Initialization**:
```csharp
private volatile object? _instance;
private readonly SemaphoreSlim initLock = new(1, 1);

// DANGEROUS:
public async Task<object> GetInstanceAsync()
{
    if (_instance == null)  // Race condition
    {
        await InitializeAsync();
    }
    return _instance;
}

// SAFE:
public async Task<object> GetInstanceSafeAsync()
{
    if (_instance != null) return _instance;
    
    await initLock.WaitAsync();
    try
    {
        if (_instance == null)
        {
            _instance = await InitializeAsync();
        }
        return _instance;
    }
    finally
    {
        initLock.Release();
    }
}
```

5. **Cached Values**:
```csharp
private decimal? cachedPrice;
private readonly SemaphoreSlim cacheLock = new(1, 1);

// DANGEROUS:
public async Task<decimal> GetPriceAsync()
{
    if (!cachedPrice.HasValue)
    {
        cachedPrice = await FetchPriceAsync();
    }
    return cachedPrice.Value;
}

// SAFE:
public async Task<decimal> GetPriceSafeAsync()
{
    await cacheLock.WaitAsync();
    try
    {
        if (!cachedPrice.HasValue)
        {
            cachedPrice = await FetchPriceAsync();
        }
        return cachedPrice.Value;
    }
    finally
    {
        cacheLock.Release();
    }
}
```

### Protection Strategies

1. **Use SemaphoreSlim**:
   - Lightweight synchronization
   - Async-friendly
   - Ensures sequential access

2. **State Verification**:
   - Always verify state in same context as changes
   - Use try/finally blocks
   - Include proper error messages

3. **Proper Scope**:
   - Keep state changes and verification together
   - Avoid checking state across method boundaries
   - Use atomic operations where possible

### Implementation Rules

1. **Always Use try/finally**:
```csharp
await syncLock.WaitAsync();
try
{
    // State changes here
    // Verification here
}
finally
{
    syncLock.Release();
}
```

2. **Avoid Nested Locks**:
   - Can lead to deadlocks
   - Increases complexity
   - Hard to verify correctness

3. **Keep Operations Atomic**:
   - Minimize time holding locks
   - Batch related changes
   - Verify state before releasing

### Testing Requirements

1. **Concurrent Access Tests**:
   - Multiple simultaneous operations
   - Verify state consistency
   - Check error conditions

2. **Stress Testing**:
   - High frequency operations
   - Resource contention scenarios
   - Recovery from failures

### Remember

> "If you're sharing state across async boundaries, you need explicit synchronization - no exceptions!"

The presence of async/await doesn't guarantee operation ordering or state visibility. Always use proper synchronization mechanisms.