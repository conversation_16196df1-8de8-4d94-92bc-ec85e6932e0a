using System.Threading;
using System.Threading.Tasks;
using MyTraderSpace.Exchanges;
using MyTraderSpace.Models;

namespace MyTraderSpace.Trading.Strategies
{
    /// <summary>
    /// Interface for trading strategies
    /// </summary>
    public interface IStrategy
    {
        string NameId { get; init; }

        StrategyState State { get; }

        /// <summary>
        /// Gets the Exchange API instance used by the strategy.
        /// </summary>
        BaseExchangeAPI ExchangeAPI { get; }

        /// <summary>
        /// Gets the Market Data Service instance used by the strategy.
        /// </summary>
        IMarketDataService MarketDataService { get; }

        /// <summary>
        /// Gets a value indicating whether the strategy is currently running.
        /// </summary>
        bool IsRunning => State == StrategyState.Running;

        /// <summary>
        /// Starts the trading strategy asynchronously.
        /// </summary>
        /// <returns>A task representing the asynchronous operation.</returns>
        Task StartAsync();

        /// <summary>
        /// Stops the trading strategy asynchronously.
        /// </summary>
        /// <returns>A task representing the asynchronous operation.</returns>
        Task StopAsync();
    }
} 