using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using MyTraderSpace.Configuration;
using MyTraderSpace.Exchanges;
using MyTraderSpace.Logging;
using MyTraderSpace.Models;

namespace MyTraderSpace.Trading.Strategies
{
    public class MainStrategy_New : IUIDataProvider, IDisposable
    {
        public CurrencyPair TradingPair { get; init; }
        private HedgeGridStrategy_New? _currentStrategy = null;
        private readonly ConfigurationLoader _configLoader;
        private readonly LogManager _log;
        private readonly IMarketDataService _marketDataService;
        private Dictionary<string, BaseExchangeAPI> ApiPool { get; set; } = new Dictionary<string, BaseExchangeAPI>();
        private ConcurrentDictionary<string, HedgeGridStrategy_New> _strategyPool = new ConcurrentDictionary<string, HedgeGridStrategy_New>();
        private MainStrategyConfig? _mainStrategyConfig;
        private HedgeGridStrategyConfig? _hedgeGridStrategyConfig;
        private CancellationTokenSource? _runCts;
        private bool _disposed = false;

        public StrategyState State { get; private set; } = StrategyState.Initializing;

        // --- PnL and Fee Tracking ---
        public decimal AccumulatedCalculatedRealizedPnL { get; private set; } = 0m;
        public decimal AccumulatedReportedRealizedPnL { get; private set; } = 0m;
        public Fee AccumulatedCalculatedFees { get; private set; } = new Fee();
        public Fee AccumulatedReportedFees { get; private set; } = new Fee();

        // Drawdown tracking
        public decimal PeakCalculatedPnL { get; private set; } = 0m;
        public decimal TroughCalculatedPnL { get; private set; } = 0m;
        public decimal MaxDrawdownCalculated { get; private set; } = 0m;

        public MainStrategy_New(ConfigurationLoader configLoader, IMarketDataService marketDataService)
        {
            _configLoader = configLoader ?? throw new ArgumentNullException(nameof(configLoader));
            _marketDataService = marketDataService ?? throw new ArgumentNullException(nameof(marketDataService));
            
            _log = new LogManager(nameof(MainStrategy_New));
            
            // Config loading and other initializations will go here.
            _mainStrategyConfig = _configLoader.GetStrategyConfig<MainStrategyConfig>(nameof(MainStrategy));
            _hedgeGridStrategyConfig = _configLoader.GetStrategyConfig<HedgeGridStrategyConfig>(nameof(HedgeGridStrategy));

            TradingPair = _configLoader.GetAppConfig().TradingPairParsed;

            _log.Information("MainStrategy_New created.");
        }

        public async Task InitializeAsync()
        {
            State = StrategyState.Initializing;
            _log.Information("Initializing MainStrategy_New...");

            // In a real scenario, API pool would be populated here
            // e.g., SetAvailableApis(...)

            SubscribeToExchangeEvents();

            // Logic to reconstruct the grid of HGS instances from the exchange would go here.
            // For now, we'll assume a blank slate.

            State = StrategyState.Ready;
            _log.Information("MainStrategy_New Initialized and Ready.");
            await Task.CompletedTask;
        }

        public async Task StartAsync()
        {
            if (State != StrategyState.Ready)
            {
                _log.Error("Cannot start MainStrategy, not in Ready state.");
                return;
            }
            _log.Information("Starting MainStrategy_New...");
            State = StrategyState.Running;

            await HandleBlankSlateStartAsync();
        }

        private async Task HandleBlankSlateStartAsync()
        {
            _log.Information("Handling blank slate start: No existing grid found.");
            
            var api = await AcquireNextAvailableApiAsync();
            if (api == null)
            {
                _log.Error("Cannot start blank slate, no available API in the pool.");
                State = StrategyState.Error;
                return;
            }

            var hgsNameId = $"HGS_{DateTime.UtcNow:yyyyMMddHHmmssfff}";
            var newHgs = await CreateAndInitializeHgsAsync(hgsNameId, 0, api, _marketDataService, true);
            if (newHgs == null)
            {
                _log.Error("Failed to create and initialize the first HGS for a blank slate start.");
                State = StrategyState.Error;
                ReleaseApiToPool(api);
                return;
            }

            if (newHgs.State == StrategyState.Error)
            {
                _log.Error($"First HGS '{newHgs.NameId}' is in Error state after initialization. Cannot proceed with blank slate start.");
                State = StrategyState.Error;
                return;
            }

            _currentStrategy = newHgs;
            _log.Information($"Blank slate HGS '{newHgs.NameId}' created and set as current strategy.");
            await newHgs.StartAsync();
        }

        private async Task<HedgeGridStrategy_New?> CreateAndInitializeHgsAsync(string hgsNameId, decimal intendedPrice, BaseExchangeAPI apiForHgs, IMarketDataService mdService, bool isInitialBlankSlateStep)
        {
            _log.Information($"Creating HGS '{hgsNameId}' with IntendedPrice: {intendedPrice}");
            if (_strategyPool.ContainsKey(hgsNameId))
            {
                _log.Warning($"Strategy with name {hgsNameId} already exists in the pool.");
                return _strategyPool[hgsNameId];
            }

            try
            {
                var hgs = new HedgeGridStrategy_New(hgsNameId, intendedPrice, apiForHgs, mdService, _hedgeGridStrategyConfig!, isInitialBlankSlateStep);
                if (_strategyPool.TryAdd(hgs.NameId, hgs))
                {
                    // Subscribe to HGS events before initializing
                    hgs.OrderUpdate += OnHgsOrderUpdate;
                    hgs.InitialPlacementFailed += OnHgsInitialPlacementFailed;
                    hgs.Activated += OnHgsActivated;
                    hgs.StrategyError += OnHgsStrategyError;
                    hgs.FeeUpdated += OnHgsFeeUpdated;
                    hgs.PnLUpdated += OnHgsPnLUpdated;

                    await hgs.InitializeAsync();
                    if (hgs.State == StrategyState.Error)
                    {
                        _log.Error($"HGS '{hgs.NameId}' entered Error state during initialization. API '{apiForHgs.Name}' remains committed. HGS will be retained in error state in the pool.");

                        // Ensure the API is considered "used" and not in the available ApiPool
                        if (ApiPool.ContainsKey(apiForHgs.Name))
                        {
                            _log.Warning($"API '{apiForHgs.Name}' for errored HGS '{hgs.NameId}' was unexpectedly found in ApiPool. Removing it now to prevent reuse.");
                            ApiPool.Remove(apiForHgs.Name);
                        }

                        // DO NOT remove from _strategyPool - errored HGS must remain to quarantine the API
                        // DO NOT unsubscribe from OrderUpdate - we still want to monitor it
                        return hgs; // Return the errored HGS so it can be tracked
                    }
                    _log.Information($"HGS '{hgs.NameId}' created and initialized successfully.");
                    return hgs;
                }
            }
            catch (Exception ex)
            {
                _log.Error(ex, $"Failed to create or initialize HGS '{hgsNameId}'.");
            }

            return null;
        }

        private async Task<BaseExchangeAPI?> AcquireNextAvailableApiAsync()
        {
            // This is a simplified stand-in for the original API pool management logic
            if (ApiPool.Any())
                return await Task.FromResult(ApiPool.First().Value);
            
            return null;
        }

        private bool ReleaseApiToPool(BaseExchangeAPI? api)
        {
            // Stand-in for more complex logic
            return true;
        }

        private void RemoveHgsFromPool(HedgeGridStrategy_New hgs)
        {
            if (_strategyPool.TryRemove(hgs.NameId, out _))
            {
                // Unsubscribe from the HGS OrderUpdate event
                hgs.OrderUpdate -= OnHgsOrderUpdate;
                _log.Information($"Removed HGS '{hgs.NameId}' from strategy pool and unsubscribed from events.");
            }
        }

        private void SubscribeToExchangeEvents()
        {
            // Subscribe to both global order updates (to forward to HGS) and market data
            var primaryApi = ApiPool.Values.FirstOrDefault();
            if (primaryApi != null)
            {
                primaryApi.OnOrderUpdate += OnGlobalOrderUpdate;
                _marketDataService.SubscribeToFuturesMarketData(TradingPair, OnGlobalMarketDataUpdate);
                _log.Information("Subscribed to global exchange events.");
            }
            else
            {
                _log.Warning("No API instances available in pool to subscribe to events.");
            }
        }

        private void UnsubscribeFromExchangeEvents()
        {
            var primaryApi = ApiPool.Values.FirstOrDefault();
            if (primaryApi != null)
            {
                primaryApi.OnOrderUpdate -= OnGlobalOrderUpdate;
                // Unsubscribing from market data would require managing the IDisposable token.
                _log.Information("Unsubscribed from global exchange events.");
            }
        }

        private void OnGlobalOrderUpdate(OrderModelUpdate update)
        {
            _log.Verbose($"Global order update received for Cloid: {update.ClientOrderId}");
            // Find the HGS instance that manages this order and forward the update.
            foreach (var hgs in _strategyPool.Values)
            {
                if (hgs.LongSide.IsManagingOrder(update.ClientOrderId, update.OrderId) ||
                    hgs.ShortSide.IsManagingOrder(update.ClientOrderId, update.OrderId))
                {
                    _log.Verbose($"Forwarding order update to HGS: {hgs.NameId}");
                    hgs.OnOrderUpdate(update);
                    return; // Assume only one HGS can manage an order
                }
            }
            _log.Warning($"No HGS found in the pool to handle order update for Cloid: {update.ClientOrderId}");
        }

        private void OnHgsOrderUpdate(HedgeGridStrategy_New hgs, OrderModelUpdate update)
        {
            _log.Verbose($"Order update received from HGS '{hgs.NameId}' for Cloid: {update.ClientOrderId}, Status: {update.Status}");

            // Handle HGS that have entered Error state
            if (hgs.State == StrategyState.Error)
            {
                _log.Information($"HGS '{hgs.NameId}' is in Error state due to a runtime error. API '{hgs.ExchangeAPI?.Name}' will remain committed. HGS will be retained in Error state in the pool.");

                if (hgs.ExchangeAPI == null)
                {
                    _log.Error($"Critical: HGS '{hgs.NameId}' in Error state but its ExchangeAPI is null. Cannot manage API commitment.");
                    return;
                }

                // Ensure the API is considered "used" and not in the available ApiPool
                if (ApiPool.ContainsKey(hgs.ExchangeAPI.Name))
                {
                    _log.Warning($"API '{hgs.ExchangeAPI.Name}' for errored HGS '{hgs.NameId}' was unexpectedly found in ApiPool. Removing it now to prevent reuse.");
                    ApiPool.Remove(hgs.ExchangeAPI.Name);
                }

                // If this was the current strategy, nullify it
                if (_currentStrategy == hgs)
                {
                    _log.Information($"Errored HGS '{hgs.NameId}' was the CurrentStrategy. Nullifying CurrentStrategy.");
                    _currentStrategy = null;
                }

                return;
            }

            // TODO: Add specific logic here for MainStrategy to react to HGS order updates
            // This could include:
            // - Monitoring for activation events (both sides filled)
            // - Tracking PnL and fees
            // - Managing grid expansion/contraction
            // - Creating flanking steps when HGS becomes active
        }

        private void OnHgsInitialPlacementFailed(HedgeGridStrategy_New hgs, string reason)
        {
            _log.Error($"HGS '{hgs.NameId}' reported OnInitialPlacementFailed. Reason: {reason}. HGS State: {hgs.State}. API '{hgs.ExchangeAPI?.Name}' will remain committed. HGS will be retained in Error state.");

            if (hgs.ExchangeAPI == null)
            {
                _log.Error($"Critical: HGS '{hgs.NameId}' reported OnInitialPlacementFailed but its ExchangeAPI is null. Cannot manage API commitment.");
                return;
            }

            // Ensure the API is considered "used" and not in the available ApiPool
            if (ApiPool.ContainsKey(hgs.ExchangeAPI.Name))
            {
                _log.Warning($"API '{hgs.ExchangeAPI.Name}' for HGS '{hgs.NameId}' (which failed initial placement) was unexpectedly found in ApiPool. Removing it now to prevent reuse.");
                ApiPool.Remove(hgs.ExchangeAPI.Name);
            }
        }

        private void OnHgsActivated(HedgeGridStrategy_New hgs)
        {
            _log.Information($"HGS '{hgs.NameId}' has been activated (both base orders filled).");

            if (hgs.State == StrategyState.Error)
            {
                _log.Warning($"HGS '{hgs.NameId}' sent OnActivated, but it's in Error state. Ignoring activation.");
                return;
            }

            // Set as current strategy if we don't have one
            if (_currentStrategy == null)
            {
                _log.Information($"HGS '{hgs.NameId}' set as CurrentStrategy.");
                _currentStrategy = hgs;

                // TODO: Add logic for creating flanking steps and grid management
                // await CreateFlankingStepsAsync(hgs);
            }
        }

        private void OnHgsStrategyError(HedgeGridStrategy_New hgs, string errorMessage)
        {
            _log.Error($"HGS '{hgs.NameId}' reported StrategyError: {errorMessage}");

            // The error handling is already done in OnHgsOrderUpdate when we detect Error state
            // This event provides additional context about the error
        }

        private void OnHgsFeeUpdated(HedgeGridStrategy_New hgs, OrderPair_New orderPair, Fee calculatedFee, Fee reportedFee)
        {
            _log.Information($"Fee Update from HGS '{hgs.NameId}' OrderPair '{orderPair.Name}'. Calc: {calculatedFee}, Rep: {reportedFee}");

            AccumulatedCalculatedFees += calculatedFee;
            AccumulatedReportedFees += reportedFee;

            UpdateDrawdownAndLog();
        }

        private void OnHgsPnLUpdated(HedgeGridStrategy_New hgs, OrderPair_New orderPair, decimal calculatedPnl, decimal reportedPnl)
        {
            _log.Information($"PnL Update from HGS '{hgs.NameId}' OrderPair '{orderPair.Name}'. Calc: {calculatedPnl}, Rep: {reportedPnl}");

            AccumulatedCalculatedRealizedPnL += calculatedPnl;
            AccumulatedReportedRealizedPnL += reportedPnl;

            UpdateDrawdownAndLog();
        }

        private void UpdateDrawdownAndLog()
        {
            // Update peak and trough tracking
            if (AccumulatedCalculatedRealizedPnL > PeakCalculatedPnL)
            {
                PeakCalculatedPnL = AccumulatedCalculatedRealizedPnL;
            }

            if (AccumulatedCalculatedRealizedPnL < TroughCalculatedPnL)
            {
                TroughCalculatedPnL = AccumulatedCalculatedRealizedPnL;
            }

            // Calculate max drawdown (peak to current)
            decimal currentDrawdown = PeakCalculatedPnL - AccumulatedCalculatedRealizedPnL;
            if (currentDrawdown > MaxDrawdownCalculated)
            {
                MaxDrawdownCalculated = currentDrawdown;
            }

            _log.Information($"MAIN STRATEGY AGGREGATE PnL: AccumCalcPnL: {AccumulatedCalculatedRealizedPnL:F4}, AccumRepPnL: {AccumulatedReportedRealizedPnL:F4}, AccumRepFees: {AccumulatedReportedFees.Quote.Amount:F4}, AccumCalcFees: {AccumulatedCalculatedFees.Quote.Amount:F4}, Peak: {PeakCalculatedPnL:F4}, MaxDD: {MaxDrawdownCalculated:F4}");
        }

        private void OnGlobalMarketDataUpdate(FuturesMarketData data)
        {
            // For now, we broadcast the market data to all strategies.
            // A more optimized approach might only send it to relevant strategies.
            foreach (var hgs in _strategyPool.Values)
            {
                hgs.OnMarketDataUpdate(data);
            }
        }

        public void Dispose()
        {
            Dispose(true);
            GC.SuppressFinalize(this);
        }

        protected virtual void Dispose(bool disposing)
        {
            if (_disposed) return;
            if (disposing)
            {
                UnsubscribeFromExchangeEvents();

                // Unsubscribe from all HGS events and dispose HGS instances
                foreach (var hgs in _strategyPool.Values)
                {
                    hgs.OrderUpdate -= OnHgsOrderUpdate;
                    hgs.InitialPlacementFailed -= OnHgsInitialPlacementFailed;
                    hgs.Activated -= OnHgsActivated;
                    hgs.StrategyError -= OnHgsStrategyError;
                    hgs.FeeUpdated -= OnHgsFeeUpdated;
                    hgs.PnLUpdated -= OnHgsPnLUpdated;
                    hgs.Dispose();
                }
                _strategyPool.Clear();

                _runCts?.Cancel();
                _runCts?.Dispose();
                _log.Information("MainStrategy_New disposed.");
            }
            _disposed = true;
        }
        
        public StrategyUIData? GetUIDataSnapshot()
        {
            // UI data aggregation logic will be ported later
            return new StrategyUIData();
        }
    }
} 