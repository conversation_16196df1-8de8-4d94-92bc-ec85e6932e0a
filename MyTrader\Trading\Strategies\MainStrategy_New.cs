using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using MyTraderSpace.Configuration;
using MyTraderSpace.Exchanges;
using MyTraderSpace.Logging;
using MyTraderSpace.Models;

namespace MyTraderSpace.Trading.Strategies
{
    public class MainStrategy_New : IUIDataProvider, IDisposable
    {
        public CurrencyPair TradingPair { get; init; }
        private HedgeGridStrategy_New? _currentStrategy = null;
        private readonly ConfigurationLoader _configLoader;
        private readonly LogManager _log;
        private readonly IMarketDataService _marketDataService;
        private Dictionary<string, BaseExchangeAPI> ApiPool { get; set; } = new Dictionary<string, BaseExchangeAPI>();
        private ConcurrentDictionary<string, HedgeGridStrategy_New> _strategyPool = new ConcurrentDictionary<string, HedgeGridStrategy_New>();
        private MainStrategyConfig? _mainStrategyConfig;
        private HedgeGridStrategyConfig? _hedgeGridStrategyConfig;
        private CancellationTokenSource? _runCts;
        private bool _disposed = false;

        public StrategyState State { get; private set; } = StrategyState.Initializing;

        public MainStrategy_New(ConfigurationLoader configLoader, IMarketDataService marketDataService)
        {
            _configLoader = configLoader ?? throw new ArgumentNullException(nameof(configLoader));
            _marketDataService = marketDataService ?? throw new ArgumentNullException(nameof(marketDataService));
            
            _log = new LogManager(nameof(MainStrategy_New));
            
            // Config loading and other initializations will go here.
            _mainStrategyConfig = _configLoader.GetStrategyConfig<MainStrategyConfig>(nameof(MainStrategy));
            _hedgeGridStrategyConfig = _configLoader.GetStrategyConfig<HedgeGridStrategyConfig>(nameof(HedgeGridStrategy));

            TradingPair = _configLoader.GetAppConfig().TradingPairParsed;

            _log.Information("MainStrategy_New created.");
        }

        public async Task InitializeAsync()
        {
            State = StrategyState.Initializing;
            _log.Information("Initializing MainStrategy_New...");

            // In a real scenario, API pool would be populated here
            // e.g., SetAvailableApis(...)

            SubscribeToExchangeEvents();

            // Logic to reconstruct the grid of HGS instances from the exchange would go here.
            // For now, we'll assume a blank slate.

            State = StrategyState.Ready;
            _log.Information("MainStrategy_New Initialized and Ready.");
            await Task.CompletedTask;
        }

        public async Task StartAsync()
        {
            if (State != StrategyState.Ready)
            {
                _log.Error("Cannot start MainStrategy, not in Ready state.");
                return;
            }
            _log.Information("Starting MainStrategy_New...");
            State = StrategyState.Running;

            await HandleBlankSlateStartAsync();
        }

        private async Task HandleBlankSlateStartAsync()
        {
            _log.Information("Handling blank slate start: No existing grid found.");
            
            var api = await AcquireNextAvailableApiAsync();
            if (api == null)
            {
                _log.Error("Cannot start blank slate, no available API in the pool.");
                State = StrategyState.Error;
                return;
            }

            var hgsNameId = $"HGS_{DateTime.UtcNow:yyyyMMddHHmmssfff}";
            var newHgs = await CreateAndInitializeHgsAsync(hgsNameId, 0, api, _marketDataService, true);
            if (newHgs == null)
            {
                _log.Error("Failed to create and initialize the first HGS for a blank slate start.");
                State = StrategyState.Error;
                ReleaseApiToPool(api);
                return;
            }

            _currentStrategy = newHgs;
            _log.Information($"Blank slate HGS '{newHgs.NameId}' created and set as current strategy.");
            await newHgs.StartAsync();
        }

        private async Task<HedgeGridStrategy_New?> CreateAndInitializeHgsAsync(string hgsNameId, decimal intendedPrice, BaseExchangeAPI apiForHgs, IMarketDataService mdService, bool isInitialBlankSlateStep)
        {
            _log.Information($"Creating HGS '{hgsNameId}' with IntendedPrice: {intendedPrice}");
            if (_strategyPool.ContainsKey(hgsNameId))
            {
                _log.Warning($"Strategy with name {hgsNameId} already exists in the pool.");
                return _strategyPool[hgsNameId];
            }

            try
            {
                var hgs = new HedgeGridStrategy_New(hgsNameId, intendedPrice, apiForHgs, mdService, _hedgeGridStrategyConfig!, isInitialBlankSlateStep);
                if (_strategyPool.TryAdd(hgs.NameId, hgs))
                {
                    await hgs.InitializeAsync();
                    if (hgs.State == StrategyState.Error)
                    {
                        _log.Error($"HGS '{hgs.NameId}' entered Error state during initialization.");
                        return null;
                    }
                    _log.Information($"HGS '{hgs.NameId}' created and initialized successfully.");
                    return hgs;
                }
            }
            catch (Exception ex)
            {
                _log.Error(ex, $"Failed to create or initialize HGS '{hgsNameId}'.");
            }

            return null;
        }

        private async Task<BaseExchangeAPI?> AcquireNextAvailableApiAsync()
        {
            // This is a simplified stand-in for the original API pool management logic
            if (ApiPool.Any())
                return await Task.FromResult(ApiPool.First().Value);
            
            return null;
        }

        private bool ReleaseApiToPool(BaseExchangeAPI? api)
        {
            // Stand-in for more complex logic
            return true;
        }

        private void SubscribeToExchangeEvents()
        {
            // This assumes we have a primary API instance to get global updates from.
            // The logic might need to be adapted if updates come from multiple API instances.
            var primaryApi = ApiPool.Values.FirstOrDefault();
            if (primaryApi != null)
            {
                primaryApi.OnOrderUpdate += OnGlobalOrderUpdate;
                _marketDataService.SubscribeToFuturesMarketData(TradingPair, OnGlobalMarketDataUpdate);
                _log.Information("Subscribed to global exchange events.");
            }
            else
            {
                _log.Warning("No API instances available in pool to subscribe to events.");
            }
        }

        private void UnsubscribeFromExchangeEvents()
        {
            var primaryApi = ApiPool.Values.FirstOrDefault();
            if (primaryApi != null)
            {
                primaryApi.OnOrderUpdate -= OnGlobalOrderUpdate;
                // Unsubscribing from market data would require managing the IDisposable token.
                _log.Information("Unsubscribed from global exchange events.");
            }
        }

        private void OnGlobalOrderUpdate(OrderModelUpdate update)
        {
            _log.Verbose($"Global order update received for Cloid: {update.ClientOrderId}");
            // Find the HGS instance that manages this order and forward the update.
            foreach (var hgs in _strategyPool.Values)
            {
                if (hgs.LongSide.IsManagingOrder(update.ClientOrderId, update.OrderId) || 
                    hgs.ShortSide.IsManagingOrder(update.ClientOrderId, update.OrderId))
                {
                    _log.Verbose($"Forwarding order update to HGS: {hgs.NameId}");
                    hgs.OnOrderUpdate(update);
                    return; // Assume only one HGS can manage an order
                }
            }
            _log.Warning($"No HGS found in the pool to handle order update for Cloid: {update.ClientOrderId}");
        }

        private void OnGlobalMarketDataUpdate(FuturesMarketData data)
        {
            // For now, we broadcast the market data to all strategies.
            // A more optimized approach might only send it to relevant strategies.
            foreach (var hgs in _strategyPool.Values)
            {
                hgs.OnMarketDataUpdate(data);
            }
        }

        public void Dispose()
        {
            Dispose(true);
            GC.SuppressFinalize(this);
        }

        protected virtual void Dispose(bool disposing)
        {
            if (_disposed) return;
            if (disposing)
            {
                UnsubscribeFromExchangeEvents();
                // Cleanup logic here
                _runCts?.Cancel();
                _runCts?.Dispose();
                _log.Information("MainStrategy_New disposed.");
            }
            _disposed = true;
        }
        
        public StrategyUIData? GetUIDataSnapshot()
        {
            // UI data aggregation logic will be ported later
            return new StrategyUIData();
        }
    }
} 