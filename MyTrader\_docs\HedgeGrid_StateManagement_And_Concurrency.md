﻿# Hedge Grid Strategy - State Management and Concurrency Rules

This document outlines the agreed-upon rules for handling state, processing market data, and managing concurrency within the `HedgeGridStrategy` to ensure robustness and prevent race conditions.

## Core Principle: Sequential State, Ignore Ticks During Processing

The entire `MainStrategy` and `HedgeGridStrategy` ecosystem is built on a fundamental principle to prevent race conditions and ensure deterministic behavior: **State transitions are processed sequentially, and any new market data (ticks) that arrives while a state transition is in progress MUST be ignored.**

- **NO QUEUING:** Ticks are not buffered or queued for later processing. If the system is busy, the incoming tick is dropped. This is acceptable because the strategy is designed to re-evaluate its state based on the *next available* tick once it's free. Chasing stale, buffered ticks would lead to incorrect decisions.

- **WHAT IS "PROCESSING"?** The system is considered "busy" or "processing" if either of the following is true:
    1.  The `MainStrategy` is in the middle of its `EvaluateAndManageGridAsync` logic. This is the master evaluation loop that can create or prune entire strategies.
    2.  Any individual `HedgeGridStrategy` instance is actively trying to place an order (`IsAnyPairPlacing` is `true`). This is a critical, atomic operation that cannot be interrupted.

- **IMPLEMENTATION:** This principle is enforced at the entry point of market data handling in `MainStrategy.cs`. A check at the beginning of the market data event handler immediately exits if any processing is active. This ensures that the logic inside `EvaluateAndManageGridAsync` and the order placement logic in `OrderPair` can be written without concerns for re-entrancy or concurrent modification, as they are protected by this "ignore" gate.

This design choice simplifies the state management logic significantly by eliminating a whole class of complex concurrency problems.

## Core Principles

1.  **State Certainty:** Trading logic evaluation and decision-making must only occur when the system is in a known, confirmed state. No evaluations should be based on potentially outdated information while waiting for exchange confirmations.
2.  **Robustness over Responsiveness:** Prioritize preventing race conditions and ensuring state consistency over potentially marginal gains in responsiveness during brief order confirmation windows.
3.  **Decisions from Stable State:** All actions decided during a single evaluation cycle (when no results are awaited) are considered part of one logical decision and should be initiated.

## Rules

1.  **Market Data Updates (`_latestFuturesData`):**
    *   Incoming market data ticks are always processed immediately upon arrival (`OnFuturesMarketDataUpdate`) to update the strategy's internal view (`_latestFuturesData`). This ensures the latest price information is available when evaluation *is* permitted.

2.  **Evaluation Logic Halting (`IsAwaitingResult`):**
    *   The core evaluation logic within `ProcessMarketUpdateAsync` (including checks for re-opening positions, checking for gaps/end movements via `CheckAndOpenNewStep`, and any future consolidation/hedging logic) **MUST BE SKIPPED** entirely if `_tradePoints.Any(tp => tp.IsAwaitingResult)` is true.
    *   `IsAwaitingResult` becomes true when an `OrderPoint`'s `BaseOrder` or `TakeProfitOrder` status is set to `OrderStatus.Created` after a placement request. It becomes false when the status is updated via an `OnOrderUpdate` event from the exchange (to `New`, `Filled`, `Rejected`, `Cancelled`, etc.).
    *   This rule guarantees that trading decisions are only made based on a fully confirmed system state. The strategy will wait until *all* outstanding `OrderStatus.Created` entries are resolved.

3.  **API Call Initiation:**
    *   The `HandleOrderPlacementRequest` method (and any similar method that directly calls the `ExchangeAPI`) should proceed to make its API call without an additional internal strategy-level lock (like the old `_isProcessingApiCall` flag).
    *   Concurrency of actual API calls is managed by the underlying `ExchangeAPI` implementation and the exchange itself.

4.  **Permitting Multiple Actions from a Single Evaluation:**
    *   If, during a single execution of `ProcessMarketUpdateAsync` (when no results are awaited), the strategy logic decides on multiple actions (e.g., placing both base orders for a new `TradePoint`, or a re-open AND a new step), all corresponding order requests should be initiated.
    *   Once these requests are made and their respective `OrderPoint` statuses become `OrderStatus.Created`, Rule 2 (`IsAwaitingResult`) will naturally halt further evaluations until all these new requests are resolved by the exchange.

## Rationale Summary

This approach ensures that the strategy never acts on incomplete information. All decisions are made from a "settled" state. If multiple actions are decided in such a state, they are all dispatched. The system then enters a waiting period (halting further evaluations) until all those dispatched actions receive a conclusive result from the exchange, ensuring the next evaluation cycle also starts from a fully settled state. The previous `_isProcessingApiCall` flag (for serializing API calls at the strategy level) is no longer necessary under this model.
