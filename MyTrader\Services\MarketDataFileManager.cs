using System;
using System.Collections.Generic;
using System.IO;
using System.Threading;
using System.Threading.Tasks;
using MyTraderSpace.Logging;
using Newtonsoft.Json;
using System.Linq;
using System.Threading.Channels;

// Note: SimulatedMarketDataConfig also has a 'simple' 'FileSaver'/ 'FileLoader'
public class MarketDataFileManager : IDisposable
{
    // Helper struct to hold save requests in the channel
    private readonly record struct SaveRequest(string Symbol, object Data);

    private readonly Dictionary<string, DateTime> _lastFileDates = new();
    private readonly Dictionary<string, string> _symbolFileNames = new();
    private readonly Dictionary<string, StreamWriter> _activeWriters = new();
    private readonly string _baseDirectory;
    private readonly LogManager _log;

    // Channel and processing task for async saving
    private readonly Channel<SaveRequest> _saveChannel;
    private readonly Task _processingTask;
    private readonly CancellationTokenSource _cts;
    private bool _isDisposed; // Added disposal flag

    public MarketDataFileManager(string baseDirectory, int queueCapacity = 1000) // Added capacity parameter
    {
        _baseDirectory = baseDirectory;
        _log = new LogManager(nameof(MarketDataFileManager),
            LogLevel.Information,
            enableConsole: true,
            enableFile: true
        );

        if (!Directory.Exists(_baseDirectory))
        {
            _log.Information($"Base directory '{_baseDirectory}' does not exist. Creating...");
            Directory.CreateDirectory(_baseDirectory);
        }

        // Initialize channel and cancellation token
        _cts = new CancellationTokenSource();
        _saveChannel = Channel.CreateBounded<SaveRequest>(new BoundedChannelOptions(queueCapacity)
        {
            FullMode = BoundedChannelFullMode.Wait, // Or DropWrite, DropOldest - Wait provides backpressure signal
            SingleReader = true, // Optimization as we only have one processing task
            SingleWriter = false // Multiple threads might call EnqueueSave
        });

        // Start the background processing task
        _processingTask = Task.Run(ProcessSaveQueueAsync);
        _log.Information($"MarketDataFileManager initialized. Save queue capacity: {queueCapacity}.");
    }

    private string GetFileName(string symbol)
    {
        var currentDate = DateTime.Now.Date;

        // Get or initialize last file date for this symbol
        if (!_lastFileDates.TryGetValue(symbol, out var symbolLastFileDate))
        {
            symbolLastFileDate = DateTime.MinValue;
            _lastFileDates[symbol] = symbolLastFileDate;
        }

        // Check if date changed or if this symbol hasn't been seen before
        if (currentDate != symbolLastFileDate || !_symbolFileNames.ContainsKey(symbol))
        {
            _lastFileDates[symbol] = currentDate;
            var baseFileName = Path.Combine(_baseDirectory, 
                $"market_data_{symbol}_{DateTime.Now:yyyyMMdd}.json");
            
            if (!File.Exists(baseFileName))
            {
                _symbolFileNames[symbol] = baseFileName;
                _log.Information($"Created new file for {symbol}: {Path.GetFileName(baseFileName)}");
                return baseFileName;
            }

            // Find first available numbered filename
            int counter = 1;
            string numberedFileName;
            do
            {
                numberedFileName = Path.Combine(_baseDirectory, 
                    $"market_data_{symbol}_{DateTime.Now:yyyyMMdd}_{counter}.json");
                counter++;
            } while (File.Exists(numberedFileName));

            _symbolFileNames[symbol] = numberedFileName;
            _log.Information($"Created new file for {symbol}: {Path.GetFileName(numberedFileName)}");
        }

        return _symbolFileNames[symbol];
    }

    private StreamWriter GetWriter(string symbol)
    {
        lock (_activeWriters)
        {
            if (!_activeWriters.TryGetValue(symbol, out var writer))
            {
                var filePath = GetFileName(symbol);
                try
                {
                    writer = new StreamWriter(filePath, true) { AutoFlush = true };
                    _activeWriters[symbol] = writer;
                    _log.Debug($"Opened StreamWriter for {symbol} at path: {filePath}");
                }
                catch (Exception ex)
                {
                    _log.Error(ex, $"Failed to create StreamWriter for symbol {symbol} at path {filePath}");
                    throw;
                }
            }
            return writer;
        }
    }

    // Public method to enqueue data for saving - non-blocking
    // Returns true if successfully enqueued, false if channel was full (if using Drop modes) or closed.
    public bool TryEnqueueSave<T>(string symbol, T data)
    {
        if (_isDisposed || _cts.IsCancellationRequested)
        {
             _log.Warning("Attempted to enqueue save after disposal or cancellation for symbol {Symbol}.", symbol);
             return false;
        }

        var request = new SaveRequest(symbol, data!); // Create request
        bool success = _saveChannel.Writer.TryWrite(request);

        if (!success && _saveChannel.Reader.Completion.IsCompleted) // Check if failed due to completion
        {
            _log.Warning("Save channel is already completed. Could not enqueue save for symbol {Symbol}.", symbol);
        }
        else if (!success) // Failed likely due to FullMode = Drop... (if configured)
        {
             _log.Warning("Save channel is full. Could not enqueue save for symbol {Symbol}.", symbol);
        }
        // If FullMode=Wait, TryWrite will return false immediately if channel is full,
        // but WriteAsync would block. We use TryWrite for responsiveness.

        return success;
    }

    // OR: If backpressure (blocking the caller) is acceptable/desired:
    /*
    public async ValueTask EnqueueSaveAsync<T>(string symbol, T data, CancellationToken cancellationToken = default)
    {
        if (_isDisposed) throw new ObjectDisposedException(nameof(MarketDataFileManager));
        var request = new SaveRequest(symbol, data!);
        await _saveChannel.Writer.WriteAsync(request, cancellationToken);
    }
    */

    // Internal processing loop run by _processingTask
    private async Task ProcessSaveQueueAsync()
    {
        _log.Information("Save queue processing task started.");
        try
        {
            // Read until channel is completed and empty
            await foreach (var request in _saveChannel.Reader.ReadAllAsync(_cts.Token))
            {
                await SaveRecordInternalAsync(request.Symbol, request.Data);
            }
        }
        catch (OperationCanceledException)
        {
            _log.Information("Save queue processing task cancelled.");
        }
        catch (ChannelClosedException)
        {
            _log.Information("Save channel was closed, processing loop ending.");
            // Optionally process remaining items if needed, though ReadAllAsync usually handles this.
        }
        catch (Exception ex)
        {
            _log.Error(ex, "Unhandled exception in save queue processing task.");
        }
        finally
        {
            _log.Information("Save queue processing task finished. Closing active writers.");
            // Ensure writers are flushed and closed when processing stops
            CloseActiveWriters();
        }
    }

    // The actual synchronous saving logic, called by the processing task
    private Task SaveRecordInternalAsync(string symbol, object data)
    {
        // This can potentially still block if GetWriter needs to create a new file/directory,
        // but the primary blocking call (WriteLineAsync) is handled.
        // Consider making GetWriter async if file creation is slow/problematic.
        try
        {
            var writer = GetWriter(symbol); // Synchronous writer retrieval/creation
            var json = JsonConvert.SerializeObject(data);
            // Use WriteLineAsync on the background thread.
            return writer.WriteLineAsync(json); // Return the Task
        }
        catch (Exception ex)
        {
            _log.Error(ex, $"Failed to save market data internally for symbol {symbol}");
            return Task.CompletedTask; // Return completed task on error
        }
    }

    public async Task<IEnumerable<T>> LoadMarketDataAsync<T>(
        string symbol, 
        CancellationToken cancellationToken = default)
    {
        var result = new List<T>();
        IEnumerable<string> files;
        try
        {
            files = GetDataFiles(symbol);
            _log.Information($"Found {files.Count()} data files for symbol '{symbol}' in '{_baseDirectory}'.");
        }
        catch (Exception ex)
        {
            _log.Error(ex, $"Failed to get data files for symbol '{symbol}' in '{_baseDirectory}'.");
            return Enumerable.Empty<T>();
        }

        foreach (var file in files)
        {
            if (cancellationToken.IsCancellationRequested)
            {
                _log.Information($"Loading cancelled for symbol '{symbol}'.");
                break;
            }

            _log.Debug($"Loading data from file: {file}");
            try
            {
                using var reader = new StreamReader(file);
                string? line;
                int lineNumber = 0;
                while ((line = await reader.ReadLineAsync()) != null)
                {
                    lineNumber++;
                    if (cancellationToken.IsCancellationRequested) break;

                    if (!string.IsNullOrWhiteSpace(line))
                    {
                        try
                        {
                            var data = JsonConvert.DeserializeObject<T>(line);
                            if (data != null)
                                result.Add(data);
                        }
                        catch (JsonException jsonEx)
                        {
                            _log.Warning(jsonEx, $"Failed to deserialize line {lineNumber} in file '{file}'. Line: {line.Substring(0, Math.Min(line.Length, 100))}...");
                        }
                    }
                }
                _log.Debug($"Finished loading data from file: {file}");
            }
            catch (Exception ex)
            {
                _log.Error(ex, $"Failed to read or parse data file: {file}");
            }
        }
        _log.Information($"Finished loading {result.Count} records for symbol '{symbol}'.");
        return result;
    }

    private IEnumerable<string> GetDataFiles(string symbol)
    {
        var pattern = $"market_data_{symbol}*.json";
        if (!Directory.Exists(_baseDirectory))
        {
            _log.Warning($"Base directory '{_baseDirectory}' does not exist. Cannot find data files.");
            return Enumerable.Empty<string>();
        }
        return Directory.GetFiles(_baseDirectory, pattern)
                        .OrderBy(f => f);  // Ensure chronological order
    }

    // Closes and clears active writers, called by Dispose and potentially ProcessSaveQueueAsync finally block
    private void CloseActiveWriters()
    {
        lock (_activeWriters)
        {
            if (!_activeWriters.Any()) return; // No writers to close

            _log.Debug($"Closing {_activeWriters.Count} active file writers.");
            foreach (var kvp in _activeWriters)
            {
                try
                {
                    // Writer has AutoFlush = true, but explicit flush for safety
                    kvp.Value.Flush();
                    kvp.Value.Dispose();
                    _log.Debug($"Closed writer for symbol {kvp.Key}");
                }
                catch (Exception ex)
                {
                    _log.Error(ex, $"Error disposing writer for symbol {kvp.Key}");
                }
            }
            _activeWriters.Clear();
        }
    }

    // Public method to gracefully stop the saving process
    public async Task StopAsync(TimeSpan? timeout = null)
    {
        if (_isDisposed) return;
        _log.Information("Stopping MarketDataFileManager processing...");

        // Signal no more items will be added
        _saveChannel.Writer.TryComplete();

        // Signal cancellation to the processing loop
        if (!_cts.IsCancellationRequested)
        {
            _cts.Cancel();
        }

        // Wait for the processing task to finish
        _log.Debug("Waiting for save queue processing task to complete...");
        bool completedNaturally = false;
        if (timeout.HasValue)
        {
            completedNaturally = await Task.WhenAny(_processingTask, Task.Delay(timeout.Value)) == _processingTask;
        }
        else
        {
            await _processingTask;
            completedNaturally = true;
        }

        if (!completedNaturally)
        {
            _log.Warning($"Save queue processing task did not complete within the timeout ({timeout}). Writers might not be flushed.");
        }
        else
        {
            _log.Debug("Save queue processing task completed.");
        }

        // Close writers again just in case the finally block wasn't reached
        CloseActiveWriters();
        _log.Information("MarketDataFileManager processing stopped.");
    }

    public void Dispose()
    {
        if (_isDisposed) return;

        _log.Debug("Disposing MarketDataFileManager...");

        // Initiate graceful shutdown
        try
        {
            // Use Wait with a reasonable timeout, as Dispose should be synchronous
            StopAsync(TimeSpan.FromSeconds(10)).Wait();
        }
        catch (Exception ex)
        {
            _log.Warning(ex, "Exception during StopAsync within Dispose.");
            // Ensure cancellation is signaled even if StopAsync failed
            if (!_cts.IsCancellationRequested) _cts.Cancel();
        }

        // Dispose CancellationTokenSource
        _cts?.Dispose();

        // Close writers (might be redundant if StopAsync worked, but safe)
        CloseActiveWriters();

        // Dispose the LogManager
        _log?.Dispose();

        _isDisposed = true;
        GC.SuppressFinalize(this);
        // Cannot log after this point
    }
} 