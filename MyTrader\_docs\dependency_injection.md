# Dependency Injection Patterns in .NET

## Simple Injection
The most straightforward way to share dependencies:

```csharp
// Create shared dependency
var marketDataService = new BybitMarketDataService(environment, "BTCUSDT");
await marketDataService.InitializeAsync();
// Inject into multiple consumers
var api1 = new BybitExchangeAPI("BTCUSDT", "key1", "secret1", marketDataService);
var api2 = new BybitExchangeAPI("BTCUSDT", "key2", "secret2", marketDataService);
```

## DI Container Approach
Using Microsoft.Extensions.DependencyInjection for more complex scenarios:

```csharp
var services = new ServiceCollection();
// Register services with different lifecycles
services.AddSingleton<IMarketDataService, BybitMarketDataService>(); // One instance
services.AddTransient<IExchangeAPI, BybitExchangeAPI>(); // New instance each time
services.AddScoped<ITradeService>(); // One per scope
var provider = services.BuildServiceProvider();
var api = provider.GetRequiredService<IExchangeAPI>();
```

## Service Lifetimes

1. **Singleton**: One instance for the entire application
   - Good for: Shared resources, caches, configuration
   - Example: Market data service, logging

2. **Transient**: New instance created each time
   - Good for: Lightweight, stateless services
   - Example: API clients, validators

3. **Scoped**: One instance per scope/request
   - Good for: Per-request state in web apps
   - Example: Database contexts, user session

## When to Use What

### Simple Injection
- Small applications
- Clear dependency flow
- Few shared services
- Manual control needed

### DI Container
- Larger applications
- Complex dependency graphs
- Multiple implementations
- Testing with mocks
- Automatic lifecycle management

## Best Practices

1. Constructor Injection
```csharp
public class ExchangeAPI
{
   private readonly IMarketDataService marketData;

   public ExchangeAPI(IMarketDataService marketData)
   {
      marketData = marketData;
   }
}
```

2. Interface-based Design
```csharp
public interface IMarketDataService { }
public interface IExchangeAPI { }
```


3. Avoid Service Locator Pattern
```csharp
// Bad
var service = ServiceLocator.Current.GetInstance<IMarketDataService>();
// Good
public ExchangeAPI(IMarketDataService marketData) { }
```


## Testing with DI

```csharp
// Mock service
var mockMarketData = new Mock<IMarketDataService>();
mockMarketData.Setup(x => x.GetPrice()).Returns(100m);
// Inject mock
var api = new ExchangeAPI(mockMarketData.Object);
```

## References
- [MS Docs - Dependency Injection](https://docs.microsoft.com/en-us/dotnet/core/extensions/dependency-injection)
- [DI Guidelines](https://docs.microsoft.com/en-us/dotnet/architecture/modern-web-apps-azure/architectural-principles#dependency-inversion)