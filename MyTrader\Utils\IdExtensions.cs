using System;

namespace MyTraderSpace.Utils
{
    public static class IdExtensions
    {
        /// <summary>
        /// Returns a shortened string representation of a Guid.
        /// </summary>
        /// <param name="guid">The Guid to shorten.</param>
        /// <param name="length">The desired length of the shortened string. Defaults to 6.</param>
        /// <returns>A string representing the first 'length' characters of the Guid's N format.</returns>
        public static string ToShortId(this Guid guid, int length = 6)
        {
            if (length <= 0) 
                return string.Empty;
            
            string nFormat = guid.ToString("N"); // 32 characters, no hyphens
            return nFormat.Length >= length ? nFormat.Substring(0, length) : nFormat;
        }
    }
} 