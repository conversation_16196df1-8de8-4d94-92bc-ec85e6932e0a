using System;
using System.Collections.Generic; // Added for List
using MyTraderSpace.Exchanges; // For ExchangeState

namespace MyTraderSpace.Models
{
    /// <summary>
    /// Represents a snapshot of strategy and market data for UI display.
    /// Uses nullable types for flexibility if data is not yet available.
    /// </summary>
    public class StrategyUIData
    {
        // Status
        public string StrategyStatus { get; set; } = "Initializing";
        public ExchangeState APIStatus { get; set; } = ExchangeState.Initializing;

        // Market Data
        public decimal? SpotLastPrice { get; set; }
        public decimal? SpotBid { get; set; }
        public decimal? SpotAsk { get; set; }
        public decimal? FuturesLastPrice { get; set; }
        public decimal? FuturesMarkPrice { get; set; }
        public decimal? FuturesBid { get; set; }
        public decimal? FuturesAsk { get; set; }
        public decimal? FuturesFundingRate { get; set; }

        // Position Data
        public decimal? LongPositionSize { get; set; }
        public decimal? LongPositionEntry { get; set; }
        public decimal? ShortPositionSize { get; set; }
        public decimal? ShortPositionEntry { get; set; }

        // PnL & Wallet
        public decimal? TotalEquity { get; set; }
        public decimal? UnrealizedPnL { get; set; } // Exchange reported Unrealized PnL for open positions
        public decimal? RealizedPnL { get; set; } // Exchange reported session/overall Realized PnL for the account

        // Detailed Wallet Balances
        // TODO public decimal? AccountInitialMarginRate { get; set; } 
        // TODO public decimal? AccountMaintenanceMarginRate { get; set; }
        public decimal? TotalWalletBalance { get; set; } // Sum of all assets in the wallet, usually in USD value. This is the "hard" balance.
        public decimal? TotalAvailableBalance { get; set; } // Portion of TotalWalletBalance available for new trades.
        public decimal? USDTWalletBalance { get; set; } // Specifically the total amount of USDT held.
        public decimal? USDTAvailableBalance { get; set; } // Specifically the available amount of USDT.
        // Strategy-calculated PnL and Fee metrics
        public decimal? StrategyCalculatedRealizedPnL { get; set; }
        public decimal? PeakStrategyRealizedPnL { get; set; }
        public decimal? TroughStrategyRealizedPnL { get; set; }
        public decimal? TotalStrategyFees { get; set; } // Quote currency

        // Strategy Specific (HedgeGrid)
        public int? OpenStepCount { get; set; }
        public decimal? HighestStepPrice { get; set; }
        public decimal? LowestStepPrice { get; set; }
        public List<string>? LastFiveStepsInfo { get; set; } // Added property
        public List<string>? LastErrorMessages { get; set; } // Added for recent error display

        // Add other relevant properties as needed...
        public DateTime Timestamp { get; set; } = DateTime.UtcNow;
    }
} 