using MyTraderSpace.Models;

namespace MyTraderSpace.Exchanges
{
    public interface IMarketDataService : IDisposable
    {
        //Type ExchangeType { get; }
        ExchangeType ExchangeType { get; }
        bool IsInitialized { get; }
        IObservable<SpotMarketData> SpotMarketData { get; }
        IObservable<FuturesMarketData> FuturesMarketData { get; }
        (SpotMarketData? Spot, FuturesMarketData? Futures) GetLatestData();
        Task InitializeAsync();
        Task StopAsync();
        
        // Added properties and methods
        bool SupportsSpot { get; }
        bool SupportsFutures { get; }
        IDisposable SubscribeToSpotMarketData(CurrencyPair pair, Action<SpotMarketData> callback);
        IDisposable SubscribeToFuturesMarketData(CurrencyPair pair, Action<FuturesMarketData> callback);
    }
} 