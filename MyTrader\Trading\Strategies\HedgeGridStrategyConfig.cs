using MyTraderSpace.Logging;
using MyTraderSpace.Models; // For TimeInForce, OrderType

namespace MyTraderSpace.Trading.Strategies
{
    public class HedgeGridStrategyConfig
    {
        public LogLevel LogLevel { get; set; } = LogLevel.Information; // Default log level
        // Symbol is inherited from BaseExchangeAPI.TradingPair

        public decimal StepSize { get; set; } = 100m; // e.g., 100 USDT
        public decimal PositionSizePerStep { get; set; } = 0.01m; // e.g., 0.01 BTC
        public decimal ProfitTargetDistance { get; set; } = 200m; // e.g., 200 USDT from entry
        public decimal Leverage { get; set; } = 100m; // Default leverage, e.g., 100x

        public TimeInForce BaseOrderTimeInForce { get; set; } = TimeInForce.GoodTillCancel;
        public TimeInForce TakeProfitOrderTimeInForce { get; set; } = TimeInForce.GoodTillCancel;
        public OrderType BaseOrderType { get; set; } = OrderType.Market; // This is done as Market only if very first 'Initial Blank Slate'
        public OrderType TakeProfitOrderType { get; set; } = OrderType.Limit;
        public decimal BaseOrderLimitPriceAdjustment { get; set; } = 0m; // pretty much legacy

        // Ratio of liquidation price to mark price that triggers a stop for this strategy instance
        public decimal StopOnLiquidationPriceRatio { get; set; } = 0.8m; // e.g., stop if 80% towards liquidation

        // Tolerance for price deviation when making trade decisions (e.g., reopening, initial placement)
        public decimal MaxInitialStepSpreadTolerancePercentage { get; set; } = 0.0002m; // 0.02% default

        // New properties for self-managed order placement
        public int PlacementMaxAttempts { get; set; } = 5; // How many times HGS will try to place its orders.
        public int PlacementAttemptDelayMs { get; set; } = 3000; // Delay between these attempts.
        public int PlacementOverallTimeoutMs { get; set; } = 30000; // Overall timeout for HGS to achieve its successful order placement.
        public bool AggressiveGapFilling { get; set; } = false;
        public bool AutoPostOnlyFyOrders { get; set; } = false; // Automatically try to re-place non-postonly orders as postonly
        public decimal PostOnlyRePlaceMinNudgePercentage { get; set; } = 0.0001m; // 0.01% - Percentage of bid/ask to nudge price deeper for PostOnly re-placement
    }
}