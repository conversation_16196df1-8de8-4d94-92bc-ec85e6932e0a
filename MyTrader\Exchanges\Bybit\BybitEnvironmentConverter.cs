using System.Text.Json;
using System.Text.Json.Serialization;
using Bybit.Net;

namespace MyTraderSpace.Exchanges.Bybit
{
    /// <summary>
    /// JSON converter for BybitEnvironment to handle deserialization from string or object
    /// </summary>
    public class BybitEnvironmentConverter : JsonConverter<BybitEnvironment>
    {
        public override BybitEnvironment Read(ref Utf8JsonReader reader, Type typeToConvert, JsonSerializerOptions options)
        {
            if (reader.TokenType == JsonTokenType.String)
            {
                string? envName = reader.GetString();
                return envName switch
                {
                    "Live" => BybitEnvironment.Live,
                    "Demo" or "DemoTrading" => BybitEnvironment.DemoTrading,
                    "Testnet" => BybitEnvironment.Testnet,
                    _ => BybitEnvironment.DemoTrading // Default to demo trading
                };
            }
            else if (reader.TokenType == JsonTokenType.StartObject)
            {
                // Read the JSON document into a JsonDocument
                using var jsonDoc = JsonDocument.ParseValue(ref reader);
                var rootElement = jsonDoc.RootElement;
                
                // Check if there's a "Name" property
                if (rootElement.TryGetProperty("Name", out var nameElement))
                {
                    string? envName = nameElement.GetString();
                    return envName switch
                    {
                        "Live" => BybitEnvironment.Live,
                        "Demo" or "DemoTrading" => BybitEnvironment.DemoTrading,
                        "Testnet" => BybitEnvironment.Testnet,
                        _ => BybitEnvironment.DemoTrading // Default to demo trading
                    };
                }
            }
            
            // Default to demo trading
            return BybitEnvironment.DemoTrading;
        }
        
        public override void Write(Utf8JsonWriter writer, BybitEnvironment value, JsonSerializerOptions options)
        {
            // Write as a string
            writer.WriteStringValue(value.Name);
        }
    }
} 