using Serilog;
using Serilog.Core;
using Serilog.Events;
using System;

namespace MyTraderSpace.Logging
{
    public enum LogLevel
    {
        Verbose,
        Debug,
        Information,
        Warning,
        Error,
        Fatal
    }

    public class LogManager : IDisposable
    {
        private readonly ILogger _logger;
        private LogLevel _minimumLevel;
        private readonly string _loggerName;
        private bool _isDisposed;

        // Static action for global log redirection
        private static Action<LogLevel, string, string, Exception?>? _globalLogAction;

        /// <summary>
        /// Sets a global action to be called for every log message, in addition to standard logging.
        /// Useful for tests or centralized log collection.
        /// </summary>
        /// <param name="logAction">The action to call. Parameters are: LogLevel, LoggerName, FormattedMessage, Exception (optional).</param>
        public static void SetGlobalLogger(Action<LogLevel, string, string, Exception?>? logAction)
        {
            _globalLogAction = logAction;
        }

        public LogManager(
            string loggerName,
            LogLevel minimumLevel = LogLevel.Information,
            bool enableConsole = true,
            bool enableFile = true,
            string? filePath = null,
            RollingInterval rollingInterval = RollingInterval.Day
        )
        {
            _loggerName = loggerName;
            _minimumLevel = minimumLevel;
            
            var logConfig = new LoggerConfiguration()
                .MinimumLevel.Is(_minimumLevel.ConvertToSerilogLevel())
                .Enrich.WithProperty("LoggerName", _loggerName);

            if (enableConsole)
            {
                logConfig.WriteTo.Console(outputTemplate:
                    "[{Timestamp:HH:mm:ss} {Level:u3} {LoggerName}] {Message:lj}{NewLine}{Exception}");
            }

            string? finalLogPath = null;
            if (enableFile)
            {
                if (string.IsNullOrWhiteSpace(filePath))
                {
                    finalLogPath = System.IO.Path.Combine("logs", _loggerName, $"{_loggerName}-.log");
                    Console.WriteLine($"[Info] LogManager: FilePath not specified for '{_loggerName}', using automatic path: {finalLogPath}");
                }
                else
                {
                    finalLogPath = System.IO.Path.Combine("logs", filePath.TrimStart('/', '\\'));
                    Console.WriteLine($"[Info] LogManager: Using explicit FilePath for '{_loggerName}', relative to logs/: {filePath} -> {finalLogPath}");
                }
                 
                try
                {
                    string? directory = System.IO.Path.GetDirectoryName(finalLogPath);
                    if (!string.IsNullOrEmpty(directory) && !System.IO.Directory.Exists(directory))
                    {
                        Console.WriteLine($"[Info] LogManager: Creating log directory: {directory}");
                        System.IO.Directory.CreateDirectory(directory);
                    }

                    logConfig.WriteTo.File(
                        finalLogPath,
                        rollingInterval: rollingInterval,
                        outputTemplate: "[{Timestamp:yyyy-MM-dd HH:mm:ss.fff zzz} {Level:u3} {LoggerName}] {Message:lj}{NewLine}{Exception}",
                        retainedFileCountLimit: 31,
                        shared: true
                    );
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"[Error] Failed to configure file logging for path '{finalLogPath}': {ex.Message}. File logging disabled.");
                    enableFile = false;
                    finalLogPath = null;
                }
            }

            _logger = logConfig.CreateLogger();
            
            Information($"Logger '{_loggerName}' initialized. Level: {_minimumLevel}, Console: {enableConsole}, File: {enableFile}, Path: {(finalLogPath ?? "N/A")}, Interval: {rollingInterval}");
        }

        public void Debug(string messageTemplate, params object[] propertyValues)
        {
            if (_minimumLevel <= LogLevel.Debug)
            {
                _logger.Debug(messageTemplate, propertyValues);
                _globalLogAction?.Invoke(LogLevel.Debug, _loggerName, FormatMessage(messageTemplate, propertyValues), null);
            }
        }

        public void Information(string messageTemplate, params object[] propertyValues)
        {
            if (_minimumLevel <= LogLevel.Information)
            {
                _logger.Information(messageTemplate, propertyValues);
                _globalLogAction?.Invoke(LogLevel.Information, _loggerName, FormatMessage(messageTemplate, propertyValues), null);
            }
        }

        public void Warning(string messageTemplate, params object[] propertyValues)
        {
            if (_minimumLevel <= LogLevel.Warning)
            {
                _logger.Warning(messageTemplate, propertyValues);
                _globalLogAction?.Invoke(LogLevel.Warning, _loggerName, FormatMessage(messageTemplate, propertyValues), null);
            }
        }

        public void Error(string messageTemplate, params object[] propertyValues)
        {
            if (_minimumLevel <= LogLevel.Error)
            {
                _logger.Error(messageTemplate, propertyValues);
                _globalLogAction?.Invoke(LogLevel.Error, _loggerName, FormatMessage(messageTemplate, propertyValues), null);
            }
        }

        public void Error(Exception ex, string messageTemplate, params object[] propertyValues)
        {
            if (_minimumLevel <= LogLevel.Error)
            {
                _logger.Error(ex, messageTemplate, propertyValues);
                _globalLogAction?.Invoke(LogLevel.Error, _loggerName, FormatMessage(messageTemplate, propertyValues), ex);
            }
        }

        public void Warning(Exception ex, string messageTemplate, params object[] propertyValues)
        {
            if (_minimumLevel <= LogLevel.Warning)
            {
                _logger.Warning(ex, messageTemplate, propertyValues);
                _globalLogAction?.Invoke(LogLevel.Warning, _loggerName, FormatMessage(messageTemplate, propertyValues), ex);
            }
        }

        public void Verbose(string messageTemplate, params object[] propertyValues)
        {
            if (_minimumLevel <= LogLevel.Verbose)
            {
                _logger.Verbose(messageTemplate, propertyValues);
                _globalLogAction?.Invoke(LogLevel.Verbose, _loggerName, FormatMessage(messageTemplate, propertyValues), null);
            }
        }

        public void Verbose(Exception ex, string messageTemplate, params object[] propertyValues)
        {
            if (_minimumLevel <= LogLevel.Verbose)
            {
                _logger.Verbose(ex, messageTemplate, propertyValues);
                _globalLogAction?.Invoke(LogLevel.Verbose, _loggerName, FormatMessage(messageTemplate, propertyValues), ex);
            }
        }

        public void SetLogLevel(LogLevel level)
        {
            _minimumLevel = level;
        }

        public LogLevel GetLogLevel()
        {
            return _minimumLevel;
        }

        public void Dispose()
        {
            if (_isDisposed) return;
            
            _isDisposed = true;
        }

        // Helper method to format messages
        private string FormatMessage(string messageTemplate, params object[] propertyValues)
        {
            try
            {
                // Serilog's message template format is slightly different from string.Format
                // For simplicity here, we'll attempt a basic format.
                // A more robust solution might involve using Serilog's internal formatter if accessible,
                // or a simpler convention if complex template features aren't used with the global logger.
                if (propertyValues == null || propertyValues.Length == 0)
                {
                    return messageTemplate;
                }
                // This is a naive replacement. Serilog handles structured logging differently.
                // If propertyValues are named structures, this won't render them as Serilog does.
                return string.Format(messageTemplate.Replace("{", "{{").Replace("}", "}}").Replace("{{@", "{").Replace("}}", "}"), propertyValues);
            }
            catch (FormatException ex)
            {
                // Fallback if formatting fails
                return $"Error formatting log message '{messageTemplate}': {ex.Message} | Args: {string.Join(", ", propertyValues)}";
            }
        }
    }

    public static class LogManagerExtensions
    {
        public static LogEventLevel ConvertToSerilogLevel(this LogLevel level)
        {
            return level switch
            {
                LogLevel.Verbose => LogEventLevel.Verbose,
                LogLevel.Debug => LogEventLevel.Debug,
                LogLevel.Information => LogEventLevel.Information,
                LogLevel.Warning => LogEventLevel.Warning,
                LogLevel.Error => LogEventLevel.Error,
                LogLevel.Fatal => LogEventLevel.Fatal,
                _ => LogEventLevel.Information
            };
        }
    }
} 