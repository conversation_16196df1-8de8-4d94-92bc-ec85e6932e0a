# Hedge Grid Strategy Description

This document outlines the implemented logic for the Hedge Grid trading strategy, designed for perpetual futures markets (e.g., BTC/USDT) on exchanges configured for **Hedge Mode (BothSides)**.

## I. Core Concepts

### 1. HedgeGridStrategy (HGS) - The Building Block
Each `HedgeGridStrategy` (HGS) instance represents a potential price level in the grid. It manages a pair of orders: one long (`LongSide`) and one short (`ShortSide`), centered around its `IntendedPrice`.

*   **OrderPair:** Each `OrderPair` (Long or Short) within an HGS is responsible for:
    *   Placing and managing its "Base" order (to enter a position).
    *   Placing and managing its "Take Profit" (TP) order (to exit the position with profit).
    *   It handles retries and timeouts for order placements as defined in `HedgeGridStrategyConfig`.
*   **Self-Contained Logic:** An HGS aims to maintain its hedged position. If its base order fills, it places a TP. If its TP fills, it attempts to re-place its base order.

### 2. MainStrategy - The Orchestrator
`MainStrategy` manages a pool of HGS instances (`_strategyPool`) and orchestrates the overall grid.

*   **API Pool:** `MainStrategy` receives a pool of `BaseExchangeAPI` instances. Each HGS, upon creation, consumes one API. When an HGS is removed, its API is released back to the pool.
*   **`CurrentStrategy`:** This is the HGS instance considered the "frontier" or the most relevant to the current market price. It's typically the active HGS closest to the market. Market data ticks from `CurrentStrategy` drive `MainStrategy`'s decisions when one is active.
*   **Global Market Data:** If `CurrentStrategy` is `null`, `MainStrategy` listens to a global market data feed.
*   **`EvaluateAndManageGridAsync()`:** This is the core decision-making method in `MainStrategy`. It's triggered by market data updates and significant strategy events. Its primary responsibilities are:
    1.  **Determine `CurrentStrategy`**: Selects the `IsValid`, `IsReady`, and `IsActive()` HGS closest to the current market price.
    2.  **Hole Filling / Extension**: If the market price moves a `StepSize` or more away from `CurrentStrategy.IntendedPrice`, `MainStrategy` attempts to create a new HGS in that direction, provided an API is available and a step doesn't already exist there.

### 3. Key HGS States & Properties
*   **`IntendedPrice`**: The target price level for an HGS.
*   **`IsValid`**: Indicates if the HGS is properly initialized and not in a permanent error state.
*   **`IsReady`**: Indicates the HGS is initialized, valid, and ready to attempt order placements or manage existing orders/positions.
*   **`IsPlaced`**: (Concept primarily for pruning) True if both base orders are active (e.g., 'New', 'PartiallyFilled') but not necessarily fully filled.
*   **`IsActivated`**: True if *at least one* of its base orders has `Filled`. This is a key state for an HGS to be considered operational and a candidate for some logic, but `IsActive()` is more critical for grid management.
    *   `OnActivated` event: Fired by HGS the *first time* it becomes `IsActive()`. `MainStrategy` uses this to prune and expand the grid.
*   **`IsActive()`**: True if *both* its long and short positions are open (i.e., both base orders have filled at some point and resulted in open positions).
*   **`IsWasBothBaseOrdersFilledFirstTime`**: A crucial flag within an HGS. It's `true` only for the *very first time* the HGS becomes `IsActive()`.
    *   **Significance**: In `MainStrategy.HandleActivated`, if an HGS reports this flag as `true`, `MainStrategy` will:
        1.  Prune distant, non-activated HGS instances.
        2.  Set this HGS as the `CurrentStrategy`.
        3.  Attempt to create new HGS steps one `StepSize` above and below this newly, fully filled HGS.
    *   If this flag is `false` (meaning the HGS has cycled through fills before), `MainStrategy` does not perform pruning or create new flanking steps based on this specific activation event.

## II. Strategy Lifecycle & Flow

### 1. Initialization & Reconstruction
*   **ExchangeTrader:** Initializes `BaseExchangeAPI` instances based on `config.json` and provides them to `MainStrategy`.
*   **MainStrategy `InitializeAsync()`:**
    1.  Filters the provided APIs based on `MainStrategyConfig`.
    2.  For each API designated for use:
        *   Attempts to reconstruct an HGS if existing orders/positions are found on the exchange for that API's `TradingPair`.
        *   HGS reconstruction uses timestamps of orders/positions to infer the sequence and `IntendedPrice`. `HedgeGridStrategy.ReConstructStateAsync()` handles this.
        *   A reconstructed HGS is added to `_strategyPool`, and its API is marked as used.
    3.  If, after reconstruction, `_strategyPool` is empty (or no suitable `CurrentStrategy` can be found), `MainStrategy` prepares for a "Blank Slate Start," which is triggered by `EvaluateAndManageGridAsync`.
    4.  Subscribes to the global market data feed (`_marketDataService`) via `ProcessGlobalMarketData`.
    5.  Calls `EvaluateAndManageGridAsync()` to set the initial `CurrentStrategy` and assess the grid.

### 2. Blank Slate Start (`HandleBlankSlateStartAsync`)
*   This occurs if `EvaluateAndManageGridAsync` finds no suitable active strategies.
*   If a blank slate is appropriate:
    1.  Acquires an available API.
    2.  Determines an `initialIntendedPrice` from the current market (avg bid/ask, mark price, or last price).
    3.  Creates a new HGS with this `initialIntendedPrice` and sets `isInitialBlankSlateStep = true`.
    4.  Calls `StartAsync()` on this new HGS. The HGS's `OrderPair` instances will use `Market` orders (as per config for initial steps) for their base orders due to `isInitialBlankSlateStep`.
    5.  If this HGS becomes `Active()`, its `OnActivated` event will fire, which `MainStrategy.HandleActivated` uses to set it as `CurrentStrategy`.

### 3. Ongoing Operation (`EvaluateAndManageGridAsync`)
Driven by market data ticks (either global or from `CurrentStrategy`):

*   **Part 1: Determine `CurrentStrategy`**:
    *   Filters `_strategyPool` for HGS instances that are `IsValid && IsReady && IsActive()`.
    *   Selects the one closest to `relevantMarketPrice` as the new `CurrentStrategy`.
    *   If `CurrentStrategy` changes to `null` (e.g., the previous one became invalid/stopped, or no suitable HGS found), `MainStrategy` ensures it's subscribed to the global market data feed.
    *   If no suitable HGS is found at all, it calls `HandleBlankSlateStartAsync`.

*   **Part 2: Hole Filling / End Addition (based on `CurrentStrategy`)**:
    *   If `CurrentStrategy` is set and the `distanceToMarket` (between `CurrentStrategy.IntendedPrice` and `relevantMarketPrice`) is `>= StepSize`:
        *   A `newStepTargetPrice` is calculated one `StepSize` away from `CurrentStrategy.IntendedPrice` in the direction of the market.
        *   It checks if an `IsValid` HGS already exists near this `newStepTargetPrice`.
        *   If not, it acquires an API and calls `CreateAndActivateNewHedgeGridStepAsync()` to establish a new HGS there. This new HGS will attempt to place its base orders (typically `Limit PostOnly` as it's not an initial blank slate step).

### 4. HGS Creation and Activation (`CreateAndActivateNewHedgeGridStepAsync`)
1.  A new `HedgeGridStrategy` instance is created with the given `intendedPrice` and an acquired `BaseExchangeAPI`.
2.  The HGS is immediately added to `_strategyPool` (committing the API).
3.  `HGS.InitializeAsync()` is called (which might involve its own reconstruction logic if `intendedPrice` was 0, though typically for new steps it's non-zero).
4.  `HGS.StartAsync()` is called. This triggers the HGS to attempt placing its initial base orders via its `OrderPair` instances.
5.  If the HGS successfully places its orders and eventually becomes `Active()`, its `OnActivated` event will notify `MainStrategy`.

### 5. Key HGS Event Handling in `MainStrategy`
*   **`HandleInitialPlacementFailed`**: If an HGS fails its initial order placements (within `HGS.StartAsync()`), it transitions to an `Error` state. `MainStrategy` ensures this HGS is tracked in `_strategyPool` with its committed API and logs the failure. The HGS remains for manual review.
*   **`HandleHGSError`**: If a running HGS encounters a critical error (e.g., persistent order placement failures reported by `OrderPair`), the HGS transitions to `Error`. `MainStrategy` logs this and retains the HGS in the pool with its API committed. If it was `CurrentStrategy`, `CurrentStrategy` is set to `null`.
*   **`HandleActivated`**:
    *   If `strategy.IsWasBothBaseOrdersFilledFirstTime` is `true`:
        1.  `PruneDistantStrategiesAsync()` is called: This identifies HGS instances in `_strategyPool` that are `!IsActive()`, `State != StrategyState.Error`, and whose `IntendedPrice` is more than `StepSize` away from the newly filled `strategy.IntendedPrice`. These are consolidated (orders cancelled, positions closed if any) and removed.
        2.  The `strategy` becomes the `CurrentStrategy`.
        3.  `MainStrategy` attempts to create new HGS steps one `StepSize` above and below this `strategy`.
    *   If `IsWasBothBaseOrdersFilledFirstTime` is `false`, no pruning or new step creation occurs at the `MainStrategy` level due to this specific event.
*   **`HandleBaseOrderPairFilled`**: This event is informational for `MainStrategy` but doesn't trigger pruning or grid expansion. The core logic for that is in `HandleActivated`.
*   **`HandleStrategyRemovalRequest`**: When an HGS requests its own removal (e.g., after `ConsolidateAsync`), `MainStrategy` removes it from `_strategyPool`, releases its API, and calls `EvaluateAndManageGridAsync()`.

### 6. API Management
*   **`AcquireNextAvailableApiAsync`**:
    1.  Tries to get an API from the `ApiPool` of available APIs.
    2.  If `ApiPool` is empty, calls `GetAndReleaseFirstApiAsync`.
*   **`GetAndReleaseFirstApiAsync`**:
    1.  Identifies the HGS in `_strategyPool` whose `IntendedPrice` is furthest from the current market price (and is not in an `Error` state).
    2.  Calls `ConsolidateAsync()` on this HGS.
    3.  Removes the HGS from `_strategyPool` and adds its API back to the `ApiPool`.
    4.  The released API is then acquired.
*   **`ReleaseApiToPool`**: Adds a specified API back to the `ApiPool`.

### 7. Error Handling
*   Errored HGS instances (`State == StrategyState.Error`) remain in `_strategyPool`.
*   Their APIs remain committed to them.
*   They are **not** automatically consolidated or removed by `ConsolidateNonActivatedStrategiesAsync` or `PruneDistantStrategiesAsync`.
*   They require manual intervention/commands to be cleared or retried.
*   If an errored HGS was `CurrentStrategy`, `CurrentStrategy` is set to `null`, triggering `EvaluateAndManageGridAsync`.

This should provide a comprehensive overview. Let me know if you'd like any section expanded or clarified!
