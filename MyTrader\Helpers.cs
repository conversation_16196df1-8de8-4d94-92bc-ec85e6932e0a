using ExchangeSharp;
using MyTraderSpace.Models;

namespace MyTraderSpace.Utils
{
    public static class Helpers
    {
        public static (string apiKey, string secretKey) LoadAPIKeys(string keysFile)
        {
            try
            {
                var secureStrings = CryptoUtility.LoadProtectedStringsFromFile(keysFile);

                if (secureStrings.Length < 2)
                {
                    throw new InvalidOperationException(
                        "Encrypted keys file should contain both API key and Secret key"
                    );
                }

                // Use CryptoUtility's built-in method to convert SecureString to string
                string apiKey = secureStrings[0].ToUnsecureString();
                string secretKey = secureStrings[1].ToUnsecureString();

                return (apiKey, secretKey);
            }
            catch (FileNotFoundException)
            {
                throw new FileNotFoundException($"Keys file not found: {keysFile}");
            }
            catch (Exception ex) when (ex is not InvalidOperationException)
            {
                throw new InvalidOperationException($"Error loading keys from file: {ex.Message}");
            }
        }

        // Add coin parsing helpers
        public static bool TryParseCoin(string? value, out CoinType coinType)
        {
            if (string.IsNullOrEmpty(value))
            {
                coinType = default;
                return false;
            }
            return Enum.TryParse(value, true, out coinType);
        }

        public static CoinType ParseCoin(string value)
        {
            if (TryParseCoin(value, out var coinType))
                return coinType;
            throw new ArgumentException($"Invalid coin type: {value}", nameof(value));
        }

        public static PositionModel ToPositionModel(this PositionModelUpdate update)
        {
            return new PositionModel
            {
                Symbol = update.Symbol,
                Direction = update.Direction,
                Side = update.Side,
                Quantity = update.Quantity,
                AveragePrice = update.AveragePrice,
                MarkPrice = update.MarkPrice,
                LiquidationPrice = update.LiquidationPrice,
                Leverage = update.Leverage,
                UnrealizedPnl = update.UnrealizedPnl,
                RealizedPnl = update.RealizedPnl,
                PositionValue = update.PositionValue,
                Category = update.Category,
                PositionMode = update.PositionMode,
                TradeMode = update.TradeMode,
                PositionStatus = update.PositionStatus,
                BustPrice = update.BustPrice,
                InitialMargin = update.InitialMargin,
                MaintenanceMargin = update.MaintenanceMargin,
                TakeProfit = update.TakeProfit,
                StopLoss = update.StopLoss,
                CurrentRealizedPnl = update.CurrentRealizedPnl,
                AutoAddMargin = update.AutoAddMargin,
                PositionBalance = update.PositionBalance,
                UpdateTime = update.UpdateTime,
                CreateTime = update.CreateTime
            };
        }

        public static OrderModelUpdate ToOrderModelUpdate(this OrderModel order)
        {
            return new OrderModelUpdate
            {
                OrderId = order.OrderId,
                ClientOrderId = order.ClientOrderId,
                Symbol = order.Symbol,
                Category = order.Category,
                Price = order.Price,
                Quantity = order.Quantity,
                Side = order.Side,
                PositionDirection = order.PositionDirection,
                IsLeverage = order.IsLeverage,
                Status = order.Status,
                RejectReason = order.RejectReason,
                AveragePrice = order.AveragePrice,
                QuantityRemaining = order.QuantityRemaining,
                ValueRemaining = order.ValueRemaining,
                QuantityFilled = order.QuantityFilled,
                ValueFilled = order.ValueFilled,
                ExecutedFee = order.ExecutedFee,
                TimeInForce = order.TimeInForce,
                OrderType = order.OrderType,
                IsReduceOnly = order.IsReduceOnly,
                TriggerPrice = order.TriggerPrice,
                TriggerBy = order.TriggerBy,
                TriggerDirection = order.TriggerDirection,
                TakeProfit = order.TakeProfit,
                StopLoss = order.StopLoss,
                UpdateTime = order.UpdateTime,
                TakeProfitLimitPrice = order.TakeProfitLimitPrice,
                StopLossLimitPrice = order.StopLossLimitPrice,
                CreateTime = order.CreateTime
            };
        }

        public static PositionModelUpdate ToPositionModelUpdate(this PositionModel position)
        {
            return new PositionModelUpdate
            {
                Symbol = position.Symbol,
                Direction = position.Direction,
                Side = position.Side,
                Quantity = position.Quantity,
                AveragePrice = position.AveragePrice,
                MarkPrice = position.MarkPrice,
                LiquidationPrice = position.LiquidationPrice,
                Leverage = position.Leverage,
                UnrealizedPnl = position.UnrealizedPnl,
                RealizedPnl = position.RealizedPnl,
                PositionValue = position.PositionValue,
                Category = position.Category,
                PositionMode = position.PositionMode,
                TradeMode = position.TradeMode,
                PositionStatus = position.PositionStatus,
                BustPrice = position.BustPrice,
                InitialMargin = position.InitialMargin,
                MaintenanceMargin = position.MaintenanceMargin,
                TakeProfit = position.TakeProfit,
                StopLoss = position.StopLoss,
                CurrentRealizedPnl = position.CurrentRealizedPnl,
                AutoAddMargin = position.AutoAddMargin,
                PositionBalance = position.PositionBalance,
                UpdateTime = position.UpdateTime,
                CreateTime = position.CreateTime
                // TODO / Note: PositionModelUpdate might have more fields than PositionModel in the future or vice-versa.
                // Ensure all relevant fields are mapped.
            };
        }

        public static OrderModel ToOrderModel(this OrderModelUpdate update, OrderModel? existingOrder = null)
        {
            if (update == null) return null;

            OrderModel result = existingOrder ?? new OrderModel();

            result.OrderId = update.OrderId;
            result.ClientOrderId = update.ClientOrderId;
            result.Symbol = update.Symbol;
            result.Category = update.Category;
            result.Price = update.Price;
            result.Quantity = update.Quantity;
            result.Side = update.Side;
            result.PositionDirection = update.PositionDirection;
            result.IsLeverage = update.IsLeverage;
            result.Status = update.Status;
            result.RejectReason = update.RejectReason;
            result.AveragePrice = update.AveragePrice;
            result.QuantityRemaining = update.QuantityRemaining;
            result.ValueRemaining = update.ValueRemaining;
            result.QuantityFilled = update.QuantityFilled;
            result.ValueFilled = update.ValueFilled;
            result.ExecutedFee = update.ExecutedFee;
            result.TimeInForce = update.TimeInForce;
            result.OrderType = update.OrderType;
            result.IsReduceOnly = update.IsReduceOnly;
            result.TriggerPrice = update.TriggerPrice;
            result.TriggerBy = update.TriggerBy;
            result.TriggerDirection = update.TriggerDirection;
            result.TakeProfit = update.TakeProfit;
            result.StopLoss = update.StopLoss;
            result.UpdateTime = update.UpdateTime;
            result.TakeProfitLimitPrice = update.TakeProfitLimitPrice;
            result.StopLossLimitPrice = update.StopLossLimitPrice;
            result.CreateTime = update.CreateTime;
            // Note: ClosedPnl is specific to OrderModelUpdate and not included in OrderModel

            return result;
        }

        public static OrderModelUpdate? ToOrderModelUpdate(this OrderModel? order, OrderModelUpdate? existingUpdate = null)
        {
            if (order == null) return null;

            OrderModelUpdate result = existingUpdate ?? new OrderModelUpdate();

            result.OrderId = order.OrderId;
            result.ClientOrderId = order.ClientOrderId;
            result.Symbol = order.Symbol;
            result.Category = order.Category;
            result.Price = order.Price;
            result.Quantity = order.Quantity;
            result.Side = order.Side;
            result.PositionDirection = order.PositionDirection;
            result.IsLeverage = order.IsLeverage;
            result.Status = order.Status;
            result.RejectReason = order.RejectReason;
            result.AveragePrice = order.AveragePrice;
            result.QuantityRemaining = order.QuantityRemaining;
            result.ValueRemaining = order.ValueRemaining;
            result.QuantityFilled = order.QuantityFilled;
            result.ValueFilled = order.ValueFilled;
            result.ExecutedFee = order.ExecutedFee;
            result.TimeInForce = order.TimeInForce;
            result.OrderType = order.OrderType;
            result.IsReduceOnly = order.IsReduceOnly;
            result.TriggerPrice = order.TriggerPrice;
            result.TriggerBy = order.TriggerBy;
            result.TriggerDirection = order.TriggerDirection;
            result.TakeProfit = order.TakeProfit;
            result.StopLoss = order.StopLoss;
            result.UpdateTime = order.UpdateTime;
            result.TakeProfitLimitPrice = order.TakeProfitLimitPrice;
            result.StopLossLimitPrice = order.StopLossLimitPrice;
            result.CreateTime = order.CreateTime;
            result.Timestamp = DateTime.UtcNow; // Set current timestamp for the update
            // Note: ClosedPnl is specific to OrderModelUpdate and not included in OrderModel
            // If existingUpdate is provided, we preserve its ClosedPnl value

            return result;
        }

        // Important Note: This is very specific for reconstructing OrderModelUpdate from PositionModel, not generally correct (not even possible to be fully correct)
        // Weakest points: OrderType, TimeInForce
        public static OrderModelUpdate ToOrderModelUpdate(this PositionModel position)
        {
            return new OrderModelUpdate
            {
                OrderId = "No_Id_From_Position",
                ClientOrderId = "No_Cid_From_Position",
                Symbol = position.Symbol,
                Category = position.Category,
                Price = position.AveragePrice,
                Quantity = position.Quantity,
                Side = position.Side == PositionSide.Buy ? OrderSide.Buy : OrderSide.Sell,
                PositionDirection = position.Direction,
                IsLeverage = position.Leverage.HasValue,
                Status = OrderStatus.Filled,
                RejectReason = null, // No reject reason for filled positions
                AveragePrice = position.AveragePrice,
                QuantityRemaining = null,
                ValueRemaining = null,
                QuantityFilled = position.Quantity,
                ValueFilled = position.PositionValue,
                TimeInForce = TimeInForce.GoodTillCancel, // Default to GTC // NOTE: *Weak point* no proper reconstruction possibility, and hopefully usually this is PostOnly
                OrderType = Models.OrderType.Limit, // Default to Limit // NOTE: *Weak point* no proper reconstruction possibility!
                TriggerPrice = null, // No trigger price for positions
                TriggerBy = null,
                TriggerDirection = null,
                IsReduceOnly = false, // Default to false because 'this is a BaseOrder', and only TP orders are reduce-only
                TakeProfit = position.TakeProfit,
                StopLoss = position.StopLoss,
                UpdateTime = DateTime.UtcNow, // Use current time for update
                TakeProfitLimitPrice = position.TakeProfit, //position.TakeProfitLimitPrice, // maybe should exist
                StopLossLimitPrice = position.StopLoss, //position.StopLossLimitPrice, // maybe should exist
                ExecutedFee = null, // new Fee(), // No correspondence
                ClosedPnl = position.RealizedPnl, // Use RealizedPnl as ClosedPnl
                CreateTime = position.CreateTime ?? DateTime.UtcNow,
                Timestamp = position.UpdateTime ?? DateTime.UtcNow // Use UpdateTime or current time if null
            };
        }
    }
}