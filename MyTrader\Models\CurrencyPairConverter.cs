using System.Text.Json;
using System.Text.Json.Serialization;
using MyTraderSpace.Models;

namespace MyTraderSpace.Models
{
    /// <summary>
    /// JSON converter for CurrencyPair to handle deserialization from JSON
    /// </summary>
    public class CurrencyPairConverter : JsonConverter<CurrencyPair>
    {
        public override CurrencyPair Read(ref Utf8JsonReader reader, Type typeToConvert, JsonSerializerOptions options)
        {
            if (reader.TokenType == JsonTokenType.String)
            {
                // If it's a string, parse it as a symbol
                string? symbol = reader.GetString();
                if (string.IsNullOrEmpty(symbol))
                    return CommonPairs.BTCUSDT; // Default to BTCUSDT
                
                return CommonPairs.ParseSymbolToCurrencyPair(symbol);
            }
            else if (reader.TokenType == JsonTokenType.StartObject)
            {
                // Read the JSON document into a JsonDocument
                using var jsonDoc = JsonDocument.ParseValue(ref reader);
                var rootElement = jsonDoc.RootElement;
                
                // Extract the properties
                CoinType baseCoin = CoinType.BTC;
                CoinType quoteCoin = CoinType.USDT;
                int basePrecision = 6;
                int quotePrecision = 2;
                
                if (rootElement.TryGetProperty("BaseCoin", out var baseCoinElement))
                {
                    if (baseCoinElement.ValueKind == JsonValueKind.String)
                    {
                        string? baseCoinStr = baseCoinElement.GetString();
                        if (!string.IsNullOrEmpty(baseCoinStr) && Enum.TryParse<CoinType>(baseCoinStr, out var parsedBaseCoin))
                        {
                            baseCoin = parsedBaseCoin;
                        }
                    }
                    else if (baseCoinElement.ValueKind == JsonValueKind.Number)
                    {
                        int baseCoinInt = baseCoinElement.GetInt32();
                        if (Enum.IsDefined(typeof(CoinType), baseCoinInt))
                        {
                            baseCoin = (CoinType)baseCoinInt;
                        }
                    }
                }
                
                if (rootElement.TryGetProperty("QuoteCoin", out var quoteCoinElement))
                {
                    if (quoteCoinElement.ValueKind == JsonValueKind.String)
                    {
                        string? quoteCoinStr = quoteCoinElement.GetString();
                        if (!string.IsNullOrEmpty(quoteCoinStr) && Enum.TryParse<CoinType>(quoteCoinStr, out var parsedQuoteCoin))
                        {
                            quoteCoin = parsedQuoteCoin;
                        }
                    }
                    else if (quoteCoinElement.ValueKind == JsonValueKind.Number)
                    {
                        int quoteCoinInt = quoteCoinElement.GetInt32();
                        if (Enum.IsDefined(typeof(CoinType), quoteCoinInt))
                        {
                            quoteCoin = (CoinType)quoteCoinInt;
                        }
                    }
                }
                
                if (rootElement.TryGetProperty("BasePrecision", out var basePrecisionElement))
                {
                    basePrecision = basePrecisionElement.GetInt32();
                }
                
                if (rootElement.TryGetProperty("QuotePrecision", out var quotePrecisionElement))
                {
                    quotePrecision = quotePrecisionElement.GetInt32();
                }
                
                // Create and return the CurrencyPair
                return new CurrencyPair(baseCoin, quoteCoin, basePrecision, quotePrecision);
            }
            
            // Default to BTCUSDT
            return CommonPairs.BTCUSDT;
        }
        
        public override void Write(Utf8JsonWriter writer, CurrencyPair value, JsonSerializerOptions options)
        {
            // Write as an object
            writer.WriteStartObject();
            
            writer.WriteString("BaseCoin", value.BaseCoin.ToString());
            writer.WriteString("QuoteCoin", value.QuoteCoin.ToString());
            writer.WriteNumber("BasePrecision", value.BasePrecision);
            writer.WriteNumber("QuotePrecision", value.QuotePrecision);
            writer.WriteString("Symbol", value.Symbol);
            
            writer.WriteEndObject();
        }
    }
} 