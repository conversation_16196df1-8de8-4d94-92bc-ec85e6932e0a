using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Bybit.Net;
using Bybit.Net.Clients;
using CryptoExchange.Net.Authentication;
using MyTraderSpace.Logging;
using Bybit.Net.Objects.Models.V5;
using MyTraderSpace.Models;
using CryptoExchange.Net.Objects;
using CryptoExchange.Net.Objects.Sockets;
using MyTraderSpace.Utils;

namespace MyTraderSpace.Exchanges.Bybit
{
    public class BybitExchangeAPI : BaseExchangeAPI
    {
        //public Type ExchangeType => typeof(ByBit);
        public override Models.ExchangeType ExchangeType => Models.ExchangeType.Bybit;

        private readonly LogManager _log;
        private readonly BybitExchangeConfig _config;
        private IDisposable? _spotSubscription;
        private IDisposable? _futuresSubscription;
        private bool _isDataStreamConnected;  // For updates/notifications

        // Clients cleanup
        private BybitSocketClient? _subscriptionSocketClient;  // Keep this for data stream
        private BybitRestClient? privateRestClient;

        private UpdateSubscription? _positionSubscription;
        private UpdateSubscription? _orderSubscription;
        private UpdateSubscription? _walletSubscription;

        public override ExchangeState State { get; protected set; } = ExchangeState.Initializing;

        public BybitExchangeAPI(
            string name,
            CurrencyPair tradingPair,
            IMarketDataService marketDataService,
            BybitExchangeConfig config)
            : base(name, tradingPair, marketDataService, new LogManager(name, config.LogLevel), config)
        {
            _log = base._log;
            _config = config ?? throw new ArgumentNullException(nameof(config));
            // Already instantiated in base class
            //Wallet = new Wallet(marketDataService);

            if (!string.IsNullOrEmpty(config.KeysFile))
            {
                var (apiKey, apiSecret) = Helpers.LoadAPIKeys(config.KeysFile);
                InitPrivateClients(apiKey, apiSecret, config.Environment);
            }
            else if (!string.IsNullOrEmpty(config.ApiKey) && !string.IsNullOrEmpty(config.ApiSecret))
            {
                InitPrivateClients(config.ApiKey, config.ApiSecret, config.Environment);
            }
            else
            {
                throw new ArgumentException("Either KeysFile or ApiKey/ApiSecret must be provided");
            }

            if (config.Leverage > 0)
            {
                _currentBuyLeverage = config.Leverage;
                _currentSellLeverage = config.Leverage;
                _log.Information($"Setting Bybit leverage from config: {config.Leverage}");
            }

            _log.Information($"BybitExchangeAPI '{Name}' specific constructor finished.");
        }

        // InitializeAsync initializes private clients and verifies connectivity via a simple API call.
        public override async Task InitializeAsync()
        {
            try
            {
                _log.Information("Setting up market data subscriptions");
                
                // Initialize subscription socket
                if (_subscriptionSocketClient != null)
                {
                    try
                    {
                        var walletSubResult = await _subscriptionSocketClient.V5PrivateApi.SubscribeToWalletUpdatesAsync(data =>
                        {
                            foreach (var balance in data.Data)
                            {
                                var update = UpdateMapping.MapWalletUpdate(balance);
                                _log.Information($"Wallet update: Total Balance: {update.TotalWalletBalance:F2} USD...");
                                HandleWalletUpdate(update);
                            }
                        });

                        if (walletSubResult.Success)
                        {
                            _walletSubscription = walletSubResult.Data;
                            _log.Information("Successfully subscribed to wallet updates");
                        }
                        else
                        {
                            _log.Warning($"Failed to subscribe to wallet updates: {walletSubResult.Error?.Message}");
                        }

                        // Subscribe to position updates
                        var positionSubResult = await _subscriptionSocketClient.V5PrivateApi.SubscribeToPositionUpdatesAsync(data =>
                        {
                            foreach (var pos in data.Data)
                            {
                                var position = UpdateMapping.MapPosition(pos);
                                _log.Information($"Position update received for {position.Symbol}: Size={position.Quantity}, Direction={position.Direction}");
                                HandlePositionUpdate(position);
                            }
                        });

                        if (positionSubResult.Success)
                        {
                            _positionSubscription = positionSubResult.Data;
                            _log.Information("Successfully subscribed to position updates");
                        }
                        else
                        {
                            _log.Warning($"Failed to subscribe to position updates: {positionSubResult.Error?.Message}");
                        }

                        // Subscribe to order updates
                        var orderSubResult = await _subscriptionSocketClient.V5PrivateApi.SubscribeToOrderUpdatesAsync(data =>
                        {
                            foreach (var order in data.Data)
                            {
                                var update = UpdateMapping.MapOrderUpdate(order);
                                _log.Information($"Order update: {update.OrderId} status: {update.Status}, filled: {update.QuantityFilled}");
                                HandleOrderUpdate(update);
                            }
                        });

                        if (orderSubResult.Success)
                        {
                            _orderSubscription = orderSubResult.Data;
                            _log.Information("Successfully subscribed to order updates");
                        }
                        else
                        {
                            _log.Warning($"Failed to subscribe to order updates: {orderSubResult.Error?.Message}");
                        }

                        _isDataStreamConnected = walletSubResult.Success && positionSubResult.Success && orderSubResult.Success;

                        if (_isDataStreamConnected)
                        {
                            _log.Information("Successfully connected to all data streams");
                        }
                        else
                        {
                            _log.Warning("Not all data streams connected successfully");
                        }
                    }
                    catch (Exception ex)
                    {
                        _log.Error(ex, "Failed to set up subscription socket");
                        _isDataStreamConnected = false;
                    }
                }

                // Initialize account settings from exchange
                try
                {
                    // First try to get current positions and orders to track them
                    try
                    {
                        // Get and track existing positions
                        var positions = await GetPositionsAsync();
                        var positionsList = positions.ToList();
                        
                        // Track all open positions in the base class _openPositions collection
                        foreach (var pos in positionsList.Where(p => Math.Abs(p.Quantity) > 0))
                        {
                            string positionKey = GetPositionKey(pos.Symbol, pos.Direction);
                            _openPositions[positionKey] = pos;
                            _log.Information($"Tracking existing position: {pos.Symbol} {pos.Direction}, Size: {pos.Quantity}, Entry: {pos.AveragePrice}");
                        }
                        
                        // Get and track existing active orders
                        try
                        {
                            // Get active orders for both spot and futures
                            var spotOrders = await GetActiveOrdersForCategoryAsync(Models.Category.Spot, TradingPair.Symbol);
                            var futuresOrders = await GetActiveOrdersForCategoryAsync(Models.Category.Linear, TradingPair.Symbol);
                            var allOrders = spotOrders.Concat(futuresOrders).ToList();
                            
                            // Track all active orders in the base class _activeOrders collection
                            //foreach (var order in allOrders)
                            // Only track orders that are not filled, canceled, or rejected
                            foreach (var order in allOrders.Where(o => 
                                o.Status != Models.OrderStatus.Filled && 
                                o.Status != Models.OrderStatus.Cancelled && 
                                o.Status != Models.OrderStatus.Rejected))
                            {
                                _activeOrders[order.OrderId] = order;
                                _log.Information($"Tracking existing order: {order.OrderId}, {order.Symbol} {order.Side}, Status: {order.Status}, Quantity: {order.Quantity}");
                            }
                            
                            _log.Information($"Initialized with {_activeOrders.Count} active orders");
                        }
                        catch (Exception ex)
                        {
                            _log.Warning(ex, "Failed to initialize active orders");
                        }
                        
                        var position = positionsList.FirstOrDefault();
                        
                        if (position != null)
                        {
                            _currentPositionMode = position.PositionMode;
                            _currentTradeMode = position.TradeMode;
                            _currentBuyLeverage = position.Leverage.HasValue ? position.Leverage.Value : _config.Leverage; // TODO 2: check if Bybit actually defines BuyLeverage and SellLeverage and use it
                            _currentSellLeverage = position.Leverage.HasValue ? position.Leverage.Value : _config.Leverage; // TODO 2: check if Bybit actually defines BuyLeverage and SellLeverage and use it
                            _log.Information($"Initialized from exchange - Position mode: {_currentPositionMode}, Trade mode: {_currentTradeMode}, Leverage: {_currentBuyLeverage}x");
                        }
                    }
                    catch (Exception ex)
                    {
                        _log.Warning(ex, "Failed to initialize settings from exchange, using config values");
                    }
                    
                    // Then apply config settings if they differ from current settings
                    if (privateRestClient != null)
                    {
                        // 1. Set margin mode if different
                        if (_currentMarginMode != _config.MarginMode)
                        {
                            var marginModeResult = await privateRestClient.V5Api.Account.SetMarginModeAsync(
                                Mapping.MapToBybitMarginMode(_config.MarginMode));
                            
                            if (marginModeResult.Success)
                            {
                                _currentMarginMode = _config.MarginMode;
                                _log.Information($"Successfully set margin mode to {_currentMarginMode}");
                            }
                            else
                            {
                                _log.Warning($"Failed to set margin mode to {_config.MarginMode}: {marginModeResult.Error?.Message}");
                            }
                        }
                        
                        // 2. Set trade mode if different
                        if (_currentTradeMode != _config.TradeMode)
                        {
                            var tradeModeResult = await privateRestClient.V5Api.Account.SwitchCrossIsolatedMarginAsync(
                                Mapping.MapToBybitCategory(Models.Category.Linear), 
                                TradingPair.Symbol, 
                                Mapping.MapToBybitTradeMode(_config.TradeMode), 
                                _config.Leverage, 
                                _config.Leverage);
                            
                            if (tradeModeResult.Success)
                            {
                                _currentTradeMode = _config.TradeMode;
                                _log.Information($"Successfully set trade mode to {_currentTradeMode}");
                            }
                            else
                            {
                                _log.Warning($"Failed to set trade mode to {_config.TradeMode}: {tradeModeResult.Error?.Message}");
                            }
                        }
                        
                        // 3. Set position mode if different
                        if (_currentPositionMode != _config.PositionMode)
                        {
                            var positionModeResult = await SetPositionModeAsync(_config.PositionMode);
                            
                            if (!positionModeResult.IsSuccess)
                            {
                                _log.Warning($"Failed to set position mode to {_config.PositionMode}: {positionModeResult.Message}");
                            }
                        }
                        
                        // 4. Set leverage if different
                        if (_currentBuyLeverage != _config.Leverage || _currentSellLeverage != _config.Leverage)
                        {
                            var leverageResult = await SetLeverageAsync(_config.Leverage);
                            
                            if (!leverageResult.IsSuccess)
                            {
                                _log.Warning($"Failed to set leverage to {_config.Leverage}: {leverageResult.Message}");
                            }
                        }
                    }
                }
                catch (Exception ex)
                {
                    _log.Warning(ex, "Failed to initialize account settings");
                }

                // Market data subscriptions
                _spotSubscription = _marketDataService.SpotMarketData.Subscribe(
                    data => InvokeSpotUpdate(data),
                    ex => _log.Error(ex, "Error in spot subscription")
                );

                _futuresSubscription = _marketDataService.FuturesMarketData.Subscribe(
                    data => InvokeFuturesUpdate(data),
                    ex => _log.Error(ex, "Error in futures subscription")
                );

                // Initialize position mode
                try
                {
                    var positions = await GetPositionsAsync();
                    _currentPositionMode = positions.FirstOrDefault()?.PositionMode ?? Models.PositionMode.MergedSingle;
                    _log.Information($"Initialized position mode: {_currentPositionMode}");
                }
                catch (Exception ex)
                {
                    _log.Warning(ex, "Failed to initialize position mode, using default");
                    _currentPositionMode = _config.PositionMode;
                }

                State = ExchangeState.Ready;
                _log.Information("BybitExchangeAPI initialized and ready");
            }
            catch (Exception ex)
            {
                State = ExchangeState.Error;
                _log.Error(ex, "Error during BybitExchangeAPI initialization");
                throw;
            }
        }

        private void InitPrivateClients(string apiKey, string apiSecret, BybitEnvironment environment)
        {
            var credentials = new ApiCredentials(apiKey, apiSecret);
            
            // Initialize subscription socket client
            _subscriptionSocketClient = new BybitSocketClient(options =>
            {
                options.Environment = environment;
                options.ApiCredentials = credentials;
                options.ReconnectInterval = TimeSpan.FromSeconds(5);
                options.V5Options.PingInterval = TimeSpan.FromSeconds(20);
            });

            privateRestClient = new BybitRestClient(options =>
            {
                options.ApiCredentials = credentials;
                options.Environment = environment;
            });
        }

        public override async Task<IEnumerable<AssetBalance>> GetBalancesAsync()
        {
            if (privateRestClient == null)
            {
                throw new InvalidOperationException("API credentials are required to get balances");
            }

            try
            {
                if (State != ExchangeState.Ready)
                    throw new InvalidOperationException("API not in Ready state");

                _log.Information("Getting unified account balances");

                var response = await privateRestClient.V5Api.Account.GetBalancesAsync(
                    accountType: global::Bybit.Net.Enums.AccountType.Unified);

                if (!response.Success)
                {
                    _log.Error($"Failed to get balances: {response.Error?.Message}");
                    throw new Exception($"Failed to get balances: {response.Error?.Message}");
                }

                // Map Bybit balances to our Balance model
                return response.Data.List
                    .SelectMany(account => account.Assets ?? Enumerable.Empty<BybitAssetBalance>())
                    .Select(asset => new AssetBalance
                    {
                        Asset = asset.Asset,
                        Total = asset.WalletBalance ?? 0,
                        Available = asset.Free ?? 0,
                        InOrder = asset.Locked ?? 0  // asset.WalletBalance - asset.Free
                    })
                    .ToList();
            }
            catch (Exception ex)
            {
                _log.Error(ex, "Error getting balances");
                throw;
            }
        }

        public override async Task<AssetBalance> GetBalanceAsync(string asset)
        {
            if (string.IsNullOrEmpty(asset))
                throw new ArgumentException("Asset cannot be empty", nameof(asset));

            var balances = await GetBalancesAsync();
            var balance = balances.FirstOrDefault(b => b.Asset.Equals(asset, StringComparison.OrdinalIgnoreCase));

            if (balance == null)
            {
                return new AssetBalance  // Return zero balance if asset not found
                {
                    Asset = asset,
                    Total = 0,
                    Available = 0,
                    InOrder = 0
                };
            }

            return balance;
        }

        public override async Task<WalletUpdate> GetWalletBalancesAsync()
        {
            if (privateRestClient == null)
            {
                _log.Warning("GetWalletBalancesAsync: API credentials (privateRestClient) are not initialized. Returning last known update.");
                // Fallback to base behavior which might return a cached or empty update
                return await base.GetWalletBalancesAsync();
            }

            try
            {
                if (State != ExchangeState.Ready)
                {
                    _log.Warning($"GetWalletBalancesAsync: API not in Ready state (State: {State}). Returning last known update.");
                    return await base.GetWalletBalancesAsync();
                }

                _log.Information("Fetching wallet balances via REST API...");

                // Bybit's GetBalancesAsync in V5 returns a list of BybitAccountBalance objects.
                // Each BybitAccountBalance contains a list of BybitAssetBalance.
                // We need to aggregate these into a single WalletUpdate.
                var response = await privateRestClient.V5Api.Account.GetBalancesAsync(
                    accountType: global::Bybit.Net.Enums.AccountType.Unified); // Or Contract, depending on needs

                if (!response.Success || response.Data == null || !response.Data.List.Any())
                {
                    _log.Error($"Failed to get wallet balances: {response.Error?.Message}. Returning last known update.");
                    return await base.GetWalletBalancesAsync(); // Fallback
                }

                // Assuming we are interested in the first account type returned, or a specific one.
                // For Unified accounts, there's typically one main "account" in the list.
                var accountBalance = response.Data.List.First(); // Modify if multiple account types need merging

                // Map Bybit's accountBalance to our WalletUpdate model.
                // UpdateMapping.MapWalletUpdate seems to be for BybitWalletBalance (socket data).
                // We need to adapt or create a mapping for BybitAccountBalance (REST data).
                // For now, let's assume a direct mapping or a new helper in UpdateMapping.
                
                var walletUpdate = new WalletUpdate
                {
                    AccountType = UpdateMapping.MapFromBybitAccountType(accountBalance.AccountType),
                    TotalEquity = accountBalance.TotalEquity,
                    TotalWalletBalance = accountBalance.TotalWalletBalance,
                    TotalMarginBalance = accountBalance.TotalMarginBalance,
                    TotalAvailableBalance = accountBalance.TotalAvailableBalance,
                    TotalPerpUnrealizedPnl = accountBalance.TotalPerpUnrealizedPnl,
                    TotalInitialMargin = accountBalance.TotalInitialMargin,
                    TotalMaintenanceMargin = accountBalance.TotalMaintenanceMargin,
                    Assets = accountBalance.Assets?.Select(asset => new AssetBalance
                    {
                        Asset = asset.Asset,
                        Total = asset.WalletBalance ?? 0,
                        Available = asset.AvailableToWithdraw ?? 0, // Prefer AvailableToWithdraw if present, removed .Free as it's not typically on BybitAssetBalance for this endpoint
                        InOrder = asset.Locked ?? 0
                    }).ToList() ?? new List<AssetBalance>()
                };
                
                // Invoke the base class method to update the internal Wallet state and trigger events
                InvokeWalletUpdate(walletUpdate);
                _log.Information($"Successfully fetched and processed wallet balances. Total Equity: {walletUpdate.TotalEquity ?? 0:F2}");

                return walletUpdate;
            }
            catch (Exception ex)
            {
                _log.Error(ex, "Error in GetWalletBalancesAsync");
                // Fallback to base behavior in case of any other exception
                return await base.GetWalletBalancesAsync();
            }
        }

        private SpotOrderResult CreateSpotErrorResult(string message)
        {
            _log.Warning(message);
            return new SpotOrderResult
            {
                IsSuccess = false,
                Status = Models.OrderStatus.Rejected,
                Timestamp = DateTime.UtcNow,
                Message = message
            };
        }

        private FuturesOrderResult CreateFuturesErrorResult(string message)
        {
            _log.Warning(message);
            return new FuturesOrderResult
            {
                IsSuccess = false,
                Status = Models.OrderStatus.Rejected,
                Timestamp = DateTime.UtcNow,
                Message = message
            };
        }

        /// <summary>
        /// Gets the current account settings from Bybit. Attempts to infer from positions.
        /// </summary>
        public override async Task<AccountSettings> GetAccountSettingsAsync()
        {
            try
            {
                if (privateRestClient == null)
                {
                    _log.Warning("GetAccountSettingsAsync: REST client not initialized, returning cached account settings");
                    // Return cached values if REST client isn't available
                    return new AccountSettings
                    {
                        PositionMode = _currentPositionMode,
                        MarginMode = _currentMarginMode,
                        TradeMode = _currentTradeMode,
                        BuyLeverage = _currentBuyLeverage,
                        SellLeverage = _currentSellLeverage
                    };
                }

                // Fetch current positions to infer settings
                // We wrap this in a try-catch as fetching positions might fail
                try
                {
                    // Use the class's own GetPositionsAsync method
                    var positionsResult = await this.GetPositionsAsync(
                        Models.Category.Linear,
                        TradingPair.Symbol
                    );

                    // positionsResult is now IEnumerable<PositionModel>, not WebCallResult
                    if (positionsResult.Any())
                    {
                        // var positionsList = positionsResult.Data.List.ToList(); // No longer need Data.List
                        var positionsList = positionsResult.ToList(); // Directly convert the IEnumerable
                        var relevantPositions = positionsList.Where(p => p.Symbol == TradingPair.Symbol).ToList();

                        if (relevantPositions.Any())
                        {
                            var firstPos = relevantPositions.First();
                            // Map using our model properties now
                            _currentPositionMode = firstPos.PositionMode;
                            _currentTradeMode = firstPos.TradeMode;
                            // Ensure Leverage has value, default if not (though GetPositionsAsync should populate it)
                            _currentBuyLeverage = firstPos.Leverage ?? _config.Leverage;
                            _currentSellLeverage = firstPos.Leverage ?? _config.Leverage;
                            _log.Information($"Retrieved settings via GetPositionsAsync: Mode={_currentPositionMode}, TradeMode={_currentTradeMode}, Leverage={_currentBuyLeverage}x");
                        }
                        else
                        {
                            _log.Information("GetAccountSettingsAsync: No relevant positions found via GetPositionsAsync. Settings might be stale.");
                        }
                    }
                    else
                    {
                         _log.Information("GetAccountSettingsAsync: No positions found via GetPositionsAsync. Settings might be stale.");
                    }
                }
                catch (Exception ex)
                {
                    _log.Error(ex, "GetAccountSettingsAsync: Exception while getting positions for settings update via this.GetPositionsAsync.");
                    // Proceed with potentially stale cached values
                }


                // We still need MarginMode - Bybit doesn't have a simple endpoint for this readily available
                // in V5 that isn't account-wide (like GetBalances which doesn't show it).
                // We rely on the initially set _currentMarginMode or what was set via SetMarginModeAsync.

                // Return the potentially updated settings
                return new AccountSettings
                {
                    PositionMode = _currentPositionMode,
                    MarginMode = _currentMarginMode, // Relying on cached/set value
                    TradeMode = _currentTradeMode,
                    BuyLeverage = _currentBuyLeverage,
                    SellLeverage = _currentSellLeverage
                };
            }
            catch (Exception ex)
            {
                _log.Error(ex, "GetAccountSettingsAsync: Outer error getting account settings.");
                // Return cached values as fallback
                return new AccountSettings
                {
                    PositionMode = _currentPositionMode,
                    MarginMode = _currentMarginMode,
                    TradeMode = _currentTradeMode,
                    BuyLeverage = _currentBuyLeverage,
                    SellLeverage = _currentSellLeverage
                };
            }
        }

        /// <summary>
        /// Sets the leverage on Bybit for the specific symbol.
        /// </summary>
        public override async Task<OperationResult> SetLeverageAsync(decimal? leverage, Models.PositionDirection? positionDirection = null)
        {
            if (privateRestClient == null)
                return new OperationResult() { IsSuccess = false, Message = "API credentials are required to set leverage" };

            try
            {
                if (!leverage.HasValue)
                {
                    _log.Warning($"Attempted to set null leverage. No changes made. Current leverage is: Buy={_currentBuyLeverage}x, Sell={_currentSellLeverage}x");
                    return new OperationResult() { IsSuccess = false, Message = $"Null leverage provided. Current leverage remains at Buy={_currentBuyLeverage}x, Sell={_currentSellLeverage}x" };
                }

                decimal validatedLeverage = leverage.Value;
                if (validatedLeverage < 1) {
                    _log.Warning($"Leverage {validatedLeverage} is less than 1, adjusting to 1.");
                    validatedLeverage = 1;
                }
                if (validatedLeverage > _config.MaxLeverage) {
                    _log.Warning($"Leverage {validatedLeverage} exceeds max leverage {_config.MaxLeverage}, adjusting to max.");
                    validatedLeverage = _config.MaxLeverage;
                }


                // Bybit V5 sets leverage per symbol, not per direction buy/sell anymore for Unified/Contract accounts
                // The positionDirection parameter becomes less relevant here, but we retain it for potential future compatibility
                // We will set both buyLeverage and sellLeverage to the same value as required by Bybit V5 API for Linear category.
                decimal buyLeverageToSet = validatedLeverage;
                decimal sellLeverageToSet = validatedLeverage;

                _log.Information($"Attempting to set leverage for {TradingPair.Symbol} to {validatedLeverage}x");

                var leverageResult = await privateRestClient.V5Api.Account.SetLeverageAsync(
                    category: Mapping.MapToBybitCategory(Models.Category.Linear),
                    symbol: TradingPair.Symbol,
                    buyLeverage: buyLeverageToSet,
                    sellLeverage: sellLeverageToSet);

                if (!leverageResult.Success)
                {
                    // Log specific error code if available
                    string errorMsg = $"Bybit API Error: {leverageResult.Error?.Code} - {leverageResult.Error?.Message}";
                    _log.Warning($"Failed to set leverage to {validatedLeverage}x. {errorMsg}");
                    return new OperationResult() { IsSuccess = false, Message = $"Failed to set leverage. {errorMsg}. Current leverage remains Buy={_currentBuyLeverage}x, Sell={_currentSellLeverage}x" };
                }

                // Update stored leverage values if successful
                _currentBuyLeverage = buyLeverageToSet;
                _currentSellLeverage = sellLeverageToSet;

                _log.Information($"Successfully set leverage for {TradingPair.Symbol} to Buy={_currentBuyLeverage}x, Sell={_currentSellLeverage}x");
                return new OperationResult() { IsSuccess = true, Message = $"Leverage set to Buy={_currentBuyLeverage}x, Sell={_currentSellLeverage}x" };
            }
            catch (Exception ex)
            {
                _log.Error(ex, "Error setting leverage");
                return new OperationResult() { IsSuccess = false, Message = $"Error setting leverage: {ex.Message}" };
            }
        }

        // Default implementation is okay
        public Task<SpotOrderResult> OpenSpotPositionAsync(bool isBuy, decimal baseAmount, decimal? limitPrice = null, Models.OrderType? orderType = null, Models.TimeInForce timeInForce = Models.TimeInForce.PostOnly)
        {
            return base.OpenSpotPositionAsync(isBuy, baseAmount, limitPrice, orderType, timeInForce);
        }

        // Default implementation is okay
        public Task<FuturesOrderResult> OpenFuturesPositionAsync(bool isLong, decimal baseAmount, decimal? leverage = null, decimal? limitPrice = null, Models.OrderType? orderType = null, Models.TimeInForce timeInForce = Models.TimeInForce.PostOnly, Models.PositionDirection? positionDirection = null)
        {
            return base.OpenFuturesPositionAsync(isLong, baseAmount, leverage, limitPrice, orderType, timeInForce, positionDirection);
        }

        private async Task<T> ExecuteWithFallbackAsync<T>(
            Func<Task<T>> socketOperation,
            Func<Task<T>> restOperation,
            string operationName)
        {
            // Check ONLY trading stream connection status
            if (_isDataStreamConnected && _subscriptionSocketClient != null)
            {
                try
                {
                    _log.Information($"Attempting {operationName} via socket...");
                    var result = await socketOperation();
                    _log.Information($"{operationName} executed successfully via socket");
                    return result;
                }
                catch (Exception ex)
                {
                    // If we get a connection error, mark socket as disconnected
                    if (ex.Message.Contains("connect", StringComparison.OrdinalIgnoreCase) ||
                        ex.Message.Contains("socket", StringComparison.OrdinalIgnoreCase))
                    {
                        _isDataStreamConnected = false;
                        _log.Warning($"Data stream connection lost: {ex.Message}");
                    }

                    _log.Warning($"Socket operation failed: {ex.Message}. Falling back to REST...");
                    
                    try
                    {
                        _log.Information($"Attempting {operationName} via REST...");
                        var result = await restOperation();
                        _log.Information($"{operationName} executed successfully via REST");
                        return result;
                    }
                    catch (Exception restEx)
                    {
                        _log.Error($"REST operation also failed: {restEx.Message}");
                        throw;
                    }
                }
            }
            else
            {
                if (!_isDataStreamConnected)
                    _log.Information("Data stream not connected, using REST API");
                    
                return await restOperation();
            }
        }

        protected override async Task<SpotOrderResult> PlaceSpotOrderAsync(
            string symbol,
            Models.OrderSide side,
            decimal quantity,
            decimal? price = null,
            Models.OrderType? orderType = null,
            Models.TimeInForce timeInForce = Models.TimeInForce.GoodTillCancel,
            string? clientId = null
            )
        {
            if ((privateRestClient == null) && (_subscriptionSocketClient == null))
                throw new InvalidOperationException("API credentials are required to place orders");

            try
            {
                if (State != ExchangeState.Ready)
                    throw new InvalidOperationException("API not in Ready state");

                // Use config default if not specified
                orderType ??= _config.OrderDefaults.SpotOrderType;

                // Validate inputs
                if (quantity <= 0)
                    throw new ArgumentException("Quantity must be positive", nameof(quantity));
                if (price.HasValue && price <= 0)
                    throw new ArgumentException("Price must be positive", nameof(price));

                // Validate order type and price combinations
                if (orderType == Models.OrderType.Limit && !price.HasValue)
                {
                    return CreateSpotErrorResult("Limit orders must specify a price");
                }

                // Before placing orders, round the amounts
                quantity = TradingPair.RoundBaseAmount(quantity);
                if (price.HasValue)
                {
                    price = TradingPair.RoundQuoteAmount(price.Value);
                }

                _log.Information(
                    $"Placing {side} spot {orderType} order - " +
                    clientId ?? $"ClientOrderId: {clientId}, " +
                    $"Symbol: {symbol}, " +
                    $"Amount: {quantity} {TradingPair.BaseCoin}" +
                    (price.HasValue ? $", Price: {price.Value} {TradingPair.QuoteCoin}" : "") +
                    $", TIF: {timeInForce}");

                return await ExecuteWithFallbackAsync(
                    async () => await PlaceSpotOrderViaSocketAsync(symbol, side, quantity, price, orderType, timeInForce, clientId),
                    async () => await PlaceSpotOrderViaRestAsync(symbol, side, quantity, price, orderType, timeInForce, clientId),
                    "spot order placement");
            }
            catch (Exception ex)
            {
                return CreateSpotErrorResult($"Error in PlaceSpotOrderAsync: {ex.Message}");
            }
        }

        private async Task<SpotOrderResult> PlaceSpotOrderViaSocketAsync(
            string symbol,
            Models.OrderSide side,
            decimal quantity,
            decimal? price,
            Models.OrderType? orderType,
            Models.TimeInForce timeInForce,
            string? clientOrderId
            )
        {
            try
            {
                 // Round quantities just before API call
                 decimal roundedQuantity = TradingPair.RoundBaseAmount(quantity);
                 decimal? roundedPrice = price.HasValue ? TradingPair.RoundQuoteAmount(price.Value) : null;

                 _log.Debug($"Socket Placing Spot: Qty={roundedQuantity}, Price={roundedPrice}, TIF={Mapping.MapToBybitTimeInForce(timeInForce)}");

                var response = await _subscriptionSocketClient!.V5PrivateApi.PlaceOrderAsync(
                    clientOrderId: clientOrderId,
                    category: Mapping.MapToBybitCategory(Models.Category.Spot),
                    symbol: symbol,
                    side: Mapping.MapToBybitOrderSide(side),
                    type: Mapping.MapToBybitNewOrderType(orderType!.Value),
                    quantity: roundedQuantity, // Use rounded quantity
                    price: orderType == Models.OrderType.Market ? null : roundedPrice, // Use rounded price
                    timeInForce: Mapping.MapToBybitTimeInForce(timeInForce) // Map and pass TIF
                    );

                if (!response.Success)
                {
                    _log.Warning($"Socket order placement failed: {response.Error?.Message}");
                    throw new Exception(response.Error?.Message ?? "Unknown socket error");
                }

                var result = await ProcessSocketOrderResponse(response, Models.Category.Spot, clientOrderId);
                if (result is SpotOrderResult spotResult)
                    return spotResult;
                
                throw new InvalidOperationException("Expected SpotOrderResult but got different type");
            }
            catch (Exception ex)
            {
                _log.Warning($"Socket order placement failed: {ex.Message}");
                throw;
            }
        }

        private async Task<SpotOrderResult> PlaceSpotOrderViaRestAsync(
            string symbol,
            Models.OrderSide side,
            decimal quantity,
            decimal? price,
            Models.OrderType? orderType,
            Models.TimeInForce timeInForce,
            string? clientOrderId
            )
        {
            try
            {
                 // Round quantities just before API call
                 decimal roundedQuantity = TradingPair.RoundBaseAmount(quantity);
                 decimal? roundedPrice = price.HasValue ? TradingPair.RoundQuoteAmount(price.Value) : null;

                 _log.Debug($"REST Placing Spot: Qty={roundedQuantity}, Price={roundedPrice}, TIF={Mapping.MapToBybitTimeInForce(timeInForce)}");

                var response = await privateRestClient!.V5Api.Trading.PlaceOrderAsync(
                    clientOrderId: clientOrderId,
                    category: Mapping.MapToBybitCategory(Models.Category.Spot),
                    symbol: symbol,
                    side: Mapping.MapToBybitOrderSide(side),
                    type: Mapping.MapToBybitNewOrderType(orderType!.Value),
                    quantity: roundedQuantity, // Use rounded quantity
                    price: orderType == Models.OrderType.Market ? null : roundedPrice, // Use rounded price
                    timeInForce: Mapping.MapToBybitTimeInForce(timeInForce)); // Map and pass TIF

                if (!response.Success)
                {
                    _log.Warning($"REST order placement failed: {response.Error?.Message}");
                    throw new Exception(response.Error?.Message ?? "Unknown REST error");
                }

                return (SpotOrderResult)await ProcessRestOrderResponse(response, Models.Category.Spot, clientOrderId);
            }
            catch (Exception ex)
            {
                _log.Warning($"REST order placement failed: {ex.Message}");
                throw;
            }
        }

        protected override async Task<FuturesOrderResult> PlaceFuturesOrderAsync(
            string symbol,
            Models.OrderSide side,
            decimal quantity,
            decimal? price = null,
            decimal? leverage = null,
            Models.OrderType? orderType = null,
            Models.PositionDirection? positionDirection = null,
            Models.TimeInForce timeInForce = Models.TimeInForce.GoodTillCancel,
            bool reduceOnly = false,
            string? clientId = null,
            decimal? triggerPrice = null,
            Models.TriggerType? triggerBy = null,
            Models.TriggerDirection? triggerDirection = null)
        {
            if ((privateRestClient == null) && (_subscriptionSocketClient == null))
                throw new InvalidOperationException("API credentials are required to place orders");

            try
            {
                if (State != ExchangeState.Ready)
                    throw new InvalidOperationException("API not in Ready state");

                // Use config defaults if not specified
                orderType ??= _config.OrderDefaults.FuturesOrderType;

                // Validate inputs
                if (quantity <= 0)
                    throw new ArgumentException("Quantity must be positive", nameof(quantity));
                if (price.HasValue && price <= 0)
                    throw new ArgumentException("Price must be positive", nameof(price));

                // Validate order type and price combinations
                if (orderType == Models.OrderType.Limit && !price.HasValue)
                {
                    return CreateFuturesErrorResult("Limit orders must specify a price");
                }

                // Only validate and set leverage if explicitly provided
                if (leverage.HasValue)
                {
                    var leverageResult = await SetLeverageAsync(leverage, positionDirection);

                    if (!leverageResult.IsSuccess)
                    {
                        _log.Warning($"Failed to set leverage to '{leverage}': {leverageResult?.Message}\n\tActual leverage is: {_currentBuyLeverage}");  // Bybit may reject without clear reason
                    }
                }

                // Before placing orders, round the amounts
                quantity = TradingPair.RoundBaseAmount(quantity);
                if (price.HasValue)
                {
                    price = TradingPair.RoundQuoteAmount(price.Value);
                }

                _log.Information(
                    $"Placing {side} futures {orderType} order - " +
                    clientId ?? $"ClientOrderId: {clientId}, " +
                    $"Symbol: {symbol}, " +
                    $"Amount: {quantity} {TradingPair.BaseCoin}, " +
                    $"Leverage: {leverage:F2}x" +
                    (orderType == Models.OrderType.Limit ? $", Price: {price.Value} {TradingPair.QuoteCoin}" : "") +
                    (positionDirection.HasValue ? $", PosDir: {positionDirection}" : "") +
                    $", TIF: {timeInForce}" +
                    $", ReduceOnly: {reduceOnly}" +
                    (triggerPrice.HasValue ? $", TriggerPrice: {triggerPrice}" : "") +
                    (triggerBy.HasValue ? $", TriggerBy: {triggerBy}" : "") +
                    (triggerDirection.HasValue ? $", TriggerDirection: {triggerDirection}" : ""));

                return await ExecuteWithFallbackAsync(
                    async () => await PlaceFuturesOrderViaSocketAsync(symbol, side, quantity, price, orderType, positionDirection, timeInForce, reduceOnly, clientId, triggerPrice, triggerBy, triggerDirection),
                    async () => await PlaceFuturesOrderViaRestAsync(symbol, side, quantity, price, orderType, positionDirection, timeInForce, reduceOnly, clientId, triggerPrice, triggerBy, triggerDirection),
                    "futures order placement");
            }
            catch (Exception ex)
            {
                return CreateFuturesErrorResult($"Error in PlaceFuturesOrderAsync: {ex.Message}");
            }
        }

        private async Task<FuturesOrderResult> PlaceFuturesOrderViaSocketAsync(
            string symbol,
            Models.OrderSide side,
            decimal quantity,
            decimal? price,
            Models.OrderType? orderType,
            Models.PositionDirection? positionDirection,
            Models.TimeInForce timeInForce,
            bool reduceOnly,
            string? clientOrderId,
            decimal? triggerPrice = null,
            Models.TriggerType? triggerBy = null,
            Models.TriggerDirection? triggerDirection = null)
        {
            try
            {
                // Round quantities just before API call
                decimal roundedQuantity = TradingPair.RoundBaseAmount(quantity);
                decimal? roundedPrice = price.HasValue ? TradingPair.RoundQuoteAmount(price.Value) : null;

                // Use the enhanced validation method for logging
                if (!ValidatePositionDirectionAndMode(symbol, _currentPositionMode, ref positionDirection, side == Models.OrderSide.Buy, out string validationMessage))
                {
                    _log.Warning($"{validationMessage} - this may be ignored by Bybit");
                }

                _log.Debug($"Socket Placing Futures: Client={clientOrderId}, Qty={quantity}, Price={roundedPrice}, TIF={Mapping.MapToBybitTimeInForce(timeInForce)}, ReduceOnly={reduceOnly}, PosIdx={positionDirection}");

                var response = await _subscriptionSocketClient!.V5PrivateApi.PlaceOrderAsync(
                    clientOrderId: clientOrderId,
                    category: Mapping.MapToBybitCategory(Models.Category.Linear),
                    symbol: symbol,
                    side: Mapping.MapToBybitOrderSide(side),
                    type: Mapping.MapToBybitNewOrderType(orderType!.Value),
                    quantity: roundedQuantity, // Use rounded
                    price: orderType == Models.OrderType.Market ? null : roundedPrice, // Use rounded
                    isLeverage: true, // Assuming futures always use leverage setting
                    timeInForce: Mapping.MapToBybitTimeInForce(timeInForce), // Map and pass TIF
                    positionIdx: positionDirection.HasValue ? Mapping.MapToBybitPositionIdx(positionDirection.Value) : null,
                    reduceOnly: reduceOnly, // Pass reduceOnly flag
                    triggerPrice: triggerPrice, // Pass triggerPrice
                    triggerBy: triggerBy.HasValue ? Mapping.MapToBybitTriggerType(triggerBy.Value) : null, // Map and pass triggerBy
                    triggerDirection: triggerDirection.HasValue ? Mapping.MapToBybitTriggerDirection(triggerDirection.Value) : null // Map and pass triggerDirection
                    );
                                        
                if (!response.Success)
                {
                    _log.Warning($"Socket order placement failed: {response.Error?.Message}");
                    throw new Exception(response.Error?.Message ?? "Unknown socket error");
                }

                var result = await ProcessSocketOrderResponse(response, Models.Category.Linear, clientOrderId);
                if (result is FuturesOrderResult futuresResult)
                    return futuresResult;
                
                throw new InvalidOperationException("Expected FuturesOrderResult but got different type");
            }
            catch (Exception ex)
            {
                _log.Warning($"Socket order placement failed: {ex.Message}");
                throw;
            }
        }

        private async Task<FuturesOrderResult> PlaceFuturesOrderViaRestAsync(
            string symbol,
            Models.OrderSide side,
            decimal quantity,
            decimal? price,
            Models.OrderType? orderType,
            Models.PositionDirection? positionDirection,
            Models.TimeInForce timeInForce,
            bool reduceOnly,
            string? clientOrderId,
            decimal? triggerPrice = null,
            Models.TriggerType? triggerBy = null,
            Models.TriggerDirection? triggerDirection = null)
        {
            try
            {
                // Round quantities just before API call
                decimal roundedQuantity = TradingPair.RoundBaseAmount(quantity);
                decimal? roundedPrice = price.HasValue ? TradingPair.RoundQuoteAmount(price.Value) : null;

                // Use the common validation method for logging
                if (!ValidatePositionDirectionAndMode(symbol, _currentPositionMode, ref positionDirection, side == Models.OrderSide.Buy, out string validationMessage))
                {
                    // Log validation message but proceed, let Bybit API handle final rejection
                    _log.Warning($"Validation Warning Placing Futures: {validationMessage}");
                }
                
                _log.Debug($"REST Placing Futures: {clientOrderId ?? "ClientId={clientOrderId},"} Qty={quantity}, Price={roundedPrice}, TIF={Mapping.MapToBybitTimeInForce(timeInForce)}, ReduceOnly={reduceOnly}, PosIdx={positionDirection}");

                var response = await privateRestClient!.V5Api.Trading.PlaceOrderAsync(
                    clientOrderId: clientOrderId,
                    category: Mapping.MapToBybitCategory(Models.Category.Linear),
                    symbol: symbol,
                    side: Mapping.MapToBybitOrderSide(side),
                    type: Mapping.MapToBybitNewOrderType(orderType!.Value),
                    quantity: roundedQuantity, // Use rounded
                    price: orderType == Models.OrderType.Market ? null : roundedPrice, // Use rounded
                    isLeverage: true, // Assuming futures always use leverage setting
                    timeInForce: Mapping.MapToBybitTimeInForce(timeInForce), // Map and pass TIF
                    positionIdx: positionDirection.HasValue ? Mapping.MapToBybitPositionIdx(positionDirection.Value) : null,
                    reduceOnly: reduceOnly, // Pass reduceOnly flag
                    triggerPrice: triggerPrice, // Pass triggerPrice
                    triggerBy: triggerBy.HasValue ? Mapping.MapToBybitTriggerType(triggerBy.Value) : null, // Map and pass triggerBy
                    triggerDirection: triggerDirection.HasValue ? Mapping.MapToBybitTriggerDirection(triggerDirection.Value) : null // Map and pass triggerDirection
                    );

                if (!response.Success)
                {
                    // Provide more detailed logging on failure
                    string errorMsg = $"Bybit API Error: {response.Error?.Code} - {response.Error?.Message}";
                    _log.Warning($"REST Futures Order Placement Failed. {errorMsg}");
                    throw new Exception($"REST Order Placement Failed. {errorMsg}"); // Throw exception to be caught by caller/ExecuteWithFallback
                }

                return (FuturesOrderResult)await ProcessRestOrderResponse(response, Models.Category.Linear, clientOrderId);
            }
            catch (Exception ex)
            {
                _log.Warning($"REST order placement failed: {ex.Message}");
                throw;
            }
        }

        public override async Task<OrderModel?> GetOrderStatusAsync(string orderId, Models.Category category, string? clientId = null)
        {
            if (privateRestClient == null)
                throw new InvalidOperationException("API credentials are required to get order status");
            if (string.IsNullOrEmpty(orderId) && string.IsNullOrEmpty(clientId))
                throw new ArgumentException("Either orderId or clientId must be provided.");

            try
            {
                _log.Debug($"Getting order status - OrderId: {orderId}, ClientId: {clientId}, Category: {category}");
                var orderDetails = await privateRestClient.V5Api.Trading.GetOrdersAsync(
                    category: Mapping.MapToBybitCategory(category),
                    orderId: string.IsNullOrEmpty(orderId) ? null : orderId,
                    clientOrderId: string.IsNullOrEmpty(orderId) ? clientId : null);

                if (!orderDetails.Success || orderDetails.Data.List == null || !orderDetails.Data.List.Any())
                {
                    _log.Warning($"Order not found or failed to get details - OrderId: {orderId}, ClientId: {clientId}, Category: {category}. Error: {orderDetails.Error?.Message}");
                    return null; // Return null if not found or error
                }

                var order = orderDetails.Data.List.First();

                // Map BybitOrder to OUR OrderModel
                var orderModel = UpdateMapping.MapOrder(order); // Use MapOrder, not MapOrderUpdate
                orderModel.Category = category; // Ensure category is set

                // Ensure ClientOrderId is populated if we searched by OrderId
                if (!string.IsNullOrEmpty(orderId) && string.IsNullOrEmpty(orderModel.ClientOrderId) && !string.IsNullOrEmpty(order.ClientOrderId))
                {
                    orderModel.ClientOrderId = order.ClientOrderId;
                }

                return orderModel; // Return the mapped OrderModel
            }
            catch (Exception ex)
            {
                _log.Error(ex, $"Error getting order status - OrderId: {orderId}, ClientId: {clientId}");
                return null; // Return null on exception
            }
        }

        public override async Task<OperationResult> CancelOrderAsync(string symbol, string? orderId = null, string? clientId = null)
        {
            if (privateRestClient == null)
                throw new InvalidOperationException("API credentials are required to cancel orders");
             if (string.IsNullOrEmpty(orderId) && string.IsNullOrEmpty(clientId))
                throw new ArgumentException("Either orderId or clientId must be provided for cancellation.");

            try
            {
                if (State != ExchangeState.Ready) throw new InvalidOperationException("API not in Ready state");

                _log.Information($"Cancelling order for {symbol} - OrderId: {orderId}, ClientId: {clientId}");

                // Try cancelling in both categories, prioritizing orderId if provided
                var categoriesToTry = new[] { Models.Category.Spot, Models.Category.Linear };
                WebCallResult<BybitOrderId>? firstSuccessResult = null;
                List<string> errors = new();

                foreach(var category in categoriesToTry)
                {
                    var cancelResult = await privateRestClient.V5Api.Trading.CancelOrderAsync(
                        category: Mapping.MapToBybitCategory(category),
                        symbol: symbol,
                        orderId: string.IsNullOrEmpty(orderId) ? null : orderId, // Pass null if empty
                        clientOrderId: string.IsNullOrEmpty(orderId) ? clientId : null // Use clientId ONLY if orderId is empty
                        );

                    if (cancelResult.Success)
                    {
                        _log.Information($"Successfully cancelled {category} order - OrderId: {cancelResult.Data.OrderId}, ClientId: {cancelResult.Data.ClientOrderId}");
                        firstSuccessResult = cancelResult;
                        break; // Stop after first success
                    }
                    else
                    {
                         // Ignore "Order does not exist" errors, log others
                        if (cancelResult.Error?.Code != 110001) // 110001: Order does not exist or finished
                        {
                            string errorMsg = $"Failed cancellation attempt for {category}: {cancelResult.Error?.Code} - {cancelResult.Error?.Message}";
                            _log.Warning(errorMsg);
                            errors.Add(errorMsg);
                        } else {
                            _log.Debug($"Order not found in {category} - OrderId: {orderId}, ClientId: {clientId}");
                        }
                    }
                }

                if (firstSuccessResult != null)
                {
                    return new OperationResult() { IsSuccess = true, Message = $"Successfully cancelled order {firstSuccessResult.Data.OrderId ?? firstSuccessResult.Data.ClientOrderId}" };
                }
                else if (errors.Count != 0) // If we had actual errors (not just "not found")
                {
                    return new OperationResult() { IsSuccess = false, Message = $"Failed to cancel order. Errors: {string.Join("; ", errors)}" };
                }
                else // If no success and no errors, it means the order wasn't found in either category
                {
                    return new OperationResult() { IsSuccess = false, Message = "Order not found or already finished." };
                }
            }
            catch (Exception ex)
            {
                _log.Error(ex, $"Error cancelling order - OrderId: {orderId}, ClientId: {clientId}");
                return new OperationResult() { IsSuccess = false, Message = $"Error cancelling order: {ex.Message}" }; // Return failure on exception
            }
        }

        public override async Task<OperationResult> CancelAllOrdersAsync(string? symbol = null, Category? category = null)
        {
            if (privateRestClient == null)
                return new OperationResult { IsSuccess = false, Message = "API credentials are required to cancel all orders" };

            try
            {
                if (State != ExchangeState.Ready)
                    throw new InvalidOperationException("API not in Ready state");

                _log.Information($"Cancelling all orders for Symbol: {symbol ?? "All Symbols"}, Category: {category?.ToString() ?? "All Categories"}");

                // If a specific category is provided, cancel orders for that category
                if (category.HasValue)
                {
                    var cancelResult = await privateRestClient.V5Api.Trading.CancelAllOrderAsync(
                        category: Mapping.MapToBybitCategory(category.Value),
                        symbol: symbol);

                    if (!cancelResult.Success)
                    {
                        _log.Warning($"Failed to cancel all orders for Category: {category?.ToString() ?? "All Categories"}, Symbol: {symbol ?? "All Symbols"}. Error: {cancelResult.Error?.Message}");
                        return new OperationResult { IsSuccess = false, Message = $"Failed to cancel all orders: {cancelResult.Error?.Message}" };
                    }

                    _log.Information($"Successfully cancelled all orders for Category: {category?.ToString() ?? "All Categories"}, Symbol: {symbol ?? "All Symbols"}");
                    return new OperationResult { IsSuccess = true, Message = "Successfully cancelled all orders" };
                }

                // If no specific category is provided, attempt to cancel orders for all categories
                var categoriesToTry = new[] { Models.Category.Spot, Models.Category.Linear };
                List<string> errors = new();

                foreach (var cat in categoriesToTry)
                {
                    var cancelResult = await privateRestClient.V5Api.Trading.CancelAllOrderAsync(
                        category: Mapping.MapToBybitCategory(cat),
                        symbol: symbol);

                    if (!cancelResult.Success)
                    {
                        _log.Warning($"Failed to cancel all orders for Category: {cat.ToString() ?? "All Categories"}, Symbol: {symbol ?? "All Symbols"}. Error: {cancelResult.Error?.Message}");
                        errors.Add($"Category: {cat}, Error: {cancelResult.Error?.Message}");
                    }
                    else
                    {
                        _log.Information($"Successfully cancelled all orders for Category: {cat.ToString() ?? "All Categories"}, Symbol: {symbol ?? "All Symbols"}");
                    }
                }

                if (errors.Count > 0)
                {
                    return new OperationResult { IsSuccess = false, Message = $"Failed to cancel all orders for some categories. Errors: {string.Join("; ", errors)}" };
                }

                return new OperationResult { IsSuccess = true, Message = "Successfully cancelled all orders for all categories" };
            }
            catch (Exception ex)
            {
                _log.Error(ex, "Error cancelling all orders");
                return new OperationResult { IsSuccess = false, Message = $"Error cancelling all orders: {ex.Message}" };
            }
        }

        protected override void InvokeSpotUpdate(SpotMarketData data)
        {
            base.InvokeSpotUpdate(data);
        }

        protected override void InvokeFuturesUpdate(FuturesMarketData data)
        {
            base.InvokeFuturesUpdate(data);
        }

        private async Task<OrderResult> ProcessSocketOrderResponse(
            CallResult<BybitOrderId> response,
            Models.Category category,
            string? originalClientId = null)
        {
            if (!response.Success || response.Data == null)
            {
                string errorMessage = response.Error?.Message ?? "Unknown error";
                _log.Error($"Socket order error: {errorMessage}");
                
                return category == Models.Category.Spot
                    ? CreateSpotErrorResult(errorMessage)
                    : CreateFuturesErrorResult(errorMessage);
            }
            
            var orderId = response.Data.OrderId;
            var responseClientId = response.Data.ClientOrderId;

            // Get status as OrderModel?
            var orderModel = await GetOrderStatusAsync(orderId, category, responseClientId);

            if (orderModel == null)
            {
                _log.Warning($"Could not retrieve status immediately after socket placement for OrderId: {orderId}, ClientId: {responseClientId}. Assuming 'New'.");
                return category == Models.Category.Spot
                    ? new SpotOrderResult { IsSuccess = true, OrderId = orderId, ClientOrderId = responseClientId ?? originalClientId, Status = Models.OrderStatus.New, Timestamp = DateTime.UtcNow }
                    : new FuturesOrderResult { IsSuccess = true, OrderId = orderId, ClientOrderId = responseClientId ?? originalClientId, Status = Models.OrderStatus.New, Timestamp = DateTime.UtcNow, Leverage = _currentBuyLeverage };
            }
            else
            {
                var baseResult = new OrderResult // TODO: is this (should be) used?
                {
                    IsSuccess = true,
                    OrderId = orderId,
                    ClientOrderId = orderModel.ClientOrderId,
                    Status = orderModel.Status,
                    ExecutedQuantity = orderModel.QuantityFilled,
                    ExecutedPrice = orderModel.AveragePrice,
                    Timestamp = orderModel.UpdateTime,
                    Message = "Order placed via socket"
                };
                TrackOrder(orderModel);
                return category == Models.Category.Spot
                    ? new SpotOrderResult { IsSuccess = true, OrderId = orderId, ClientOrderId = responseClientId ?? originalClientId, Status = orderModel.Status, ExecutedQuantity = orderModel.QuantityFilled, ExecutedPrice = orderModel.AveragePrice, Timestamp = orderModel.UpdateTime }
                    : new FuturesOrderResult { IsSuccess = true, OrderId = orderId, ClientOrderId = responseClientId ?? originalClientId, Status = orderModel.Status, ExecutedQuantity = orderModel.QuantityFilled, ExecutedPrice = orderModel.AveragePrice, Timestamp = orderModel.UpdateTime, Leverage = orderModel.IsLeverage == true ? _currentBuyLeverage : 0 };
            }
        }

        private async Task<OrderResult> ProcessRestOrderResponse(
            WebCallResult<BybitOrderId> response,
            Models.Category category,
            string? originalClientId = null)
        {
            if (!response.Success || response.Data == null)
            {
                string errorMessage = response.Error?.Message ?? "Unknown error";
                _log.Error($"REST order error: {errorMessage}");
                
                return category == Models.Category.Spot
                    ? CreateSpotErrorResult(errorMessage)
                    : CreateFuturesErrorResult(errorMessage);
            }
            
            var orderId = response.Data.OrderId;
            var responseClientId = response.Data.ClientOrderId;

            // Get status as OrderModel?
            var orderModel = await GetOrderStatusAsync(orderId, category, responseClientId);

            if (orderModel == null)
            {
                _log.Warning($"Could not retrieve status immediately after REST placement for OrderId: {orderId}, ClientId: {responseClientId}. Assuming 'New'.");
                return category == Models.Category.Spot
                    ? CreateSpotErrorResult("Order not found or failed to get details")
                    : CreateFuturesErrorResult("Order not found or failed to get details");
            }
            else
            {
                var baseResult = new OrderResult // TODO: is this (should be) used?
                {
                    IsSuccess = true,
                    OrderId = orderId,
                    ClientOrderId = orderModel.ClientOrderId,
                    Status = orderModel.Status,
                    ExecutedQuantity = orderModel.QuantityFilled,
                    ExecutedPrice = orderModel.AveragePrice,
                    Timestamp = orderModel.UpdateTime,
                    Message = "Order placed via REST"
                };
                TrackOrder(orderModel);
                return category == Models.Category.Spot
                    ? new SpotOrderResult { IsSuccess = true, OrderId = orderId, ClientOrderId = responseClientId ?? originalClientId, Status = orderModel.Status, ExecutedQuantity = orderModel.QuantityFilled ?? 0, ExecutedPrice = orderModel.AveragePrice, Timestamp = orderModel.UpdateTime }
                    : new FuturesOrderResult { IsSuccess = true, OrderId = orderId, ClientOrderId = responseClientId ?? originalClientId, Status = orderModel.Status, ExecutedQuantity = orderModel.QuantityFilled, ExecutedPrice = orderModel.AveragePrice ?? 0, Timestamp = orderModel.UpdateTime, Leverage = orderModel.IsLeverage == true ? _currentBuyLeverage : 0 };
            }
        }

        private void HandleWalletUpdate(WalletUpdate update)
        {
            // Use the base class method to update the wallet and forward the event
            InvokeWalletUpdate(update);
        }

        protected override void HandlePositionUpdate(PositionModelUpdate update)
        {
            //_log.Information($"Handling position update for {update.Symbol}: Size={update.Quantity}, Direction={update.Direction}");
            
            // Use the base class implementation for tracking
            base.HandlePositionUpdate(update);
            
            // Explicitly invoke the event if declared on this class
            //OnPositionUpdate?.Invoke(update);
        }

        protected override void HandleOrderUpdate(OrderModelUpdate update)
        {
            //_log.Information($"Handling order update for {update.OrderId}: Status={update.Status}, Filled={update.QuantityFilled}");
            
            // Use the base class implementation for tracking
            base.HandleOrderUpdate(update);

            // Explicitly invoke the event if declared on this class
            //OnOrderUpdate?.Invoke(update);
        }

        public override async Task<IEnumerable<PositionModel>> GetPositionsAsync(Models.Category category = Models.Category.Linear, string? symbol = null)
        {
            if (privateRestClient == null)
                throw new InvalidOperationException("API credentials are required to get positions");

            try
            {
                var response = await privateRestClient.V5Api.Trading.GetPositionsAsync(
                    category: Mapping.MapToBybitCategory(category),
                    symbol: symbol ?? TradingPair.Symbol);

                if (!response.Success)
                {
                    _log.Warning($"Failed to get positions: {response.Error?.Message}");
                    throw new Exception($"Failed to get positions: {response.Error?.Message}");
                }
               return response.Data.List.Select(p => UpdateMapping.MapPositionFromBybitPosition(p)).ToList();
            }
            catch (Exception ex)
            {
                _log.Error(ex, "Error getting positions");
                throw;
            }
        }

        public override async Task<OperationResult> SetPositionModeAsync(Models.PositionMode mode)
        {
            if (privateRestClient == null)
                return new OperationResult() { IsSuccess = false, Message = "API credentials are required to set position mode" };

            try
            {
                _log.Information($"Setting position mode to {mode}...");
                
                var result = await privateRestClient.V5Api.Account.SwitchPositionModeAsync(
                    category: Mapping.MapToBybitCategory(Models.Category.Linear),
                    mode: Mapping.MapToBybitPositionMode(mode),
                    symbol: TradingPair.Symbol);

                if (!result.Success)
                {
                    _log.Warning($"Failed to set position mode to {mode}: {result.Error?.Message}");
                    return new OperationResult() { IsSuccess = false, Message = $"Failed to set position mode: {result.Error?.Message}" };

                }

                // Update our cached state
                _currentPositionMode = mode;
                _log.Information($"Successfully set position mode to {mode}");
                return new OperationResult
                {
                    IsSuccess = true,
                    Message = $"Position mode set to {mode}"
                };
            }
            catch (Exception ex)
            {
                _log.Error(ex, "Error setting position mode");
                return new OperationResult() { IsSuccess = false, Message = $"Error setting position mode: {ex.Message}" };
            }
        }

        // StopAsync cleans up private clients
        public override async Task StopAsync()
        {
            if (State == ExchangeState.Stopping || State == ExchangeState.Stopped)
                return;

            State = ExchangeState.Stopping;
            _log.Information("Stopping BybitExchangeAPI ...");

            try
            {
                // Clean up subscriptions
                if (_spotSubscription != null)
                {
                    _spotSubscription.Dispose();
                    _spotSubscription = null;
                    _log.Information("Disposed spot subscription");
                }
                
                if (_futuresSubscription != null)
                {
                    _futuresSubscription.Dispose();
                    _futuresSubscription = null;
                    _log.Information("Disposed futures subscription");
                }

                if (_subscriptionSocketClient != null)
                {
                    try
                    {
                        // Unsubscribe from all active subscriptions
                        if (_walletSubscription != null)
                        {
                            await _subscriptionSocketClient.UnsubscribeAsync(_walletSubscription);
                            _walletSubscription = null;
                            _log.Information("Unsubscribed from wallet updates");
                        }
                        
                        if (_positionSubscription != null)
                        {
                            await _subscriptionSocketClient.UnsubscribeAsync(_positionSubscription);
                            _positionSubscription = null;
                            _log.Information("Unsubscribed from position updates");
                        }
                        
                        if (_orderSubscription != null)
                        {
                            await _subscriptionSocketClient.UnsubscribeAsync(_orderSubscription);
                            _orderSubscription = null;
                            _log.Information("Unsubscribed from order updates");
                        }

                        _subscriptionSocketClient.Dispose();
                        _subscriptionSocketClient = null;
                        _log.Information("Disposed subscription socket client");
                    }
                    catch (Exception ex)
                    {
                        _log.Warning(ex, "Error while unsubscribing subscription socket client");
                    }
                }

                if (privateRestClient != null)
                {
                    privateRestClient.Dispose();
                    privateRestClient = null;
                    _log.Information("Disposed private REST client");
                }

                State = ExchangeState.Stopped;
                _log.Information("BybitExchangeAPI stopped successfully");
            }
            catch (Exception ex)
            {
                _log.Error(ex, "Error during BybitExchangeAPI stopping");
                throw;
            }
        }

        public override async Task<OperationResult> ClosePositionAsync(string symbol)
        {
            if (privateRestClient == null)
                return new OperationResult { IsSuccess = false, Message = "API credentials are required to close positions" };

            try
            {
                _log.Information($"Closing position for {symbol} in one-way mode");
                
                // Check if we're in one-way mode
                if (_currentPositionMode != Models.PositionMode.MergedSingle)
                {
                    _log.Warning($"Attempting to close position without direction, but account is in hedge mode ({_currentPositionMode})");
                    return new OperationResult 
                    { 
                        IsSuccess = false, 
                        Message = $"Cannot close position without direction in hedge mode. Use ClosePositionAsync with direction parameter instead." 
                    };
                }
                
                // Get the position to determine direction and quantity
                var positions = await GetPositionsAsync(Models.Category.Linear, symbol);
                var position = positions.FirstOrDefault(p => p.Symbol == symbol);
                
                if (position == null || Math.Abs(position.Quantity) <= 0)
                {
                    return new OperationResult { IsSuccess = true, Message = $"No open position for {symbol}" };
                }
                
                // Determine if this is a long or short position
                bool isLong = position.Quantity > 0;
                
                // Place a market order in the opposite direction to close
                var orderResult = await OpenFuturesPositionAsync(
                    isLong: !isLong,  // Opposite direction
                    baseAmount: Math.Abs(position.Quantity),
                    orderType: Models.OrderType.Market);
                
                // Check if the order was successful
                if (!orderResult.IsSuccess)
                {
                    return new OperationResult 
                    { 
                        IsSuccess = false, 
                        Message = $"Failed to close position for {symbol}: {orderResult.Message}" 
                    };
                }
                
                // Verify the position is closed - OpenFuturesPositionAsync already waits for order completion
                positions = await GetPositionsAsync(Models.Category.Linear, symbol);
                position = positions.FirstOrDefault(p => p.Symbol == symbol);
                
                if (position == null || Math.Abs(position.Quantity) <= 0)
                {
                    return new OperationResult { IsSuccess = true, Message = $"Successfully closed position for {symbol}" };
                }
                else
                {
                    return new OperationResult 
                    { 
                        IsSuccess = false, 
                        Message = $"Position not fully closed for {symbol}. Remaining quantity: {position.Quantity}" 
                    };
                }
            }
            catch (Exception ex)
            {
                _log.Error(ex, $"Error closing position for {symbol}");
                return new OperationResult { IsSuccess = false, Message = $"Error closing position: {ex.Message}" };
            }
        }

        public override async Task<OperationResult> ClosePositionAsync(string symbol, PositionDirection? direction)
        {
            if (privateRestClient == null)
                return new OperationResult { IsSuccess = false, Message = "API credentials are required to close positions" };

            try
            {
                if (direction == null)
                {
                    return await ClosePositionAsync(symbol);
                }
                
                _log.Information($"Closing {direction} position for {symbol}");
                
                // Check if we're in hedge mode
                if (_currentPositionMode != Models.PositionMode.BothSides)
                {
                    _log.Warning($"Attempting to close position with direction, but account is in one-way mode ({_currentPositionMode})");
                    // We'll still try to close the position, but log a warning
                }
                
                // Get the position to determine quantity
                var positions = await GetPositionsAsync(Models.Category.Linear, symbol);
                var position = positions.FirstOrDefault(p => 
                    p.Symbol == symbol && 
                    p.Direction == direction);
                
                if (position == null || Math.Abs(position.Quantity) <= 0)
                {
                    return new OperationResult { IsSuccess = true, Message = $"No open {direction} position for {symbol}" };
                }
                
                // Determine if this is a long or short position
                bool isLong = direction == PositionDirection.Buy;
                
                // Place a market order in the opposite direction to close
                var orderResult = await OpenFuturesPositionAsync(
                    isLong: !isLong,  // Opposite direction
                    baseAmount: Math.Abs(position.Quantity),
                    orderType: Models.OrderType.Market,
                    positionDirection: direction);
                
                // Check if the order was successful
                if (!orderResult.IsSuccess)
                {
                    return new OperationResult 
                    { 
                        IsSuccess = false, 
                        Message = $"Failed to close {direction} position for {symbol}: {orderResult.Message}" 
                    };
                }
                
                // Verify the position is closed - OpenFuturesPositionAsync already waits for order completion
                positions = await GetPositionsAsync(Models.Category.Linear, symbol);
                position = positions.FirstOrDefault(p => 
                    p.Symbol == symbol && 
                    p.Direction == direction);
                
                if (position == null || Math.Abs(position.Quantity) <= 0)
                {
                    return new OperationResult { IsSuccess = true, Message = $"Successfully closed {direction} position for {symbol}" };
                }
                else
                {
                    return new OperationResult 
                    { 
                        IsSuccess = false, 
                        Message = $"Position not fully closed for {symbol} {direction}. Remaining quantity: {position.Quantity}" 
                    };
                }
            }
            catch (Exception ex)
            {
                _log.Error(ex, $"Error closing {direction} position for {symbol}");
                return new OperationResult { IsSuccess = false, Message = $"Error closing position: {ex.Message}" };
            }
        }

        // Helper method to get active orders for a specific category
        public override async Task<IEnumerable<OrderModel>> GetActiveOrdersForCategoryAsync(Models.Category category, string? symbol = null)
        {
            if (privateRestClient == null)
            {
                _log.Warning("GetActiveOrdersForCategoryAsync: privateRestClient is null, returning empty list");
                return Enumerable.Empty<OrderModel>();
            }

            try
            {
                var response = await privateRestClient.V5Api.Trading.GetOrdersAsync(
                    category: Mapping.MapToBybitCategory(category),
                    symbol: symbol ?? TradingPair.Symbol,
                    //openOnly: 1,  // This should already filter for open orders only, BUT IT DOESN'T, does exactly the opposite, had to comment out
                    orderFilter: global::Bybit.Net.Enums.OrderFilter.Order
                    );

                if (!response.Success)
                {
                    _log.Warning($"Failed to get active orders for {category}: {response.Error?.Message}");
                    return Enumerable.Empty<OrderModel>();
                }

                // Map orders, ensure Category is set correctly, and filter out orders in final states
                var orders = response.Data.List
                    .Where(order => !IsOrderInFinalState(Mapping.MapFromBybitOrderStatus(order.Status)))
                    .Select(order => 
                    {
                        var mappedOrder = UpdateMapping.MapOrder(order);
                        mappedOrder.Category = category; // Explicitly set the category
                        return mappedOrder;
                    })
                    .ToList();

                _log.Information($"Retrieved {orders.Count} active orders for {category}");
                return orders;
            }
            catch (Exception ex)
            {
                _log.Error(ex, $"Error getting active orders for {category}");
                return Enumerable.Empty<OrderModel>();
            }
        }
    }
}
