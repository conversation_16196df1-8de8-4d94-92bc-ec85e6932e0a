using System;
using System.IO;
using System.Text.Json;
using System.Text.Json.Serialization;
using Bybit.Net;
using MyTraderSpace.Exchanges.Bybit;
using MyTraderSpace.Exchanges.Simulated;
using MyTraderSpace.Models;
using MyTraderSpace.Exchanges;
using MyTraderSpace.Logging;
using MyTraderSpace.Trading.Strategies;
using MyTraderSpace.Trading;

namespace MyTraderSpace.Configuration
{
    public class ConfigurationLoader
    {
        private readonly LogManager _log;
        private readonly string _configFilePath;
        private readonly string _strategyConfigFilePath;
        private AppConfig? _appConfig;
        private Dictionary<string, JsonElement>? _strategyConfigs;

        public ConfigurationLoader(string configFileName = "config.json", string strategyConfigFileName = "strategies.json")
        {
            _log = new LogManager(loggerName: nameof(ConfigurationLoader), filePath: "ConfigLoader/ConfigLoader-.log");
            _configFilePath = Path.GetFullPath(Path.Combine(AppContext.BaseDirectory, configFileName));
            _strategyConfigFilePath = Path.GetFullPath(Path.Combine(AppContext.BaseDirectory, strategyConfigFileName));
            _log.Information($"Config file path: {_configFilePath}");
            _log.Information($"Strategy config file path: {_strategyConfigFilePath}");
        }

        public async Task LoadConfigAsync()
        {
            _log.Information("Loading configuration...");
            try
            {
                // Load main config.json
                if (!File.Exists(_configFilePath))
                {
                    _log.Warning($"Configuration file not found: {_configFilePath}. Creating default configuration.");
                    _appConfig = CreateDefaultAppConfiguration();
                    await SaveAppConfigurationAsync(_appConfig, _configFilePath);
                }
                else
                {
                    try
                    {
                        string json = await File.ReadAllTextAsync(_configFilePath);
                        _appConfig = JsonSerializer.Deserialize<AppConfig>(json, GetJsonSerializerOptions());
                        if (_appConfig == null)
                        {
                            _log.Error("Failed to deserialize config.json. Using default configuration and attempting to overwrite corrupted file.");
                            _appConfig = CreateDefaultAppConfiguration();
                            await SaveAppConfigurationAsync(_appConfig, _configFilePath); // Overwrite potentially corrupted file
                        }
                        else
                        {
                            _log.Information("config.json loaded successfully.");
                            // --- Parse TradingPair once after loading --- 
                            if (string.IsNullOrWhiteSpace(_appConfig.TradingPair))
                            {
                                _log.Error("Configuration Error: 'TradingPair' cannot be null or empty in config.json.");
                                throw new InvalidOperationException("'TradingPair' is missing or empty in configuration.");
                            }
                            try
                            {
                                _appConfig.TradingPairParsed = CommonPairs.ParseSymbolToCurrencyPair(_appConfig.TradingPair);
                                _log.Information($"Successfully parsed TradingPair '{_appConfig.TradingPair}' to CurrencyPair.");
                            }
                            catch (ArgumentException ex)
                            {
                                _log.Error(ex, $"Configuration Error: Invalid 'TradingPair' value '{_appConfig.TradingPair}' in config.json.");
                                throw new InvalidOperationException($"Invalid 'TradingPair' '{_appConfig.TradingPair}' configured.", ex);
                            }
                            // --- End Parse TradingPair --- 
                        }
                    }
                    catch (Exception ex)
                    {
                        _log.Error(ex, $"Error reading or deserializing config.json at {_configFilePath}. Using default configuration and attempting to overwrite file.");
                        _appConfig = CreateDefaultAppConfiguration();
                        await SaveAppConfigurationAsync(_appConfig, _configFilePath); // Overwrite potentially corrupted file
                    }
                }

                // Load strategies.json
                if (!File.Exists(_strategyConfigFilePath))
                {
                    _log.Warning($"Strategy configuration file not found: {_strategyConfigFilePath}. Creating default configuration.");
                    _strategyConfigs = CreateDefaultStrategyConfiguration();
                    await SaveStrategyConfigurationAsync(_strategyConfigs, _strategyConfigFilePath);
                }
                else
                {
                    try
                    {
                        string strategyJson = await File.ReadAllTextAsync(_strategyConfigFilePath);
                        _strategyConfigs = JsonSerializer.Deserialize<Dictionary<string, JsonElement>>(strategyJson, GetJsonSerializerOptions());
                        if (_strategyConfigs == null)
                        {
                            _log.Error("Failed to deserialize strategies.json. Using default/empty strategy configuration and attempting to overwrite corrupted file.");
                            _strategyConfigs = CreateDefaultStrategyConfiguration(); // Use default
                            await SaveStrategyConfigurationAsync(_strategyConfigs, _strategyConfigFilePath);
                        }
                        else
                        {
                            _log.Information($"strategies.json loaded successfully. Found configurations for: {string.Join(", ", _strategyConfigs.Keys)}");
                        }
                    }
                    catch (Exception ex)
                    {
                        _log.Error(ex, $"Error reading or deserializing strategies.json at {_strategyConfigFilePath}. Using default/empty strategy configuration and attempting to overwrite file.");
                        _strategyConfigs = CreateDefaultStrategyConfiguration(); // Use default
                        await SaveStrategyConfigurationAsync(_strategyConfigs, _strategyConfigFilePath);
                    }
                }
            }
            catch (Exception ex)
            {
                _log.Error(ex, "Critical error during configuration loading/saving process.");
                _appConfig ??= CreateDefaultAppConfiguration(); // Ensure _appConfig is not null
                _strategyConfigs ??= CreateDefaultStrategyConfiguration(); // Ensure _strategyConfigs is not null
            }
        }

        public AppConfig GetAppConfig()
        {
            return _appConfig ?? throw new InvalidOperationException("Configuration has not been loaded.");
        }
        
        /// <summary>
        /// Retrieves and deserializes the configuration for a specific strategy.
        /// </summary>
        /// <typeparam name="T">The type of the strategy configuration class.</typeparam>
        /// <param name="strategyName">The name of the strategy (key in strategies.json).</param>
        /// <returns>The deserialized strategy configuration object.</returns>
        /// <exception cref="InvalidOperationException">Thrown if configuration hasn't been loaded.</exception>
        /// <exception cref="KeyNotFoundException">Thrown if the strategyName is not found in the loaded configurations.</exception>
        /// <exception cref="JsonException">Thrown if deserialization into type T fails.</exception>
        public T GetStrategyConfig<T>(string strategyName)
        {
            if (_appConfig == null || _strategyConfigs == null)
            {
                throw new InvalidOperationException("Configuration has not been loaded.");
            }

            if (!_strategyConfigs.TryGetValue(strategyName, out var configElement))
            {
                 throw new KeyNotFoundException($"Configuration for strategy '{strategyName}' not found in strategies.json.");
            }

            try
            {
                // Deserialize the specific JsonElement into the requested type T
                T? strategyConfig = configElement.Deserialize<T>(GetJsonSerializerOptions());
                if (strategyConfig == null)
                {
                     throw new JsonException($"Failed to deserialize configuration for strategy '{strategyName}' into type {typeof(T).Name}.");
                }
                _log.Information($"Successfully retrieved and deserialized configuration for strategy '{strategyName}'.");
                return strategyConfig;
            }
            catch (JsonException jsonEx)
            {
                _log.Error(jsonEx, $"Error deserializing configuration for strategy '{strategyName}' into type {typeof(T).Name}.");
                throw; // Re-throw to indicate failure
            }
        }

        /// <summary>
        /// Creates a market data service based on configuration for a specific mode.
        /// <paramref name="modeName"/> can be "Simulation", "Demo", or "Live"
        /// </summary>
        public IMarketDataService CreateMarketDataService(string modeName)
        {
            var appConfig = GetAppConfig(); // Throws if not loaded
            if (!appConfig.Modes.TryGetValue(modeName, out var modeConfig))
            {
                throw new KeyNotFoundException($"Trading mode '{modeName}' not found in configuration.");
            }

            // Throw exception if MarketData config is missing for the mode
            if (modeConfig.MarketData == null)
            {
                _log.Error($"Configuration Error: MarketData section is missing for mode '{modeName}' in config.json.");
                throw new InvalidOperationException($"MarketData configuration is missing for mode '{modeName}'.");
            }
            var marketDataConfig = modeConfig.MarketData;

            // Use the pre-parsed TradingPair from AppConfig
            CurrencyPair tradingPair = appConfig.TradingPairParsed; 
            
            _log.Information($"Creating Market Data Service for mode '{modeName}', type: {marketDataConfig.Type}, pair: {tradingPair.Symbol}");

            switch (marketDataConfig.Type?.ToLower())
            {
                case "bybit":
                    var bybitEnvironment = BybitEnvironment.Live; //ParseBybitEnvironmentInternal(marketDataConfig.Environment ?? "Live"); // Attention/Warning/Important: Bybit uses 'Live' for DemoTrading also !!
                    // Ensure BybitMarketDataService exists and has this constructor
                    return new BybitMarketDataService(tradingPair, bybitEnvironment);
                
                case "simulated":
                default:
                    var simConfig = new SimulatedMarketDataConfig
                    {
                        InitialPrice = marketDataConfig.InitialPrice ?? 100000m,
                        UpdateInterval = TimeSpan.FromMilliseconds(marketDataConfig.UpdateIntervalMs ?? 500),
                        PriceVolatility = marketDataConfig.PriceVolatility ?? 0.002m,
                        SpreadPercent = marketDataConfig.SpreadPercent ?? 0.0002m,
                        SimulateFutures = marketDataConfig.SimulateFutures ?? true,
                        LoadDirectory = marketDataConfig.DefaultDataDirectory // Use DefaultDataDirectory
                    };
                    // Ensure SimulatedMarketDataService exists and has this constructor
                    return new SimulatedMarketDataService(tradingPair, simConfig);
            }
        }
        
        /// <summary>
        /// Creates an exchange API instance based on configuration for a specific exchange within a mode.
        /// <paramref name="modeName"/> can be "Simulation", "Demo", or "Live"
        /// </summary>
        public BaseExchangeAPI CreateExchangeAPI(string modeName, string exchangeName, IMarketDataService marketDataService)
        {
            var appConfig = GetAppConfig(); // Throws if not loaded
            if (!appConfig.Modes.TryGetValue(modeName, out var modeConfig))
            {
                throw new KeyNotFoundException($"Trading mode '{modeName}' not found in configuration.");
            }

            var exchangeEntry = modeConfig.Exchanges?.FirstOrDefault(e => e.Name?.Equals(exchangeName, StringComparison.OrdinalIgnoreCase) ?? false); // Match by Name
            if (exchangeEntry == null)
            {
                throw new KeyNotFoundException($"Exchange entry with name '{exchangeName}' not found in mode '{modeName}'.");
            }

            // --- Get API Name from Config ---
            // Use the Name from config, fallback to Type if Name is null/empty (though Name is preferred)
            string apiInstanceName = exchangeEntry.Name; // Name is now guaranteed by FirstOrDefault above
            if (string.IsNullOrWhiteSpace(apiInstanceName))
            {
                 // This case should ideally not happen if Name is required in config, but handle defensively
                 apiInstanceName = $"{exchangeEntry.Type ?? "UnknownType"}_{Guid.NewGuid().ToString().Substring(0, 4)}";
                 _log.Warning($"Exchange entry found by name '{exchangeName}' but its Name property is empty in config. Using generated name: {apiInstanceName}");
            }
            // --- End Get API Name ---

            // Use the pre-parsed TradingPair from AppConfig
            CurrencyPair tradingPair = appConfig.TradingPairParsed;

            string exchangeType = exchangeEntry.Type ?? "Simulated";
            var configDict = exchangeEntry.Config ?? new Dictionary<string, object>();

            _log.Information($"Creating Exchange API for mode '{modeName}', name: '{apiInstanceName}', type: {exchangeType}, pair: {tradingPair.Symbol}");

            switch (exchangeType.ToLower())
            {
                case "bybit":
                    // --- Pass Name to internal creation helper ---
                    return CreateBybitExchangeAPIInternal(apiInstanceName, configDict, tradingPair, marketDataService);

                case "simulated":
                default:
                     // --- Pass Name to internal creation helper ---
                    return CreateSimulatedExchangeAPIInternal(apiInstanceName, configDict, tradingPair, marketDataService);
            }
        }

        // --- Internal API Creation Helpers ---

        // --- Updated to accept and pass name ---
        private BybitExchangeAPI CreateBybitExchangeAPIInternal(string apiInstanceName, Dictionary<string, object> configDict, CurrencyPair tradingPair, IMarketDataService marketDataService)
        {
            _log.Information($"Mapping dictionary config to BybitExchangeConfig for API: {apiInstanceName}.");
            var config = new BybitExchangeConfig
            {
                LogLevel = GetDictValueEnumInternal<LogLevel>(configDict, "logLevel", LogLevel.Information),
                KeysFile = GetDictValueInternal<string>(configDict, "keysFile", null),
                Environment = ParseBybitEnvironmentInternal(GetDictValueInternal<string>(configDict, "environment", "DemoTrading")),
                PositionMode = GetDictValueEnumInternal<PositionMode>(configDict, "positionMode", PositionMode.BothSides),
                TradeMode = GetDictValueEnumInternal<TradeMode>(configDict, "tradeMode", TradeMode.CrossMargin),
                MarginMode = GetDictValueEnumInternal<MarginMode>(configDict, "marginMode", MarginMode.RegularMargin),
                Leverage = GetDictValueInternal<decimal>(configDict, "leverage", 10m)
            };

            if ((config.Environment == BybitEnvironment.Live) || (config.Environment == BybitEnvironment.DemoTrading) && string.IsNullOrEmpty(config.KeysFile))
            {
                _log.Error($"Bybit API '{apiInstanceName}' configured for '{config.Environment}' mode requires 'keysFile' to be specified.");
                throw new InvalidOperationException($"Bybit API '{apiInstanceName}' ('{config.Environment}') requires 'keysFile'.");
            }

            // Pass name to constructor
            return new BybitExchangeAPI(apiInstanceName, tradingPair, marketDataService, config);
        }

        // --- Updated to accept and pass name ---
        private SimulatedExchangeAPI CreateSimulatedExchangeAPIInternal(string apiInstanceName, Dictionary<string, object> configDict, CurrencyPair tradingPair, IMarketDataService marketDataService)
        {
            _log.Information($"Mapping dictionary config to SimulatedExchangeConfig for API: {apiInstanceName}.");
            var config = new SimulatedExchangeConfig
            {
                LogLevel = GetDictValueEnumInternal<LogLevel>(configDict, "logLevel", LogLevel.Information),
                InitialBaseBalance = GetDictValueInternal<decimal>(configDict, "initialBaseBalance", 0.1m),
                InitialQuoteBalance = GetDictValueInternal<decimal>(configDict, "initialQuoteBalance", 100000m),
                MarketOrderSlippage = GetDictValueInternal<decimal>(configDict, "marketOrderSlippage", 0.001m),
                MaintenanceMarginRate = GetDictValueInternal<decimal>(configDict, "maintenanceMarginRate", 0.005m),
                MaxLeverage = GetDictValueInternal<decimal>(configDict, "maxLeverage", 100m),
                PositionMode = GetDictValueEnumInternal<PositionMode>(configDict, "positionMode", PositionMode.BothSides),
                TradeMode = GetDictValueEnumInternal<TradeMode>(configDict, "tradeMode", TradeMode.CrossMargin),
                MarginMode = GetDictValueEnumInternal<MarginMode>(configDict, "marginMode", MarginMode.RegularMargin),
                Leverage = GetDictValueInternal<decimal>(configDict, "leverage", 10m),
                MinIntervalBetweenMarketTickProcessing = TimeSpan.FromMilliseconds(GetDictValueInternal<int>(configDict, "minIntervalBetweenMarketTickProcessingMs", 0)),
                ApiCallLatency = TimeSpan.FromMilliseconds(GetDictValueInternal<int>(configDict, "apiCallLatencyMs", 0))
            };
            // Pass name to constructor
            return new SimulatedExchangeAPI(apiInstanceName, tradingPair, marketDataService, config);
        }

        // --- Internal Helper Methods ---
        
        private T GetDictValueInternal<T>(Dictionary<string, object> dict, string key, T defaultValue)
        {
            if (dict.TryGetValue(key, out var value))
            {
                if (value is JsonElement element)
                {
                    try 
                    { 
                        // Handle specific case for TimeSpan from milliseconds int
                        if (typeof(T) == typeof(TimeSpan) && element.ValueKind == JsonValueKind.Number) {
                            return (T)(object)TimeSpan.FromMilliseconds(element.GetInt32());
                        }
                        return element.Deserialize<T>(GetJsonSerializerOptions()) ?? defaultValue; 
                    }
                    catch(Exception ex) 
                    { 
                        _log.Warning(ex, $"Failed to deserialize JSON element '{key}' for type {typeof(T).Name}. Using default.");
                        return defaultValue; 
                    }
                }
                try 
                { 
                    // Handle boolean conversion from string/number if T is bool
                    if (typeof(T) == typeof(bool)) { return (T)(object)ConvertToBooleanInternal(value); }
                    if (typeof(T) == typeof(decimal)) { return (T)(object)ConvertToDecimalInternal(value); }
                    // Add other specific type conversions if needed before generic ChangeType
                    return (T)Convert.ChangeType(value, typeof(T)); 
                }
                catch (Exception ex) 
                { 
                    _log.Warning(ex, $"Failed to convert dictionary value '{key}' for type {typeof(T).Name}. Using default.");
                    return defaultValue; 
                } 
            }
            return defaultValue;
        }
        
        private TEnum GetDictValueEnumInternal<TEnum>(Dictionary<string, object> dict, string key, TEnum defaultValue) where TEnum : struct, Enum
        {
            if (dict.TryGetValue(key, out var value))
            {
                string? stringValue = null;
                if (value is JsonElement element && element.ValueKind == JsonValueKind.String)
                {
                    stringValue = element.GetString();
                }
                else if (value is string str)
                {
                    stringValue = str;
                }

                if (stringValue != null && Enum.TryParse<TEnum>(stringValue, true, out var result))
                { 
                    return result; 
                }
                 else
                {
                    _log.Warning($"Failed to parse enum value '{value}' for key '{key}' and type {typeof(TEnum).Name}. Using default '{defaultValue}'.");
                }
            }
            return defaultValue;
        }

        private decimal ConvertToDecimalInternal(object value)
        {
            if (value is decimal decimalValue) return decimalValue;
            if (value is JsonElement jsonElement)
            {
                if (jsonElement.ValueKind == JsonValueKind.Number) return jsonElement.GetDecimal();
                if (jsonElement.ValueKind == JsonValueKind.String && decimal.TryParse(jsonElement.GetString(), out decimal result)) return result;
            }
            try { return Convert.ToDecimal(value); }
            catch (Exception ex) { _log.Warning(ex, $"Failed converting value '{value}' to decimal."); return 0m; }
        }

        private bool ConvertToBooleanInternal(object value)
        {
            if (value is bool boolValue) return boolValue;
            if (value is JsonElement jsonElement)
            {
                if (jsonElement.ValueKind == JsonValueKind.True) return true;
                if (jsonElement.ValueKind == JsonValueKind.False) return false;
                if (jsonElement.ValueKind == JsonValueKind.String && bool.TryParse(jsonElement.GetString(), out bool resultStr)) return resultStr;
                if (jsonElement.ValueKind == JsonValueKind.Number) return jsonElement.GetInt32() != 0;
            }
            try { return Convert.ToBoolean(value); }
            catch (Exception ex) { _log.Warning(ex, $"Failed converting value '{value}' to boolean."); return false; } 
        }

        private BybitEnvironment ParseBybitEnvironmentInternal(string environment)
        {
            return environment?.ToLower() switch
            {
                "demo" or "demotrading" => BybitEnvironment.DemoTrading,
                "live" or "production" => BybitEnvironment.Live,
                _ => BybitEnvironment.DemoTrading // Default to Demo
            };
        }

        private JsonSerializerOptions GetJsonSerializerOptions()
        {
            var options = new JsonSerializerOptions
            {
                PropertyNameCaseInsensitive = true,
                PropertyNamingPolicy = JsonNamingPolicy.CamelCase,
                WriteIndented = true,
                Converters = { new JsonStringEnumConverter(JsonNamingPolicy.CamelCase) }
            };
            return options;
        }

        private AppConfig CreateDefaultAppConfiguration()
        {
            _log.Information("Creating default AppConfig.");
            var config = new AppConfig
            {
                DefaultMode = "Demo",
                TradingPair = "BTCUSDT",
                Modes = new Dictionary<string, TradingModeConfig>
                {
                    {
                        "Simulation", new TradingModeConfig
                        {
                            MarketData = new MarketDataConfig
                            {
                                Type = "Simulated",
                                DefaultDataDirectory = "data/default",
                                InitialPrice = 100000m,
                                UpdateIntervalMs = 500,
                                PriceVolatility = 0.002m,
                                SpreadPercent = 0.0002m,
                                SimulateFutures = true
                            },
                            Exchanges = new List<ExchangeEntryConfig>
                            {
                                new ExchangeEntryConfig
                                {
                                    Type = "Simulated",
                                    Name = "Sim-1",
                                    Pools = new List<string> { "Futures" }, // { "Spot", "Futures" }
                                    Config = new Dictionary<string, object>
                                    {
                                        { "logLevel", LogLevel.Information },
                                        { "initialBaseBalance", 1.0m },
                                        { "initialQuoteBalance", 100000.0m },
                                        { "simulateSlippage", false },
                                        { "marketOrderSlippage", 0.001m },
                                        { "maintenanceMarginRate", 0.005m },
                                        { "maxLeverage", 100m },
                                        { "leverage", 10m },
                                        { "marginMode", "RegularMargin" },
                                        { "tradeMode", "CrossMargin" },
                                        { "positionMode", "BothSides" },
                                        { "minIntervalBetweenMarketTickProcessingMs", 0 },
                                        { "apiCallLatencyMs", 0 }
                                    }
                                },
                                new ExchangeEntryConfig
                                {
                                    Type = "Simulated",
                                    Name = "Sim-2",
                                    Pools = new List<string> { "Futures" }, // { "Spot", "Futures" }
                                    Config = new Dictionary<string, object>
                                    {
                                        { "logLevel", LogLevel.Information },
                                        { "initialBaseBalance", 1.0m },
                                        { "initialQuoteBalance", 100000.0m },
                                        { "simulateSlippage", false },
                                        { "marketOrderSlippage", 0.001m },
                                        { "maintenanceMarginRate", 0.005m },
                                        { "maxLeverage", 100m },
                                        { "leverage", 10m },
                                        { "marginMode", "RegularMargin" },
                                        { "tradeMode", "CrossMargin" },
                                        { "positionMode", "BothSides" },
                                        { "minIntervalBetweenMarketTickProcessingMs", 0 },
                                        { "apiCallLatencyMs", 0 }
                                    }
                                },
                                new ExchangeEntryConfig
                                {
                                    Type = "Simulated",
                                    Name = "Sim-3",
                                    Pools = new List<string> { "Futures" }, // { "Spot", "Futures" }
                                    Config = new Dictionary<string, object>
                                    {
                                        { "logLevel", LogLevel.Information },
                                        { "initialBaseBalance", 1.0m },
                                        { "initialQuoteBalance", 100000.0m },
                                        { "simulateSlippage", false },
                                        { "marketOrderSlippage", 0.001m },
                                        { "maintenanceMarginRate", 0.005m },
                                        { "maxLeverage", 100m },
                                        { "leverage", 10m },
                                        { "marginMode", "RegularMargin" },
                                        { "tradeMode", "CrossMargin" },
                                        { "positionMode", "BothSides" },
                                        { "minIntervalBetweenMarketTickProcessingMs", 0 },
                                        { "apiCallLatencyMs", 0 }
                                    }
                                }
                            }
                        }
                    },
                    {
                        "Demo", new TradingModeConfig
                        {
                            MarketData = new MarketDataConfig
                            {
                                Type = "Bybit",
                                Environment = "Live" // **VERY IMPORTANT!**: Bybit DemoTrading works only with Live ticker data!
                            },
                            Exchanges = new List<ExchangeEntryConfig>
                            {
                                new ExchangeEntryConfig
                                {
                                    Type = "Bybit",
                                    Name = "Main Demo",
                                    Pools = new List<string> { "Spot" },
                                    Config = new Dictionary<string, object>
                                    {
                                        { "logLevel", LogLevel.Information },
                                        { "keysFile", "keys/main-demo.bin" },
                                        { "environment", "DemoTrading" },
                                        { "marginMode", "RegularMargin" },
                                        { "tradeMode", "CrossMargin" },
                                        { "positionMode", "BothSides" }
                                    }
                                },
                                new ExchangeEntryConfig
                                {
                                    Type = "Bybit",
                                    Name = "Futures Demo 1",
                                    Pools = new List<string> { "Futures" },
                                    Config = new Dictionary<string, object>
                                    {
                                        { "logLevel", LogLevel.Information },
                                        { "keysFile", "keys/uni-1-demo.bin" },
                                        { "environment", "DemoTrading" },
                                        { "leverage", 50.0m },
                                        { "positionMode", "BothSides" } 
                                    }
                                },
                                new ExchangeEntryConfig
                                {
                                    Type = "Bybit",
                                    Name = "Futures Demo 2",
                                    Pools = new List<string> { "Futures" },
                                    Config = new Dictionary<string, object>
                                    {
                                        { "logLevel", LogLevel.Information },
                                        { "keysFile", "keys/uni-2-demo.bin" },
                                        { "environment", "DemoTrading" },
                                        { "positionMode", "BothSides" }
                                    }
                                },
                                new ExchangeEntryConfig
                                {
                                    Type = "Bybit",
                                    Name = "Futures Demo 3",
                                    Pools = new List<string> { "Futures" },
                                    Config = new Dictionary<string, object>
                                    {
                                        { "logLevel", LogLevel.Information },
                                        { "keysFile", "keys/uni-3-demo.bin" },
                                        { "environment", "DemoTrading" },
                                        { "positionMode", "BothSides" }
                                    }
                                }
                            }
                        }
                    },
                    {
                        "Live", new TradingModeConfig
                        {
                            MarketData = new MarketDataConfig
                            {
                                Type = "Bybit",
                                Environment = "Live"
                            },
                            Exchanges = new List<ExchangeEntryConfig>
                            {
                                new ExchangeEntryConfig
                                {
                                    Type = "Bybit",
                                    Name = "Live Spot 1",
                                    Pools = new List<string> { "Spot" },
                                    Config = new Dictionary<string, object>
                                    {
                                        { "logLevel", LogLevel.Information },
                                        { "keysFile", "keys/prod_spot_keys_1.bin" },
                                        { "environment", "Live" }
                                    }
                                },
                                new ExchangeEntryConfig
                                {
                                    Type = "Bybit",
                                    Name = "Live Spot 2",
                                    Pools = new List<string> { "Spot" },
                                    Config = new Dictionary<string, object>
                                    {
                                        { "logLevel", LogLevel.Information },
                                        { "keysFile", "keys/prod_spot_keys_2.bin" },
                                        { "environment", "Live" }
                                    }
                                },
                                new ExchangeEntryConfig
                                {
                                    Type = "Bybit",
                                    Name = "Live Futures 1",
                                    Pools = new List<string> { "Futures" },
                                    Config = new Dictionary<string, object>
                                    {
                                        { "logLevel", LogLevel.Information },
                                        { "keysFile", "keys/prod_futures_keys_1.bin" },
                                        { "environment", "Live" },
                                        { "positionMode", "BothSides"}
                                    }
                                },
                                new ExchangeEntryConfig
                                {
                                    Type = "Bybit",
                                    Name = "Live Futures 2",
                                    Pools = new List<string> { "Futures" },
                                    Config = new Dictionary<string, object>
                                    {
                                        { "logLevel", LogLevel.Information },
                                        { "keysFile", "keys/prod_futures_keys_2.bin" },
                                        { "environment", "Live" },
                                        { "positionMode", "BothSides" }
                                    }
                                }
                            }
                        }
                    }
                },
                Parameters = new ParametersConfig
                {
                    DummyParam1 = 0.001m,
                    DummyParam2 = 0.005m,
                    DummyParam3 = 0.001m,
                    CheckStuffConfig = new CheckStuffConfig() // Ensure default is created
                    {
                        IntervalMinutes = 1 // Explicitly set default interval
                    }
                },
                Logging = new LoggingConfig
                {
                    MinimumLevel = "Debug",
                    ConsoleEnabled = true,
                    FileEnabled = true,
                    FilePath = "mytrader-.txt",
                    RollingInterval = "Day"
                }
            };
            return config;
        }
        
        private async Task SaveAppConfigurationAsync(AppConfig config, string configPath)
        {
            _log.Information($"Saving AppConfig to {configPath}...");
            try
            {
                string? directory = Path.GetDirectoryName(configPath);
                if (!string.IsNullOrEmpty(directory) && !Directory.Exists(directory)) { Directory.CreateDirectory(directory); }
                
                string json = JsonSerializer.Serialize(config, GetJsonSerializerOptions());
                await File.WriteAllTextAsync(configPath, json);
                _log.Information("AppConfig saved successfully.");
            }
            catch (Exception ex) { _log.Error(ex, "Error saving AppConfig."); }
        }

        private Dictionary<string, JsonElement> CreateDefaultStrategyConfiguration()
        {
            _log.Information("Creating default strategy configurations (MainStrategy, HedgeGridStrategy)...");
            
            var mainStrategyDefaultConfig = new MainStrategyConfig(); // Default MainStrategy config
            var hedgeGridDefaultConfig = new HedgeGridStrategyConfig(); // Default HedgeGridStrategy config
            
            var strategyDict = new Dictionary<string, object>
            {
                { nameof(MainStrategy), mainStrategyDefaultConfig },
                { nameof(HedgeGridStrategy), hedgeGridDefaultConfig }
            };
            
            // Serialize the dictionary<string, object> and then deserialize back to Dictionary<string, JsonElement>
            // This ensures the structure matches what's expected from reading the file.
            string json = JsonSerializer.Serialize(strategyDict, GetJsonSerializerOptions());
            return JsonSerializer.Deserialize<Dictionary<string, JsonElement>>(json, GetJsonSerializerOptions()) 
                   ?? new Dictionary<string, JsonElement>(); // Fallback
        }

        private async Task SaveStrategyConfigurationAsync(Dictionary<string, JsonElement> configs, string configPath)
        {
            _log.Information($"Saving strategy configurations to {configPath}...");
            try
            {
                string? directory = Path.GetDirectoryName(configPath);
                if (!string.IsNullOrEmpty(directory) && !Directory.Exists(directory)) { Directory.CreateDirectory(directory); }
                
                string json = JsonSerializer.Serialize(configs, GetJsonSerializerOptions());
                await File.WriteAllTextAsync(configPath, json);
                _log.Information("Strategy configurations saved successfully.");
            }
            catch (Exception ex)
            {
                _log.Error(ex, "Error saving strategy configurations.");
            }
        }
    }
} 