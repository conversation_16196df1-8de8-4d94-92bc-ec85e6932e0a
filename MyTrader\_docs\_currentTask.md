﻿# The current task is to create a super-polished version of OrderPair.cs as/in OrderPair_New.cs HedgeGridStrategy.cs as/in HedgeGridStrategy_New.cs
and MainStrategy.cs as/in MainStrategy_New.cs (eventually when ready the _New will be renamed to the 'actuals')
In these new implementations, we try to avoid as much as possible the use of unreliable events, this means minimizing the events usages
but they are not excluded, but we use the most robust event Func<T, Task> format (if I know correctly that is the best way, otherwise let me know) 
when necessary and meaningful and results in a proper elegant and robust code.
The new implementations has to be as straightforward as possible, with minimum places of possible race conditions, as the current implementations 
can result in undetermined results due to subtle race conditions.
In the implementations we start with the core things, and step by step implement the details