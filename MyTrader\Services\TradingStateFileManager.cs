using System;
using System.IO;
using System.Text.Json;
using System.Text.Json.Serialization;
using System.Threading.Tasks;
using MyTraderSpace.Logging;
using MyTraderSpace.Models;

namespace MyTraderSpace.Services
{
    public class TradingStateFileManager : IDisposable
    {
        private readonly LogManager _log;
        private readonly string _baseDirectory;
        private readonly JsonSerializerOptions _jsonOptions;

        public TradingStateFileManager(string baseDirectory = "TradingState")
        {
            _log = new LogManager(nameof(TradingStateFileManager));
            _baseDirectory = Path.GetFullPath(Path.Combine(AppContext.BaseDirectory, baseDirectory));

            if (!Directory.Exists(_baseDirectory))
            {
                try
                {
                    Directory.CreateDirectory(_baseDirectory);
                    _log.Information($"Base directory for trading state created at: '{_baseDirectory}'");
                }
                catch (Exception ex)
                {
                    _log.Error(ex, $"Failed to create base directory for trading state at: '{_baseDirectory}'");
                    // Depending on desired robustness, could throw or operate without saving/loading
                }
            }

            _jsonOptions = new JsonSerializerOptions
            {
                PropertyNameCaseInsensitive = true,
                PropertyNamingPolicy = JsonNamingPolicy.CamelCase,
                WriteIndented = true,
                Converters = { new JsonStringEnumConverter(JsonNamingPolicy.CamelCase) },
                //ReferenceHandler = ReferenceHandler.Preserve // Potentially useful for complex object graphs, but can make JSON less readable. Let's start without.
            };
            // Ensure MyTraderSpace.Models.CurrencyPairConverter is handled if CurrencyPair is directly in FullTradingState (it's not currently, but Fee is)
            // For Fee, which contains CurrencyPair, if CurrencyPair itself is not directly serialized but Fee is, its custom converter should be picked up by System.Text.Json if Fee is structured correctly.
            // However, CurrencyPairConverter is for System.Text.Json, and the one in the project is for Newtonsoft.Json.
            // For simplicity, FullTradingState stores TradingPairSymbol as a string.
            // The Fee object in Models.cs would need to be serializable by System.Text.Json correctly.
            // Let's assume Fee can be serialized; if not, we'll need to adjust its definition or add a custom converter for System.Text.Json.
        }

        private string GetFilePath(string tradingPairSymbol, DateTime timestamp)
        {
            string sanitizedSymbol = tradingPairSymbol.Replace("/", "");
            string fileName = $"trading_state_{sanitizedSymbol}.json";
            string fullPath = Path.Combine(_baseDirectory, fileName);
            _log.Information($"FILE PATH: Generated file path for symbol {tradingPairSymbol}: {fullPath}");
            return fullPath;
        }

        // ToDo / Note: Remember to replace 'dynamic' with the actual state class
        public async Task SaveStateAsync(dynamic state)
        {
            if (state == null)
            {
                _log.Warning("SaveStateAsync called with null state. Nothing to save.");
                return;
            }

            string filePath = GetFilePath(state.TradingPairSymbol, DateTime.UtcNow);
            string tempFilePath = filePath + ".tmp";

            try
            {
                _log.Information($"Attempting to save trading state to: {filePath}");
                byte[] jsonUtf8Bytes = JsonSerializer.SerializeToUtf8Bytes(state, _jsonOptions);

                await File.WriteAllBytesAsync(tempFilePath, jsonUtf8Bytes);

                if (File.Exists(filePath))
                {
                    File.Delete(filePath); // Or File.Replace for atomic operation if preferred and supported on target FS
                }
                File.Move(tempFilePath, filePath);

                _log.Information($"Trading state saved successfully to: {filePath}");
            }
            catch (Exception ex)
            {
                _log.Error(ex, $"Failed to save trading state to {filePath}.");
                // Clean up temp file if it exists
                if (File.Exists(tempFilePath))
                {
                    try
                    {
                        File.Delete(tempFilePath);
                    }
                    catch (Exception iex)
                    {
                        _log.Warning(iex, $"Failed to delete temp file {tempFilePath} after save error.");
                    }
                }
            }
        }

        // ToDo / Note: Remember to replace 'dynamic' with the actual state class
        public async Task<dynamic?> LoadLatestStateAsync(string tradingPairSymbol)
        {
            try
            {
                _log.Information($"LOAD STATE: Attempting to load trading state for symbol: {tradingPairSymbol}");
                _log.Information($"LOAD STATE: Base directory: {_baseDirectory}");

                if (!Directory.Exists(_baseDirectory))
                {
                    _log.Information($"LOAD STATE: Trading state directory '{_baseDirectory}' does not exist. No state to load.");
                    return null;
                }

                _log.Information($"LOAD STATE: Trading state directory exists");

                string filePath = GetFilePath(tradingPairSymbol, DateTime.UtcNow);
                _log.Information($"LOAD STATE: Looking for file at path: {filePath}");

                if (!File.Exists(filePath))
                {
                    _log.Information($"LOAD STATE: Trading state file not found for symbol {tradingPairSymbol}: {filePath}.");
                    return null;
                }

                _log.Information($"LOAD STATE: Trading state file found");
                _log.Information($"LOAD STATE: Attempting to load latest trading state from: {filePath}");

                byte[] jsonUtf8Bytes = await File.ReadAllBytesAsync(filePath);
                _log.Information($"LOAD STATE: Read {jsonUtf8Bytes.Length} bytes from file");

                var state = JsonSerializer.Deserialize<dynamic>(jsonUtf8Bytes, _jsonOptions); // ToDo / Note: Remember to replace 'dynamic' with the actual state class

                if (state == null)
                {
                    _log.Error($"LOAD STATE: Deserialized state is null");
                    return null;
                }

                _log.Information($"LOAD STATE: Trading state loaded successfully from: {filePath}");
                _log.Information($"LOAD STATE: Loaded state timestamp: {state.Timestamp}");
                _log.Information($"LOAD STATE: Loaded state trading pair: {state.TradingPairSymbol}");
                _log.Information($"LOAD STATE: Loaded state has {state.Positions?.Count ?? 0} positions");
                _log.Information($"LOAD STATE: Loaded state has {state.StrategyState?.TradePoints?.Count ?? 0} trade points");

                return state;
            }
            catch (Exception ex)
            {
                _log.Error(ex, $"LOAD STATE ERROR: Failed to load latest trading state for symbol {tradingPairSymbol}.");
                return null;
            }
        }

        public void Dispose()
        {
            _log?.Dispose();
        }
    }
}