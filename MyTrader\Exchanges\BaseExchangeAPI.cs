using System;
using System.Collections.Generic;
using System.Collections.Concurrent;
using System.Linq;
using System.Threading.Tasks;
using MyTraderSpace.Models;
using MyTraderSpace.Logging;
using MyTraderSpace.Utils;

namespace MyTraderSpace.Exchanges
{
    // TODO for later: Implement global cancellation token support for all API calls
    // This would allow cancelling all in-flight API calls during application shutdown
    // Would need to:
    // 1. Add a CancellationTokenSource property to BaseExchangeAPI
    // 2. Pass the token to all API calls that support it
    // 3. Add a method to cancel all operations during shutdown
    // 4. Use the ApiCall timeout from config for all API calls

    public abstract class BaseExchangeAPI : IDisposable
    {
        //Type ExchangeType { get; init; } = typeof(BaseExchangeAPI);
        public virtual ExchangeType ExchangeType { get; init; } = ExchangeType.Simulated; // Default to Simulated for BaseExchangeAPI
        /// <summary>
        /// Gets the user-defined name for this exchange instance, typically from configuration.
        /// </summary>
        public string Name { get; init; }
        public abstract ExchangeState State { get; protected set; }
        public CurrencyPair TradingPair { get; init; }
        public virtual ExchangeConfig Config => _config;
        protected string Symbol => TradingPair.Symbol;

        protected readonly IMarketDataService _marketDataService;
        protected readonly ExchangeConfig _config;
        protected readonly LogManager _log;
        protected virtual Wallet Wallet { get; set; }
        public Wallet GetWallet() => Wallet;

        // Position mode is a fundamental account setting that should only be changed via SetPositionModeAsync
        // It should NOT be updated based on position updates, as those may report inconsistent values
        protected PositionMode _currentPositionMode;

        // Margin mode is a fundamental account setting that should only be changed via SetMarginModeAsync
        protected MarginMode _currentMarginMode;

        // Trade mode is a fundamental account setting that should only be changed via SetTradeModeAsync
        protected TradeMode _currentTradeMode;

        // Leverage values are fundamental account settings that should only be changed via SetLeverageAsync
        protected decimal _currentBuyLeverage;
        protected decimal _currentSellLeverage;

        protected FuturesMarketData? _latestFuturesData; // Added to store latest futures market data

        /// <summary>
        /// Creates a unique key for a position based on symbol and side
        /// </summary>
        protected virtual string GetPositionKey(string symbol, PositionDirection? direction) => $"{symbol}_{direction}";

        // Order and position tracking
        protected ConcurrentDictionary<string, OrderModel> _activeOrders = new();
        protected ConcurrentDictionary<string, PositionModel> _openPositions = new();
        protected ConcurrentDictionary<CurrencyPair, Fee> _sessionFees = new();
        protected ConcurrentDictionary<CurrencyPair, decimal> _sessionRealizedPnL = new();

        /// <summary>
        /// Gets all active orders (orders that are not in a final state)
        /// </summary>
        public virtual IEnumerable<OrderModel> GetActiveOrders() => _activeOrders.Values;

        /// <summary>
        /// Gets all open positions (positions with non-zero quantity)
        /// </summary>
        public virtual IEnumerable<PositionModel> GetOpenPositions() => _openPositions.Values;

        /// <summary>
        /// Gets the realized PnL for the current trading session
        /// </summary>
        public virtual decimal GetSessionRealizedPnl(CurrencyPair? currencyPair = null)
        {
            return _sessionRealizedPnL.TryGetValue(currencyPair ?? TradingPair, out decimal pnl) ? pnl : 0;
        }

        public FeeConfig GetFeeRates() => _config.Fees;

        /// <summary>
        /// Gets the total fees paid in the current trading session
        /// </summary>
        public virtual Fee GetSessionFees(CurrencyPair? currencyPair = null)
        {
            currencyPair ??= TradingPair;
            return _sessionFees.TryGetValue((CurrencyPair)currencyPair, out var fee) ? fee : new Fee((CurrencyPair)currencyPair);
        }

        public virtual decimal GetSessionFee(CurrencyPair? currencyPair = null)
        {
            currencyPair ??= TradingPair;
            return _sessionFees.TryGetValue((CurrencyPair)currencyPair, out var fee) ? fee.Quote.Amount : 0;
        }

        /// <summary>
        /// Gets the unrealized PnL for all open positions for a specific Trading Pair.
        /// Defaults to the API's primary TradingPair if none is specified.
        /// </summary>
        /// <param name="currencyPair">The currency pair to calculate unrealized PnL for. Defaults to the instance's TradingPair.</param>
        /// <returns>The total unrealized PnL for the specified pair.</returns>
        public virtual decimal GetUnrealizedPnl(CurrencyPair? currencyPair = null)
        {
            currencyPair ??= TradingPair;
            return _openPositions.Values
                .Where(p => p.Symbol == currencyPair.Value.Symbol)
                .Sum(p => p.UnrealizedPnl ?? 0);
        }

        /// <summary>
        /// Gets the net PnL (realized + unrealized) for a specific Trading Pair.
        /// Defaults to the API's primary TradingPair if none is specified.
        /// </summary>
        /// <param name="currencyPair">The currency pair to calculate net PnL for. Defaults to the instance's TradingPair.</param>
        /// <returns>The net PnL for the specified pair.</returns>
        public virtual decimal GetNetPnl(CurrencyPair? currencyPair = null)
        {
            currencyPair ??= TradingPair;
            return GetSessionRealizedPnl(currencyPair) + GetUnrealizedPnl(currencyPair);
        }

        // Market data events
        // Note: subscribe to these first, after call InitializeAsync() !! (Note2: these are for 'external' subscriptions!)
        public virtual event Action<SpotMarketData>? OnSpotMarketUpdate;
        public virtual event Action<FuturesMarketData>? OnFuturesMarketUpdate;
        public virtual event Action<WalletUpdate>? OnWalletUpdate;
        public virtual event Action<OrderModelUpdate>? OnOrderUpdate;
        public virtual event Action<PositionModelUpdate>? OnPositionUpdate;

        // Public interface methods
        public abstract Task InitializeAsync();
        public abstract Task StopAsync();
        public abstract Task<IEnumerable<AssetBalance>> GetBalancesAsync();
        public abstract Task<AssetBalance> GetBalanceAsync(string asset);

        protected BaseExchangeAPI(
            string name,
            CurrencyPair tradingPair,
            IMarketDataService marketDataService,
            LogManager logger,
            ExchangeConfig? config
            )
        {
            if (string.IsNullOrWhiteSpace(name))
            {
                throw new ArgumentException("Exchange API instance name cannot be empty.", nameof(name));
            }
            Name = name;
            TradingPair = tradingPair;
            _marketDataService = marketDataService ?? throw new ArgumentNullException(nameof(marketDataService));
            _log = logger ?? throw new ArgumentNullException(nameof(logger));
            _config = config ?? new ExchangeConfig();
            Wallet = new Wallet(marketDataService);

            // Initialize account settings from config
            _currentPositionMode = _config.PositionMode;
            _currentMarginMode = _config.MarginMode;
            _currentTradeMode = _config.TradeMode;
            _currentBuyLeverage = _config is Bybit.BybitExchangeConfig bybitConfig ? bybitConfig.Leverage : 1.0m;
            _currentSellLeverage = _currentBuyLeverage;

            _log.Information($"BaseExchangeAPI '{Name}' constructed for {TradingPair}.");
        }

        // Public trading operations that use protected methods
        public async Task<SpotOrderResult> OpenSpotPositionAsync(
            bool isBuy,
            decimal baseAmount,
            decimal? limitPrice = null,
            OrderType? orderType = null,
            TimeInForce timeInForce = TimeInForce.PostOnly,
            string? clientId = null)
        {
            var side = isBuy ? OrderSide.Buy : OrderSide.Sell;
            var effectiveOrderType = orderType ?? _config.OrderDefaults.SpotOrderType;
            var result = await PlaceSpotOrderAsync(Symbol, side, baseAmount, limitPrice, effectiveOrderType, timeInForce, clientId);

            if (result.IsSuccess && !IsOrderInFinalState(result.Status))
            {
                _log.Debug($"Order {result.OrderId} not in final state ({result.Status}), waiting for completion...");
                await WaitForOrderStatusAsync(result.OrderId, null, Category.Spot, TimeSpan.FromMilliseconds(_config.Timeouts.OrderStatusWait));

                try
                {
                    var updatedModel = await GetOrderStatusAsync(result.OrderId, Category.Spot, clientId);
                    if (updatedModel != null)
                    {
                        _log.Debug($"Retrieved updated status for order {result.OrderId}: {updatedModel.Status}");
                        return new SpotOrderResult
                        {
                            IsSuccess = result.IsSuccess,
                            OrderId = result.OrderId,
                            ClientOrderId = updatedModel.ClientOrderId,
                            Status = updatedModel.Status,
                            ExecutedQuantity = updatedModel.QuantityFilled,
                            ExecutedPrice = updatedModel.AveragePrice,
                            Message = result.Message,
                            Timestamp = updatedModel.UpdateTime
                        };
                    }
                    else
                    {
                        _log.Warning($"Could not retrieve updated status for order {result.OrderId} after waiting. Returning original result.");
                    }
                }
                catch (Exception ex)
                {
                    _log.Error(ex, $"Failed to get updated status for order {result.OrderId} after waiting. Returning original result.");
                }
            }
            else
            {
                _log.Debug($"Order {result.OrderId} was already in final state ({result.Status}) or failed placement.");
            }

            return result;
        }

        public async Task<SpotOrderResult> OpenSpotPositionAsync(SpotOrderRequest request)
        {
            var side = request.IsBuy ? OrderSide.Buy : OrderSide.Sell;
            var effectiveOrderType = request.OrderType;
            var effectiveTimeInForce = request.TimeInForce;

            var result = await PlaceSpotOrderAsync(
                Symbol,
                side,
                request.Amount,
                request.Price,
                effectiveOrderType,
                effectiveTimeInForce,
                request.ClientId
            );

            if (result.IsSuccess && !IsOrderInFinalState(result.Status))
            {
                _log.Debug($"Order {result.OrderId} (Client: {request.ClientId}) not in final state ({result.Status}), waiting...");
                await WaitForOrderStatusAsync(result.OrderId, request.ClientId, Category.Spot, TimeSpan.FromMilliseconds(_config.Timeouts.OrderStatusWait));

                try
                {
                    var updatedModel = await GetOrderStatusAsync(result.OrderId, Category.Spot, request.ClientId);
                    if (updatedModel != null)
                    {
                        _log.Debug($"Retrieved updated status for order {result.OrderId} (Client: {request.ClientId}): {updatedModel.Status}");
                        return new SpotOrderResult
                        {
                            IsSuccess = result.IsSuccess,
                            OrderId = result.OrderId,
                            ClientOrderId = updatedModel.ClientOrderId,
                            Status = updatedModel.Status,
                            ExecutedQuantity = updatedModel.QuantityFilled,
                            ExecutedPrice = updatedModel.AveragePrice,
                            Message = result.Message,
                            Timestamp = updatedModel.UpdateTime
                        };
                    }
                    else
                    {
                        _log.Warning($"Could not retrieve updated status for order {result.OrderId} (Client: {request.ClientId}) after waiting.");
                    }
                }
                catch (Exception ex)
                {
                    _log.Error(ex, $"Failed to get updated status for order {result.OrderId} (Client: {request.ClientId}) after waiting.");
                }
            }
            else
            {
                _log.Debug($"Order {result.OrderId} (Client: {request.ClientId}) already final ({result.Status}) or failed placement.");
            }

            return result;
        }

        /// <summary>
        /// Opens a futures position with the specified parameters.
        /// </summary>
        /// <param name="isLong">True for long positions, false for short positions</param>
        /// <param name="baseAmount">Amount in base currency</param>
        /// <param name="leverage">Leverage to use. If null, the current leverage will be used.</param>
        /// <param name="limitPrice">Limit price for limit orders. Required for limit orders.</param>
        /// <param name="orderType">Order type (Market or Limit). If null, the default from config will be used.</param>
        /// <param name="timeInForce">Time in force for the order</param>
        /// <param name="positionDirection">
        /// Position direction (Buy or Sell) for hedge mode.
        /// Must be null for one-way mode (MergedSingle).
        /// Required for hedge mode (BothSides).
        /// If null in hedge mode, it will be derived from isLong.
        /// </param>
        /// <param name="reduceOnly">True if the order should be reduced only</param>
        /// <returns>Result of the futures order placement</returns>
        public async Task<FuturesOrderResult> OpenFuturesPositionAsync(
            bool isLong,
            decimal baseAmount,
            decimal? leverage = null,
            decimal? limitPrice = null,
            OrderType? orderType = null,
            TimeInForce timeInForce = TimeInForce.PostOnly,
            PositionDirection? positionDirection = null,
            bool reduceOnly = false,
            string? clientId = null,
            decimal? triggerPrice = null,
            TriggerType? triggerBy = null,
            TriggerDirection? triggerDirection = null)
        {
            var side = isLong ? OrderSide.Buy : OrderSide.Sell;
            var effectiveOrderType = orderType ?? _config.OrderDefaults.FuturesOrderType;

            if (!ValidatePositionDirectionAndMode(Symbol, _currentPositionMode, ref positionDirection, isLong, out string validationMessage))
            {
                return new FuturesOrderResult
                {
                    IsSuccess = false,
                    Status = OrderStatus.Rejected,
                    Message = validationMessage,
                    Timestamp = DateTime.UtcNow
                };
            }

            // ***********************************************************************************
            // CONVENIENCE: Auto-convert aggressive PostOnly Limit to Conditional Market
            // ***********************************************************************************
            if (triggerPrice == null && // Only if NOT explicitly a conditional order
                effectiveOrderType == OrderType.Limit &&
                timeInForce == TimeInForce.PostOnly &&
                limitPrice.HasValue && 
                _latestFuturesData != null &&
                _latestFuturesData.LowestAsk.HasValue &&
                _latestFuturesData.HighestBid.HasValue)
            {
                bool isAggressive = false;
                string originalOrderTypeDesc = $"PostOnly Limit {side} @ {limitPrice.Value}";

                if (side == OrderSide.Buy && limitPrice.Value >= _latestFuturesData.LowestAsk.Value)
                {
                    isAggressive = true;
                }
                else if (side == OrderSide.Sell && limitPrice.Value <= _latestFuturesData.HighestBid.Value)
                {
                    isAggressive = true;
                }

                if (isAggressive)
                {
                    _log.Warning($"***********************************************************************************");
                    _log.Warning($"AUTO-CONVERSION (in BaseExchangeAPI.OpenFuturesPositionAsync): Aggressive {originalOrderTypeDesc} for {TradingPair.Symbol} detected.");
                    _log.Warning($"Market: Bid={_latestFuturesData.HighestBid.Value}, Ask={_latestFuturesData.LowestAsk.Value}. LimitPrice: {limitPrice.Value}");
                    _log.Warning($"Converting to Conditional Market order triggered at {limitPrice.Value}.");
                    _log.Warning($"***********************************************************************************");

                    triggerPrice = limitPrice.Value; // Original limitPrice becomes the trigger
                    effectiveOrderType = OrderType.Market;    // Order to place when triggered
                    timeInForce = TimeInForce.GoodTillCancel; // Explicitly set to GTC for conditional orders
                    triggerBy = Models.TriggerType.LastPrice; // Default trigger type
                    triggerDirection = (side == OrderSide.Buy) ? Models.TriggerDirection.Rise : Models.TriggerDirection.Fall;
                    // For a conditional market order, the original limitPrice (which is now triggerPrice) isn't used as the 'price' param.
                    // We set limitPrice to null to signify this.
                    limitPrice = null; 
                }
            }
            // ***********************************************************************************
            // END OF CONVENIENCE SECTION
            // ***********************************************************************************

            var result = await PlaceFuturesOrderAsync(Symbol, side, baseAmount, limitPrice, leverage, effectiveOrderType, positionDirection, timeInForce, reduceOnly, clientId, triggerPrice, triggerBy, triggerDirection); // Pass effectiveTimeInForce

            if (result.IsSuccess && !IsOrderInFinalState(result.Status))
            {
                _log.Debug($"Order {result.OrderId} not in final state ({result.Status}), waiting for completion...");
                await WaitForOrderStatusAsync(result.OrderId, null, Category.Linear, TimeSpan.FromMilliseconds(_config.Timeouts.OrderStatusWait));

                try
                {
                    var updatedModel = await GetOrderStatusAsync(result.OrderId, Category.Linear, clientId);
                    if (updatedModel != null)
                    {
                        _log.Debug($"Retrieved updated status for order {result.OrderId} {(clientId != null ? $"ClientId: {clientId}" : "")}: {updatedModel.Status}");
                        return new FuturesOrderResult
                        {
                            IsSuccess = result.IsSuccess,
                            OrderId = result.OrderId,
                            ClientOrderId = updatedModel.ClientOrderId,
                            Status = updatedModel.Status,
                            ExecutedQuantity = updatedModel.QuantityFilled,
                            ExecutedPrice = updatedModel.AveragePrice,
                            Message = result.Message,
                            Timestamp = updatedModel.UpdateTime,
                            Leverage = result.Leverage
                        };
                    }
                    else
                    {
                        _log.Warning($"Could not retrieve updated status for order {result.OrderId} after waiting. Returning original result.");
                    }
                }
                catch (Exception ex)
                {
                    _log.Error(ex, $"Failed to get updated status for order {result.OrderId} after waiting. Returning original result.");
                }
            }
            else
            {
                _log.Debug($"Order {result.OrderId} was already in final state ({result.Status}) or failed placement.");
            }

            return result;
        }

        public async Task<FuturesOrderResult> OpenFuturesPositionAsync(FuturesOrderRequest request)
        {
            var side = request.IsBuy ? OrderSide.Buy : OrderSide.Sell;
            // We will modify request.Price, request.OrderType, request.TimeInForce, 
            // request.TriggerPrice, request.TriggerBy, request.TriggerDirection directly if auto-conversion occurs.
            // positionDirection is handled by its getter in FuturesOrderRequest or can be passed by ref.
            var positionDirection = request.PositionDirection;

            // ***********************************************************************************
            // CONVENIENCE: Auto-convert aggressive PostOnly Limit to Conditional Market
            // ***********************************************************************************
            // IMPORTANT: This block directly modifies properties of the 'request' object.
            if (request.TriggerPrice == null && // Only if NOT explicitly a conditional order
                request.OrderType == OrderType.Limit &&
                request.TimeInForce == TimeInForce.PostOnly &&
                request.Price.HasValue && 
                _latestFuturesData != null &&
                _latestFuturesData.LowestAsk.HasValue &&
                _latestFuturesData.HighestBid.HasValue)
            {
                bool isAggressive = false;
                string originalOrderTypeDesc = $"PostOnly Limit {side} @ {request.Price.Value}";

                if (side == OrderSide.Buy && request.Price.Value >= _latestFuturesData.LowestAsk.Value)
                {
                    isAggressive = true;
                }
                else if (side == OrderSide.Sell && request.Price.Value <= _latestFuturesData.HighestBid.Value)
                {
                    isAggressive = true;
                }

                if (isAggressive)
                {
                    _log.Warning($"***********************************************************************************");
                    _log.Warning($"AUTO-CONVERSION (in BaseExchangeAPI.OpenFuturesPositionAsync from Request): Aggressive {originalOrderTypeDesc} for {TradingPair.Symbol} detected.");
                    _log.Warning($"Market: Bid={_latestFuturesData.HighestBid.Value}, Ask={_latestFuturesData.LowestAsk.Value}. LimitPrice: {request.Price.Value}");
                    _log.Warning($"Converting to Conditional Market order triggered at {request.Price.Value}.");
                    _log.Warning($"***********************************************************************************");
                    
                    // Directly modify the request object's properties
                    request.TriggerPrice = request.Price.Value; 
                    request.OrderType = OrderType.Market; 
                    request.TimeInForce = TimeInForce.GoodTillCancel; // Explicitly set to GTC for conditional orders
                    request.TriggerBy = Models.TriggerType.LastPrice;
                    request.TriggerDirection = (side == OrderSide.Buy) ? Models.TriggerDirection.Rise : Models.TriggerDirection.Fall;
                    request.Price = null; // For a conditional market order, this isn't used as the 'price' param
                }
            }
            // ***********************************************************************************
            // END OF CONVENIENCE SECTION
            // ***********************************************************************************

            // Pass positionDirection by ref to ValidatePositionDirectionAndMode
            if (!ValidatePositionDirectionAndMode(Symbol, _currentPositionMode, ref positionDirection, request.IsBuy, out string validationMessage))
            {
                return new FuturesOrderResult
                {
                    IsSuccess = false,
                    Status = OrderStatus.Rejected,
                    Message = validationMessage,
                    Timestamp = DateTime.UtcNow,
                    ClientOrderId = request.ClientId
                };
            }

            var result = await PlaceFuturesOrderAsync(
                Symbol,
                side,
                request.Amount,
                request.Price, // Use (potentially modified) request.Price
                request.Leverage,
                request.OrderType, // Use (potentially modified) request.OrderType
                positionDirection,
                request.TimeInForce, // Use (potentially modified) request.TimeInForce
                request.IsReduceOnly,
                request.ClientId,
                request.TriggerPrice,    // Use (potentially modified) request.TriggerPrice
                request.TriggerBy,       // Use (potentially modified) request.TriggerBy
                request.TriggerDirection // Use (potentially modified) request.TriggerDirection
            );

            if (result.IsSuccess && !IsOrderInFinalState(result.Status))
            {
                _log.Debug($"Order {result.OrderId} (Client: {request.ClientId}) not in final state ({result.Status}), waiting...");
                await WaitForOrderStatusAsync(result.OrderId, request.ClientId, Category.Linear, TimeSpan.FromMilliseconds(_config.Timeouts.OrderStatusWait));

                try
                {
                    var updatedModel = await GetOrderStatusAsync(result.OrderId, Category.Linear, request.ClientId);
                    if (updatedModel != null)
                    {
                        _log.Debug($"Retrieved updated status for order {result.OrderId} (ClientId: {request.ClientId}): {updatedModel.Status}");
                        return new FuturesOrderResult
                        {
                            IsSuccess = result.IsSuccess,
                            OrderId = result.OrderId,
                            ClientOrderId = updatedModel.ClientOrderId,
                            Status = updatedModel.Status,
                            ExecutedQuantity = updatedModel.QuantityFilled,
                            ExecutedPrice = updatedModel.AveragePrice,
                            Message = result.Message,
                            Timestamp = updatedModel.UpdateTime,
                            Leverage = result.Leverage
                        };
                    }
                    else
                    {
                        _log.Warning($"Could not retrieve updated status for order {result.OrderId} (Client: {request.ClientId}) after waiting.");
                    }
                }
                catch (Exception ex)
                {
                    _log.Error(ex, $"Failed to get updated status for order {result.OrderId} (Client: {request.ClientId}) after waiting.");
                }
            }
            else
            {
                _log.Debug($"Order {result.OrderId} (Client: {request.ClientId}) already final ({result.Status}) or failed placement.");
            }

            return result;
        }

        // Protected methods for derived classes to implement
        protected abstract Task<SpotOrderResult> PlaceSpotOrderAsync(
            string symbol,
            OrderSide side,
            decimal quantity,
            decimal? price = null,
            OrderType? orderType = null,
            TimeInForce timeInForce = TimeInForce.GoodTillCancel,
            string? clientId = null);

        protected abstract Task<FuturesOrderResult> PlaceFuturesOrderAsync(
            string symbol,
            OrderSide side,
            decimal quantity,
            decimal? price = null,
            decimal? leverage = null,
            OrderType? orderType = null,
            PositionDirection? positionDirection = null,
            TimeInForce timeInForce = TimeInForce.GoodTillCancel,
            bool reduceOnly = false,
            string? clientId = null,
            decimal? triggerPrice = null,
            TriggerType? triggerBy = null,
            TriggerDirection? triggerDirection = null);

        public abstract Task<OrderModel?> GetOrderStatusAsync(string orderId, Category category, string? clientId = null);

        public abstract Task<OperationResult> CancelOrderAsync(string symbol, string? orderId = null, string? clientId = null);

        public abstract Task<OperationResult> CancelAllOrdersAsync(string? symbol = null, Category? category = null);

        // Protected event invokers
        protected virtual void InvokeSpotUpdate(SpotMarketData data)
        {
            OnSpotMarketUpdate?.Invoke(data);
        }

        protected virtual void InvokeFuturesUpdate(FuturesMarketData data)
        {
            _latestFuturesData = data; // Store the latest futures data
            OnFuturesMarketUpdate?.Invoke(data);
        }

        protected virtual void InvokeWalletUpdate(WalletUpdate update)
        {
            // Update the wallet
            Wallet.UpdateFromExchange(update);

            // Forward the event
            OnWalletUpdate?.Invoke(update);
        }

        /// <summary>
        /// Gets the current leverage settings for the trading pair.
        /// Some exchanges provide direct query capability, others maintain local state.
        /// </summary>
        /// <returns>Current buy leverage value</returns>
        public virtual async Task<OperationResult> SetLeverageAsync(decimal? leverage, PositionDirection? positionDirection = null)
        {
            if (!leverage.HasValue)
            {
                return new OperationResult() { IsSuccess = false, Message = $"Null leverage provided. Current leverage remains at Buy={_currentBuyLeverage}x, Sell={_currentSellLeverage}x" };
            }

            if (leverage.Value < 1 || leverage.Value > _config.MaxLeverage)
            {
                return new OperationResult() { IsSuccess = false, Message = $"Invalid leverage: {leverage}. Must be between 1 and {_config.MaxLeverage}" };
            }

            // If position side is specified, only update that side's leverage
            if (positionDirection == PositionDirection.Buy || positionDirection == null)
                _currentBuyLeverage = leverage.Value;

            if (positionDirection == PositionDirection.Sell || positionDirection == null)
                _currentSellLeverage = leverage.Value;

            return new OperationResult()
            {
                IsSuccess = true,
                Message = $"Leverage set to Buy={_currentBuyLeverage}x, Sell={_currentSellLeverage}x",
            };
        }

        /// <summary>
        /// Validates that the position side is compatible with the position mode.
        /// </summary>
        /// <param name="symbol">The trading symbol</param>
        /// <param name="positionMode">The position mode</param>
        /// <param name="positionDirection">The position direction</param>
        /// <param name="validationMessage">If validation fails, contains a message explaining why</param>
        /// <returns>True if valid, false otherwise</returns>
        protected virtual bool ValidatePositionDirectionAndMode(
            string symbol,
            PositionMode positionMode,
            ref PositionDirection? positionDirection,
            bool isLong,
            out string validationMessage)
        {
            validationMessage = string.Empty;
            if (positionMode == PositionMode.MergedSingle && positionDirection.HasValue)
            {
                validationMessage = $"Position direction '{positionDirection}' specified but account is in One-Way mode";
                _log.Warning(validationMessage);
                positionDirection = null;
            }
            else if (positionMode == PositionMode.BothSides && !positionDirection.HasValue)
            {
                positionDirection = isLong ? PositionDirection.Buy : PositionDirection.Sell;
                validationMessage = $"Position direction not specified for Hedge mode, derived as '{positionDirection}'.";
                _log.Debug(validationMessage);
            }
            return true;
        }

        /// <summary>
        /// Gets the current position mode for the trading pair.
        /// </summary>
        /// <returns>The current position mode</returns>
        public virtual Task<PositionMode> GetPositionModeAsync()
        {
            return Task.FromResult(_currentPositionMode);
        }

        /// <summary>
        /// Gets the current margin mode for the account.
        /// </summary>
        /// <returns>The current margin mode</returns>
        public virtual Task<MarginMode> GetMarginModeAsync()
        {
            return Task.FromResult(_currentMarginMode);
        }

        /// <summary>
        /// Gets the current trade mode for the trading pair.
        /// </summary>
        /// <returns>The current trade mode</returns>
        public virtual Task<TradeMode> GetTradeModeAsync()
        {
            return Task.FromResult(_currentTradeMode);
        }

        /// <summary>
        /// Gets the current leverage values for the trading pair.
        /// </summary>
        /// <returns>A tuple containing the buy and sell leverage values</returns>
        public virtual Task<(decimal buyLeverage, decimal sellLeverage)> GetLeverageAsync()
        {
            return Task.FromResult((_currentBuyLeverage, _currentSellLeverage));
        }

        /// <summary>
        /// Determines if an order status represents a final state (e.g., Filled, Cancelled, Rejected).
        /// Uses the inverse approach - explicitly defines non-final states.
        /// Made static for general utility.
        /// </summary>
        /// <param name="status">The order status to check.</param>
        /// <returns>True if the status is considered final, false otherwise.</returns>
        public static bool IsOrderInFinalState(OrderStatus status)
        {
            // Define which states are NOT final
            // Everything else is considered final
            return /*status != OrderStatus.Created &&*/ // If I know correctly Created is a temporary state, awaiting for Untriggered, New, PartiallyFilled
                   status != OrderStatus.Untriggered &&
                   status != OrderStatus.New &&
                   status != OrderStatus.PartiallyFilled;
                   // Note: Add any other exchange-specific non-final states here if needed globally.
        }

        public static bool IsOrderActive(OrderStatus status) 
        {
            return /*status == OrderStatus.Created ||*/  // If I know correctly Created is a temporary state, awaiting for Untriggered, New, PartiallyFilled
                   status == OrderStatus.Untriggered ||
                   status == OrderStatus.New ||
                   status == OrderStatus.PartiallyFilled;
        }

        /// <summary>
        /// Waits for an order to reach a specific status or any final state.
        /// </summary>
        /// <param name="orderId">The order ID to wait for</param>
        /// <param name="targetStatus">The target status to wait for, or null to wait for any final state</param>
        /// <param name="timeout">Maximum time to wait</param>
        /// <returns>True if the order reached the target status or a final state, false if timeout occurred</returns>
        protected virtual async Task<bool> WaitForOrderStatusAsync(string orderId, string? clientId, Category category, TimeSpan timeout)
        {
            var startTime = DateTime.UtcNow;
            var endTime = startTime + timeout;

            var orderUpdateReceived = new TaskCompletionSource<bool>();

            void OrderUpdateHandler(OrderModelUpdate update)
            {
                if (update.OrderId == orderId || (!string.IsNullOrEmpty(clientId) && update.ClientOrderId == clientId))
                {
                    if (IsOrderInFinalState(update.Status))
                    {
                       orderUpdateReceived.TrySetResult(true);
                    }
                }
            }

            try
            {
                OnOrderUpdate += OrderUpdateHandler;

                var currentStatus = await GetOrderStatusAsync(orderId, category, clientId);
                if (currentStatus != null && IsOrderInFinalState(currentStatus.Status))
                {
                    _log.Debug($"Order {orderId} (Client: {clientId}) already in final state {currentStatus.Status} before waiting.");
                    return true;
                }

                if (await Task.WhenAny(orderUpdateReceived.Task, Task.Delay(timeout)) == orderUpdateReceived.Task)
                {
                    return true;
                }
                else
                {
                    _log.Warning($"WaitForOrderStatusAsync: Timed out waiting for a *final state event* for order {orderId} (Client: {clientId}). The order may still be active (e.g., 'New'). Subsequent poll will determine current status.");
                    return false;
                }
            }
            finally
            {
                OnOrderUpdate -= OrderUpdateHandler;
            }
        }

        /// <summary>
        /// Tracks a new order in the active orders collection
        /// </summary>
        protected virtual void TrackOrder(OrderModel order)
        {
            if (order == null)
                return;

            // Check if the order is in a final state
            bool isFinalState = IsOrderInFinalState(order.Status);

            if (!isFinalState)
            {
                // Only track orders that are not in a final state
                _activeOrders[order.OrderId] = order;
            }
            else
            {
                // If the order is in a final state, make sure it's not in the active orders collection
                if (_activeOrders.ContainsKey(order.OrderId))
                {
                    _activeOrders.TryRemove(order.OrderId, out _);
                }
            }
        }

        /// <summary>
        /// Updates order tracking based on an order update
        /// </summary>
        protected virtual void UpdateOrderTracking(OrderModelUpdate update)
        {
            if (update == null || string.IsNullOrEmpty(update.OrderId))
                return;

            // Check if we're tracking this order
            bool orderExists = _activeOrders.TryGetValue(update.OrderId, out var existingOrder);

            // Check if the order is in a final state
            bool isFinalState = IsOrderInFinalState(update.Status);

            if (isFinalState)
            {
                _log.Debug($"Order {update.OrderId} (Client: {update.ClientOrderId}) is in final state: {update.Status}. OrderExists in _activeOrders: {orderExists}.");
                // If the order is in a final state (Filled, Cancelled, Rejected, etc.)
                if (orderExists)
                {
                    _log.Debug($"Attempting to remove order {update.OrderId} from _activeOrders. Count before: {_activeOrders.Count}");
                    bool removed = _activeOrders.TryRemove(update.OrderId, out _);
                    _log.Debug($"Order {update.OrderId} removal from _activeOrders: {(removed ? "Succeeded" : "Failed")}. Count after: {_activeOrders.Count}");
                }

                // Update trading statistics if this is a position close (has ClosedPnl)
                if (update.ClosedPnl.HasValue && update.ClosedPnl.Value != 0)
                {
                    try
                    {
                        CurrencyPair pair = CommonPairs.ParseSymbolToCurrencyPair(update.Symbol);
                        _sessionRealizedPnL.AddOrUpdate(pair, update.ClosedPnl.Value, (key, existingPnl) => existingPnl + update.ClosedPnl.Value);
                    }
                    catch (ArgumentException ex)
                    {
                        _log.Warning($"Could not parse symbol '{update.Symbol}' to CurrencyPair for PnL tracking: {ex.Message}");
                    }
                }

                // Update fee tracking
                if (update.ExecutedFee != null && (update.ExecutedFee.Value.Base.Amount > 0 || update.ExecutedFee.Value.Quote.Amount > 0))
                {
                    try
                    {
                        CurrencyPair pair = CommonPairs.ParseSymbolToCurrencyPair(update.Symbol);
                        Fee newFee = update.ExecutedFee.Value;

                        _sessionFees.AddOrUpdate(pair, newFee, (key, existingFee) => existingFee + newFee);
                    }
                    catch (ArgumentException ex)
                    {
                         _log.Warning($"Could not parse symbol '{update.Symbol}' to CurrencyPair for Fee tracking: {ex.Message}");
                    }
                }
            }
            else
            {
                // Update existing order or add new one for non-final states
                if (orderExists)
                {
                    // Use the helper method to update the existing order
                    UpdateOrderModel(existingOrder!, update);
                }
                else
                {
                    // Use the helper method to create a new order
                    var newOrder = CreateNewOrderModel(update);
                    if (newOrder != null)
                    {
                        _activeOrders[update.OrderId] = newOrder;
                    }
                }
            }
        }

        /// <summary>
        /// Updates position tracking based on a position update
        /// </summary>
        protected virtual void HandlePositionUpdate(PositionModelUpdate update)
        {
            if (update == null)
                return;

            string positionKey = GetPositionKey(update.Symbol, update.Direction);

            if (Math.Abs(update.Quantity) > 0)
            {
                // Create or update position
                var position = new PositionModel
                {
                    Symbol = update.Symbol,
                    Direction = update.Direction,
                    Side = update.Side,
                    Quantity = update.Quantity,
                    AveragePrice = update.AveragePrice,
                    MarkPrice = update.MarkPrice,
                    LiquidationPrice = update.LiquidationPrice,
                    Leverage = update.Leverage,
                    UnrealizedPnl = update.UnrealizedPnl,
                    RealizedPnl = update.RealizedPnl,
                    PositionValue = update.PositionValue,
                    InitialMargin = update.InitialMargin,
                    MaintenanceMargin = update.MaintenanceMargin,
                    Category = update.Category,
                    TradeMode = update.TradeMode,
                    PositionStatus = update.PositionStatus,
                    UpdateTime = update.UpdateTime,
                    CreateTime = update.CreateTime,
                    PositionMode = _currentPositionMode,
                    BustPrice = update.BustPrice
                };

                _openPositions[positionKey] = position;
            }
            else
            {
                // Remove closed position
                _openPositions.TryRemove(positionKey, out _);
            }

            // Invoke the position update event
            OnPositionUpdate?.Invoke(update);
        }

        /// <summary>
        /// Handles order updates and updates tracking
        /// </summary>
        protected virtual void HandleOrderUpdate(OrderModelUpdate update)
        {
            UpdateOrderTracking(update);
            OnOrderUpdate?.Invoke(update);
        }

        /// <summary>
        /// Asynchronously fetches the latest wallet balances from the exchange
        /// and updates the internal Wallet state.
        /// </summary>
        /// <returns>The updated WalletUpdate object.</returns>
        public virtual async Task<WalletUpdate> GetWalletBalancesAsync()
        {
            // Default implementation - specific exchanges should override
            // This method *should* fetch data and call Wallet.UpdateFromExchange
            _log.Warning($"GetWalletBalancesAsync not implemented in {GetType().Name}. Returning last known update.");
            await Task.CompletedTask; // Simulate async work
            return GetLastWalletUpdate() ?? new WalletUpdate(); // Return cached value if not overridden
        }

        /// <summary>
        /// Synchronously gets the last cached WalletUpdate stored in the internal Wallet object.
        /// Does not trigger a fetch from the exchange.
        /// </summary>
        /// <returns>The last cached WalletUpdate or null if the Wallet is not initialized.</returns>
        public virtual WalletUpdate? GetLastWalletUpdate()
        {
             return Wallet?.GetLastUpdate();
        }

        /// <summary>
        /// Refreshes the wallet balances by calling the async fetch method.
        /// This is typically called periodically.
        /// </summary>
        public virtual async Task RefreshWalletBalancesAsync()
        {
            try
            {
                 _log.Debug("Refreshing wallet balances...");
                 // Call the async method which should update the internal Wallet object
                 await GetWalletBalancesAsync();
                 _log.Debug("Wallet balance refresh complete.");
            }
            catch (Exception ex)
            {
                 _log.Error(ex, "Failed to refresh wallet balances.");
                 // Decide if re-throwing is appropriate depending on caller expectation
            }
        }

        /// <summary>
        /// Gets all active positions
        /// </summary>
        /// <returns>List of active positions</returns>
        /// This is meaningful to be overriden in BybitExchangeAPI
        public virtual Task<IEnumerable<PositionModel>> GetPositionsAsync(Models.Category category = Category.Linear, string? symbol = null)
        {
            // Get all positions from the existing synchronous method
            var allPositions = GetOpenPositions();

            // Filter based on category and symbol if provided
            var filteredPositions = allPositions
                .Where(p => (category == Models.Category.Undefined || p.Category == category) &&
                           (symbol == null || p.Symbol.Equals(symbol, StringComparison.OrdinalIgnoreCase)))
                .ToList();

            return Task.FromResult<IEnumerable<PositionModel>>(filteredPositions);
        }

        /// <summary>
        /// Gets all active orders
        /// </summary>
        /// <returns>List of active orders</returns>
        public virtual Task<IEnumerable<OrderModel>> GetActiveOrdersAsync()
        {
            // Use the existing synchronous method for consistency
            return Task.FromResult(GetActiveOrders());
        }

        /// <summary>
        /// Gets a summary of profit and loss
        /// </summary>
        /// <returns>PnL summary</returns>
        public virtual Task<PnLSummary> GetPnLSummaryAsync()
        {
            // Calculate PnL summary from tracked data using existing methods
            var summary = new PnLSummary
            {
                // For simplicity, we'll use session PnL for all time periods in the base implementation
                // Derived classes should override this with actual time-based calculations
                DailyPnL = GetSessionRealizedPnl(),
                WeeklyPnL = GetSessionRealizedPnl(),
                MonthlyPnL = GetSessionRealizedPnl(),
                TotalRealizedPnL = GetSessionRealizedPnl(),
                TotalUnrealizedPnL = GetUnrealizedPnl()
            };

            return Task.FromResult(summary);
        }

        /// <summary>
        /// Shuts down the API connection
        /// </summary>
        public virtual Task ShutdownAsync()
        {
            // Base implementation - override in derived classes
            // Clean up resources
            Dispose();
            return Task.CompletedTask;
        }

        // IDisposable implementation
        public virtual void Dispose()
        {
            // Default implementation
            GC.SuppressFinalize(this);
        }

        /// <summary>
        /// Gets the current account settings from the exchange, including position mode, margin mode, leverage, etc.
        /// Derived classes must implement the specific API call(s) to retrieve these settings.
        /// </summary>
        /// <returns>An AccountSettings object reflecting the current state on the exchange.</returns>
        public abstract Task<AccountSettings> GetAccountSettingsAsync();

        /// <summary>
        /// Sets the position mode for the account (One-way or Hedge mode)
        /// </summary>
        /// <param name="mode">The position mode to set</param>
        /// <returns>Result of the operation</returns>
        public virtual async Task<OperationResult> SetPositionModeAsync(PositionMode mode)
        {
            // Default implementation - derived classes should override this
            _currentPositionMode = mode;
            _log.Information($"SetPositionModeAsync(): PositionMode cached state set to {mode} in BaseExchangeAPI. Specific exchange implementation should override to apply changes.");
            // Base implementation just updates the internal state. It doesn't interact with an exchange,
            // so it doesn't "fail" in the same way an API call would.
            // We return IsSuccess = true assuming the internal state update is always successful.
            // Derived classes making the actual API call will handle success/failure based on the exchange response.
            return new OperationResult
            {
                IsSuccess = true,
                Message = $"BaseExchangeAPI state updated to {mode}. Override needed for actual exchange change."
            };
        }

        /// <summary>
        /// Closes all open positions
        /// </summary>
        /// <returns>Result of the operation</returns>
        public virtual async Task<OperationResult> CloseAllPositionsAsync()
        {
            var positions = GetOpenPositions();
            if (!positions.Any())
            {
                return new OperationResult { IsSuccess = true, Message = "No positions to close" };
            }

            var failedPositions = new List<string>();
            var successCount = 0;

            foreach (var position in positions)
            {
                try
                {
                    OperationResult result;
                    if (_currentPositionMode == PositionMode.BothSides)
                    {
                        // Close with direction (for hedge mode)
                        result = await ClosePositionAsync(position.Symbol, position.Direction);
                    }
                    else
                    {
                        // Close without direction (for one-way mode)
                        result = await ClosePositionAsync(position.Symbol);
                    }

                    if (result.IsSuccess)
                    {
                        successCount++;
                    }
                    else
                    {
                        failedPositions.Add($"Close {position.Symbol} {position.Direction} position: Failed - {result.Message}");
                    }
                }
                catch (Exception ex)
                {
                    failedPositions.Add($"Close {position.Symbol} {position.Direction} position: Exception - {ex.Message}");
                }
            }

            // Try again for failed positions (up to 2 retries)
            if (failedPositions.Count > 0)
            {
                for (int retry = 1; retry <= 2; retry++)
                {
                    var retryPositions = GetOpenPositions();
                    if (!retryPositions.Any())
                    {
                        break; // All positions closed
                    }

                    foreach (var position in retryPositions)
                    {
                        try
                        {
                            OperationResult result;
                            if (_currentPositionMode == PositionMode.BothSides)
                            {
                                // Close with direction (for hedge mode)
                                result = await ClosePositionAsync(position.Symbol, position.Direction);
                            }
                            else
                            {
                                // Close without direction (for one-way mode)
                                result = await ClosePositionAsync(position.Symbol);
                            }

                            if (result.IsSuccess)
                            {
                                successCount++;
                                failedPositions.RemoveAll(f => f.Contains($"{position.Symbol} {position.Direction}"));
                            }
                            else
                            {
                                failedPositions.Add($"Retry {retry}: Close {position.Symbol} {position.Direction} position: Failed - {result.Message}");
                            }
                        }
                        catch (Exception ex)
                        {
                            failedPositions.Add($"Retry {retry}: Close {position.Symbol} {position.Direction} position: Exception - {ex.Message}");
                        }
                    }
                }
            }

            var remainingPositions = GetOpenPositions().Count();
            if (remainingPositions == 0)
            {
                return new OperationResult { IsSuccess = true, Message = "All positions closed successfully" };
            }
            else
            {
                return new OperationResult
                {
                    IsSuccess = false,
                    Message = $"Failed to close all positions. {remainingPositions} positions remain open. Details: {string.Join(", ", failedPositions)}"
                };
            }
        }

        /// <summary>
        /// Closes a position for the specified symbol
        /// </summary>
        /// <param name="symbol">The symbol to close</param>
        /// <returns>Result of the operation</returns>
        public virtual async Task<OperationResult> ClosePositionAsync(string symbol)
        {
            // Default implementation - derived classes should override this
            return new OperationResult
            {
                IsSuccess = false,
                Message = "Closing position is not implemented for this exchange"
            };
        }

        /// <summary>
        /// Closes a position for the specified symbol and direction
        /// </summary>
        /// <param name="symbol">The symbol to close</param>
        /// <param name="direction">The position direction to close</param>
        /// <returns>Result of the operation</returns>
        public virtual async Task<OperationResult> ClosePositionAsync(string symbol, PositionDirection? direction)
        {
            // Default implementation - derived classes should override this
            return new OperationResult
            {
                IsSuccess = false,
                Message = "Closing position with direction is not implemented for this exchange"
            };
        }

        /// <summary>
        /// Gets active orders for a specific category
        /// </summary>
        /// <param name="category">The category (Spot, Linear, etc.)</param>
        /// <param name="symbol">Optional symbol filter</param>
        /// <returns>Collection of active orders</returns>
        public virtual async Task<IEnumerable<OrderModel>> GetActiveOrdersForCategoryAsync(Models.Category category, string? symbol = null)
        {
            // Default implementation returns from the tracked orders collection
            return _activeOrders.Values
                .Where(o => o.Category == category && (symbol == null || o.Symbol == symbol))
                .ToList();
        }

        /// <summary>
        /// Updates an existing OrderModel with data from an OrderModelUpdate
        /// </summary>
        /// <param name="existingOrder">The existing order to update</param>
        /// <param name="update">The update data</param>
        /// <returns>The updated OrderModel</returns>
        protected virtual OrderModel UpdateOrderModel(OrderModel existingOrder, OrderModelUpdate update)
        {
            if (existingOrder == null || update == null)
                return existingOrder;

            // Use the improved ToOrderModel extension method that accepts an existing instance
            return update.ToOrderModel(existingOrder);
        }

        /// <summary>
        /// Creates a new OrderModel from an OrderModelUpdate
        /// </summary>
        /// <param name="update">The update data to create from</param>
        /// <returns>A new OrderModel</returns>
        protected virtual OrderModel CreateNewOrderModel(OrderModelUpdate update)
        {
            // Use the extension method from Helpers.cs
            return update.ToOrderModel();
        }

        #region Test methods and functions
        public virtual void AddOrUpdatePosition_TEST(PositionModel position)
        {
            if (position == null)
            {
                _log.Warning("AddOrUpdatePosition called with a null position.");
                return;
            }

            string positionKey = GetPositionKey(position.Symbol, position.Direction);

            if (position.Quantity != 0)
            {
                _openPositions[positionKey] = position;
                _log.Debug($"Test method added/updated position: {positionKey}, Qty: {position.Quantity}");
            }
            else
            {
                if (_openPositions.TryRemove(positionKey, out _))
                {
                    _log.Debug($"Test method removed position: {positionKey}");
                }
            }
        }

        public virtual void SimulateOrderUpdate_TEST(OrderModelUpdate orderupdate)
        {
            if (orderupdate == null)
            {
                _log.Warning("SimulateOrderUpdate_TEST called with a null update.");
                return;
            }
            _log.Debug($"Simulating order update via test method for Cloid: {orderupdate.ClientOrderId}, OID: {orderupdate.OrderId}, Status: {orderupdate.Status}");
            HandleOrderUpdate(orderupdate);
        }
        #endregion
    }
}