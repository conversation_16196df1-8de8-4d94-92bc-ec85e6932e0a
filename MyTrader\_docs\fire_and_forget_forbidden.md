# Prohibition of Fire-and-Forget Tasks

In this project, the use of "fire-and-forget" tasks (typically invoked using `_ = SomeAsyncTask();` or `Task.Run(() => SomeMethod());` without awaiting the result) is strictly forbidden.

## Reasoning

1.  **Uncatchable Exceptions:** Exceptions thrown within a fire-and-forget task are not automatically propagated back to the calling context. If an unhandled exception occurs within the task, it can crash the application (depending on the .NET version and configuration) or simply be lost, leading to silent failures that are extremely difficult to diagnose. There is no `try-catch` block that can reliably surround the *invocation* of a fire-and-forget task to catch exceptions *within* that task.
2.  **Debugging Complexity ("Ghost Tasks"):** These tasks run independently without a clear link back to the code that started them in the debugger's call stack once the initial invocation point has passed. Stepping through code that involves fire-and-forget tasks becomes significantly harder, as their execution flow is detached. They become "ghosts" in the system.
3.  **Lack of Awaitability/Sequencing:** There is no way to reliably wait for a fire-and-forget task to complete or to know when it has finished its work. This makes it impossible to sequence operations correctly. Code that depends on the *result* or the *completion* of the fire-and-forget task cannot be written reliably, leading to race conditions and unpredictable application state.
4.  **Resource Management:** If the task utilizes resources (like database connections, file handles, etc.), there's no guarantee they will be disposed of properly if the task fails silently or the application shuts down abruptly.

## Requirement

All asynchronous operations must be properly awaited using the `await` keyword. Tasks should be chained or composed using methods like `Task.WhenAll` or `Task.WhenAny` where appropriate to manage concurrency, ensuring that their completion and potential exceptions can be handled correctly by the calling code. 