using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Terminal.Gui;
using System.IO;
using System.Threading;
using MyTraderSpace.Exchanges;
using MyTraderSpace.Models;
using MyTraderSpace.Logging;

namespace MyTraderSpace.UI
{
    public enum SimulationMode
    {
        Normal,
        FailInConstructor,
        FailOnInitialize,
        FailOnRun,
        FailOnShutdown
    }

    /// <summary>
    /// A robust Terminal.Gui UI implementation that follows proper error handling 
    /// patterns and lifecycle management.
    /// </summary>
    public class TerminalConsoleInterface : IDisposable
    {
        #region Fields & Properties

        // --- UI Elements ---
        private ConsoleTextView _consoleView; // Custom TextView for console output
        private Window _consoleWindow;
        private Window _realTimeWindow;
        private View _commandContainer; // Container for command input
        private Label _commandLabel;
        private TextField _commandInput;
        private Label _modeLabel;      // Real-time view label
        private Label _statusLabel;    // Real-time view label
        private Label _equityLabel;   // For Total Equity & Unrealized PnL
        private Label _walletBalanceLabel; // For Total Wallet Balance
        private Label _availableBalanceLabel; // For Total Available Balance
        private Label _usdtBalanceLabel; // For USDT specific balances
        private Label _spotPriceLabel; // New label for Spot Price
        private Label _spotBidAskLabel; // New label for Spot Bid/Ask
        private Label _futuresPriceLabel; // New label for Futures Price
        private Label _futuresMarkLabel; // New label for Futures Mark Price
        private Label _futuresBidAskLabel; // New label for Futures Bid/Ask
        private Label _futuresFundingLabel; // New label for Futures Funding Rate
        private Label _exchangeRealizedPnlLabel; // Label for Exchange Reported Realized PnL
        private Label _strategyPnlLabel; // For Strategy Calculated PnL, Peak, Trough
        private Label _strategyFeesLabel; // For Strategy Accumulated Fees
        private Label _stepsLabel; // Add Label for Strategy Steps info
        private Label _errorMessagesLabel; // New label for error messages

        // --- Lifecycle Management ---
        private bool _isInitialized = false;
        private bool _isShuttingDown = false;
        private readonly CancellationTokenSource _internalCts = new CancellationTokenSource();
        private readonly CancellationToken _linkedToken;
        private readonly TaskCompletionSource _consoleReadyTcs = new TaskCompletionSource(TaskCreationOptions.RunContinuationsAsynchronously);
        private readonly TaskCompletionSource<bool> _uiShutdownTcs = new TaskCompletionSource<bool>(TaskCreationOptions.RunContinuationsAsynchronously);
        private Task _backgroundTask;
        private readonly TextWriter _originalConsoleOut;
        private TextWriter _consoleTextViewWriter;
        private readonly SimulationMode _simulationMode;
        private TimeSpan _initTimeout = TimeSpan.FromSeconds(5); // Timeout for UI init
        private readonly IUIDataProvider _dataProvider; // Added dependency
        private Task? _uiUpdateLoopTask; // Task for the internal update loop
        private readonly LogManager _log; // Internal logger

        // --- UI Styling ---
        private readonly ColorScheme _consoleColors = new ColorScheme()
        {
            Normal = Terminal.Gui.Attribute.Make(Color.White, Color.Black),
            Focus = Terminal.Gui.Attribute.Make(Color.White, Color.Black),
            HotNormal = Terminal.Gui.Attribute.Make(Color.BrightYellow, Color.Black),
            HotFocus = Terminal.Gui.Attribute.Make(Color.BrightYellow, Color.Black),
        };

        // Define input field color scheme (black bg normal, distinct focus bg)
        private readonly ColorScheme _inputColorScheme;

        private static readonly ColorScheme _realTimeColors = new ColorScheme
        { // Distinct colors for this view
            Normal = Terminal.Gui.Attribute.Make(Color.Black, Color.Gray),
            Focus = Terminal.Gui.Attribute.Make(Color.White, Color.DarkGray),
            HotNormal = Terminal.Gui.Attribute.Make(Color.BrightYellow, Color.Gray),
            HotFocus = Terminal.Gui.Attribute.Make(Color.BrightYellow, Color.DarkGray),
        };

        // --- Event Handlers ---
        private Action<MouseEvent> _mouseHandler; // Delegate for mouse event handler to allow unsubscribing

        /// <summary>
        /// Gets the TextWriter associated with the internal ConsoleTextView.
        /// This should only be accessed after WaitForConsoleReadyAsync completes successfully.
        /// </summary>
        public TextWriter Writer => _consoleTextViewWriter;

        /// <summary>
        /// Task that completes when the UI loop finishes execution (or fails).
        /// </summary>
        public Task UITask => _uiShutdownTcs.Task;

        /// <summary>
        /// Signals that the user wants to shut down the application (e.g., Ctrl+Q).
        /// </summary>
        public event EventHandler ShutdownRequested;

        /// <summary>
        /// Signals that the user has entered a command.
        /// </summary>
        public event EventHandler<string> CommandEntered;

        /// <summary>
        /// Event to notify that the UI has encountered an error.
        /// </summary>
        public event EventHandler<Exception> UIFailed;

        #endregion

        #region Constructor & Lifecycle Management

        /// <summary>
        /// Creates a new TerminalConsoleInterface instance.
        /// </summary>
        /// <param name="externalToken">A token to link with the UI's lifetime. Cancellation will signal the UI to stop.</param>
        /// <param name="originalConsoleOut">The original Console.Out stream for restoration.</param>
        /// <param name="dataProvider">The provider for fetching UI data snapshots.</param>
        /// <param name="simulationMode">Simulation mode for testing failure scenarios.</param>
        public TerminalConsoleInterface(CancellationToken externalToken, TextWriter originalConsoleOut, IUIDataProvider dataProvider, SimulationMode simulationMode = SimulationMode.Normal)
        {
            _originalConsoleOut = originalConsoleOut ?? throw new ArgumentNullException(nameof(originalConsoleOut));
            _dataProvider = dataProvider ?? throw new ArgumentNullException(nameof(dataProvider));
            _linkedToken = CancellationTokenSource.CreateLinkedTokenSource(externalToken, _internalCts.Token).Token;
            _simulationMode = simulationMode;
            _log = new LogManager("TerminalUI");

            // Check for failure in constructor
            if (_simulationMode == SimulationMode.FailInConstructor)
            {
                _log.Error("TerminalConsoleInterface: Simulated failure in constructor");
                throw new ApplicationException("TerminalConsoleInterface: Simulated failure in constructor");
            }

            // Initialize color schemes that depend on other instance fields
            _inputColorScheme = new ColorScheme()
            {
                Normal = Terminal.Gui.Attribute.Make(Color.White, Color.Black),
                Focus = Terminal.Gui.Attribute.Make(Color.White, Color.DarkGray),
                HotNormal = Terminal.Gui.Attribute.Make(Color.BrightYellow, Color.Black),
                HotFocus = Terminal.Gui.Attribute.Make(Color.BrightYellow, Color.DarkGray),
                Disabled = _consoleColors.Disabled.HasValidColors
                           ? _consoleColors.Disabled
                           : Terminal.Gui.Attribute.Make(Color.DarkGray, Color.Black)
            };
        }

        /// <summary>
        /// Initializes the UI components. Must be called before StartUIThread.
        /// </summary>
        public async Task InitializeAsync()
        {
            if (_isInitialized)
                throw new InvalidOperationException("TerminalConsoleInterface is already initialized");

            try
            {
                // During initial setup, we must use original console output
                // This is one of the few places where we use _originalConsoleOut directly
                Console.WriteLine("Initializing UI...");

                // Check for failure during initialization
                if (_simulationMode == SimulationMode.FailOnInitialize)
                {
                    Console.WriteLine("TerminalConsoleInterface: Simulated failure during initialization");
                    throw new ApplicationException("TerminalConsoleInterface: Simulated failure during initialization");
                }

                // Initialization is succeeded in StartUIThread after Application.Init()
                _isInitialized = true;
                Console.WriteLine("TerminalConsoleInterface: Initialization completed.");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"TerminalConsoleInterface.InitializeAsync: Exception during initialization: {ex.Message}");
                _consoleReadyTcs.TrySetException(ex);
                throw; // Propagate the exception
            }
        }

        /// <summary>
        /// Starts the UI initialization and execution loop on a background thread.
        /// Does not block. Use WaitForConsoleReadyAsync to wait for redirection capability.
        /// </summary>
        public void StartUIThread()
        {
            if (!_isInitialized)
                throw new InvalidOperationException("TerminalConsoleInterface must be initialized before starting UI thread");

            // Run the UI initialization and main loop on a dedicated thread
            _backgroundTask = Task.Factory.StartNew(RunInternalUI, _linkedToken, TaskCreationOptions.LongRunning, TaskScheduler.Default);
        }

        /// <summary>
        /// Waits until the UI has initialized sufficiently to allow Console output redirection.
        /// </summary>
        /// <param name="timeout">Maximum time to wait.</param>
        /// <exception cref="TimeoutException">Thrown if the UI does not become ready within the timeout.</exception>
        /// <exception cref="OperationCanceledException">Thrown if the linked cancellation token is signaled.</exception>
        /// <exception cref="ApplicationException">Thrown if UI initialization failed before readiness signal.</exception>
        public async Task WaitForConsoleReadyAsync(TimeSpan? timeout = null)
        {
            var actualTimeout = timeout ?? _initTimeout;
            
            try
            {
                // Wait for either the console ready signal or the UI shutdown signal (if init fails)
                var completedTask = await Task.WhenAny(_consoleReadyTcs.Task, _uiShutdownTcs.Task)
                                            .WaitAsync(actualTimeout, _linkedToken);

                // If the shutdown task completed first, it means initialization failed.
                if (completedTask == _uiShutdownTcs.Task)
                {
                    // Retrieve the potential exception from the failed UI task
                    try 
                    { 
                        await _uiShutdownTcs.Task; 
                    } 
                    catch (Exception ex) 
                    { 
                        throw new ApplicationException("UI initialization failed before console readiness.", ex); 
                    }
                    throw new ApplicationException("UI initialization failed before console readiness.");
                }
                
                // If consoleReadyTcs completed, we are good.
                await _consoleReadyTcs.Task; // Propagates potential exception if TCS was faulted
            }
            catch (Exception ex)
            {
                // If there's a timeout or cancellation, log to the original console
                Console.WriteLine($"Error waiting for console ready: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// Internal method that runs on the UI thread.
        /// </summary>
        private void RunInternalUI()
        {
            bool initSucceeded = false;
            
            try
            {
                _log.Information("UI Thread: Initializing Terminal.Gui...");
                Application.Init();
                initSucceeded = true;
                _log.Information("UI Thread: Application.Init() successful.");

                _consoleView = new ConsoleTextView();
                _consoleTextViewWriter = _consoleView.Writer;

                SetupConsoleUI();
                SetupRealTimeUI();
                SetupCommandBar();

                Application.Top.Add(_consoleWindow, _realTimeWindow, _commandContainer);
                _realTimeWindow.Visible = false;
                _consoleWindow.Visible = true;

                SetupKeyHandlers();
                SetupMouseHandler();
                
                // Start the async update loop directly
                _uiUpdateLoopTask = StartInternalUIUpdateLoopAsync(_linkedToken);

                _log.Information("UI Thread: Signaling console readiness.");
                _consoleReadyTcs.TrySetResult();

                if (_simulationMode == SimulationMode.FailOnRun)
                {
                    Task.Run(async () =>
                    {
                        await Task.Delay(10000); // Delay before simulated failure
                        Console.WriteLine("UI Thread: Simulated failure during run");
                        throw new ApplicationException("TerminalConsoleInterface: Simulated failure during run");
                    });
                }

                _commandInput.SetFocus();
                _log.Information("UI Thread: Running Application loop...");
                Application.Run(Application.Top);

                _log.Information("UI Thread: Application loop finished normally.");
                _uiShutdownTcs.TrySetResult(true);
            }
            catch (Exception ex)
            {
                _originalConsoleOut.WriteLine($"UI Thread: CRITICAL FAILURE during execution: {ex}");
                _log?.Error(ex, "UI Thread: CRITICAL FAILURE during execution");
                UIFailed?.Invoke(this, ex);
                _consoleReadyTcs.TrySetException(ex);
                _uiShutdownTcs.TrySetException(ex);
            }
            finally
            {
                _log?.Information("UI Thread: Entering UI cleanup...");

                if (_mouseHandler != null && Application.Driver != null)
                {
                    Application.RootMouseEvent -= _mouseHandler;
                    _mouseHandler = null;
                }

                if (Console.Out == _consoleTextViewWriter && _originalConsoleOut != null)
                {
                    Console.SetOut(_originalConsoleOut);
                    Console.WriteLine("UI Thread: Original console output restored");
                }

                if (initSucceeded)
                {
                    Console.WriteLine("UI Thread: Shutting down Terminal.Gui Application...");
                    Application.Shutdown();
                    Console.WriteLine("UI Thread: Application.Shutdown() completed.");
                }

                if (!_internalCts.IsCancellationRequested)
                {
                    _internalCts.Cancel();
                }
                _internalCts.Dispose();

                Console.WriteLine("UI Thread: Cleanup complete.");
                
                _uiShutdownTcs.TrySetResult(true);
            }
        }

        /// <summary>
        /// Signals the UI thread to stop its message loop.
        /// </summary>
        public void RequestStop()
        {
            Console.WriteLine("TerminalConsoleInterface.RequestStop called");
            
            // Request UI loop stop (on UI thread if possible)
            if (Application.MainLoop != null && (Application.Top?.Running ?? false))
            {
                Application.MainLoop.Invoke(() =>
                {
                    if (Application.Top?.Running ?? false) // Double check inside invoke
                    {
                        Console.WriteLine("UI.RequestStop: Requesting Application.RequestStop().");
                        Application.RequestStop();
                    }
                });
            }
            
            // Also cancel the internal CTS to stop timers etc.
            _internalCts.Cancel();
        }

        /// <summary>
        /// Performs a complete shutdown with resource cleanup.
        /// This is where exceptions during shutdown should be handled.
        /// </summary>
        public async Task ShutdownAsync()
        {
            // Prevent multiple shutdowns
            if (_isShuttingDown)
                return;
                
            _isShuttingDown = true;
            
            try
            {
                Console.WriteLine("TerminalConsoleInterface: Beginning shutdown process");
                
                // Simulate failures during shutdown if configured
                if (_simulationMode == SimulationMode.FailOnShutdown)
                {
                    Console.WriteLine("TerminalConsoleInterface: Simulated failure during shutdown");
                    throw new ApplicationException("TerminalConsoleInterface: Simulated failure during shutdown");
                }
                
                // Ensure the UI is requested to stop
                if (!_internalCts.IsCancellationRequested)
                {
                    RequestStop();
                }
                
                // Wait for the background task to complete if it exists
                if (_backgroundTask != null && !_backgroundTask.IsCompleted)
                {
                    // Add a timeout to prevent hanging
                    var timeoutTask = Task.Delay(TimeSpan.FromSeconds(5));
                    var completedTask = await Task.WhenAny(_backgroundTask, timeoutTask);
                    
                    if (completedTask == timeoutTask)
                    {
                        Console.WriteLine("TerminalConsoleInterface: Timed out waiting for background task to complete");
                    }
                    else
                    {
                        // Handle any exceptions from the background task
                        try
                        {
                            await _backgroundTask;
                            Console.WriteLine("TerminalConsoleInterface: Background task completed successfully");
                        }
                        catch (Exception ex)
                        {
                            Console.WriteLine($"TerminalConsoleInterface: Background task failed: {ex.Message}");
                            // We don't rethrow here since we're in cleanup mode
                        }
                    }
                }
                
                Console.WriteLine("TerminalConsoleInterface: Shutdown completed successfully");
            }
            catch (Exception ex)
            {
                // Critical error during shutdown - use original console as fallback
                _originalConsoleOut.WriteLine($"Exception during UI shutdown: {ex.Message}");
                throw; // Propagate the exception
            }
            finally
            {
                _isInitialized = false;
                _isShuttingDown = false;
            }
        }

        #endregion

        #region UI Setup Methods

        /// <summary>
        /// Sets up the console window and its contents.
        /// </summary>
        private void SetupConsoleUI()
        {
            _consoleWindow = new Window("Console View")
            {
                X = 0,
                Y = 0,
                Width = Dim.Fill(),
                Height = Dim.Fill(1), // Fill except command bar
                ColorScheme = _consoleColors
            };

            var topStatusBar = new Label($"Console View - F2 Toggle - Ctrl+Q Quit")
            {
                X = 0,
                Y = 0,
                Width = Dim.Fill(),
                Height = 1,
                ColorScheme = _consoleColors
            };

            // Configure the existing _consoleView instance
            _consoleView.X = 0;
            _consoleView.Y = 1; // Below top status bar
            _consoleView.Width = Dim.Fill();
            _consoleView.Height = Dim.Fill(); // Fill remaining window space
            _consoleView.ReadOnly = true;
            _consoleView.ColorScheme = _consoleColors;

            _consoleWindow.Add(topStatusBar, _consoleView);
        }

        /// <summary>
        /// Sets up the real-time status window and its contents.
        /// </summary>
        private void SetupRealTimeUI()
        {
            _realTimeWindow = new Window("Real-Time View")
            {
                X = 0,
                Y = 0,
                Width = Dim.Fill(),
                Height = Dim.Fill(1), // Fill except command bar
                ColorScheme = _realTimeColors
            };

            var infoLabel = new Label($"Real-Time View - F2 Toggle - Ctrl+Q Quit")
            {
                X = 0,
                Y = 0,
                Width = Dim.Fill(),
                Height = 1,
                ColorScheme = _realTimeColors
            };

            // --- Main Status ---
            _modeLabel = new Label("API: Initializing...") { X = 1, Y = 2, ColorScheme = _realTimeColors };
            _statusLabel = new Label("Strategy: Initializing...") { X = 1, Y = 3, ColorScheme = _realTimeColors };
            _equityLabel = new Label("Equity: --- | uPnL: ---") { X = 1, Y = 4, Width = Dim.Fill()-1, ColorScheme = _realTimeColors };
            _exchangeRealizedPnlLabel = new Label("PnL (Exch R): ---") { X = 1, Y = 5, ColorScheme = _realTimeColors };
            _walletBalanceLabel = new Label("Wallet Bal (Total): ---") { X = Pos.Percent(50), Y = 5, ColorScheme = _realTimeColors};
            _availableBalanceLabel = new Label("Wallet Bal (Avail): ---") {X = 1, Y = 6, ColorScheme = _realTimeColors};
            _usdtBalanceLabel = new Label("USDT (Total/Avail): --- / ---") { X = Pos.Percent(50), Y = 6, ColorScheme = _realTimeColors};

            // --- Market Data ---
            int marketDataRowStart = 8;
            _spotPriceLabel = new Label("Spot Last: ---") { X = 1, Y = marketDataRowStart, ColorScheme = _realTimeColors };
            _spotBidAskLabel = new Label("Spot Bid/Ask: --- / ---") { X = 1, Y = marketDataRowStart + 1, ColorScheme = _realTimeColors };
            _futuresPriceLabel = new Label("Futures Last: ---") { X = 1, Y = marketDataRowStart + 2, ColorScheme = _realTimeColors };
            _futuresMarkLabel = new Label("Futures Mark: ---") { X = 1, Y = marketDataRowStart + 3, ColorScheme = _realTimeColors };
            _futuresBidAskLabel = new Label("Futures Bid/Ask: --- / ---") { X = 1, Y = marketDataRowStart + 4, ColorScheme = _realTimeColors };
            _futuresFundingLabel = new Label("Funding Rate: ---") { X = 1, Y = marketDataRowStart + 5, ColorScheme = _realTimeColors };

            // --- Strategy Specific ---
            int strategyRowStart = marketDataRowStart + 7;
            _stepsLabel = new Label("Steps: --- | High: --- | Low: ---") { X = 1, Y = strategyRowStart, ColorScheme = _realTimeColors };
            // Strategy PnL and Fees
            _strategyPnlLabel = new Label("Strat PnL (C/P/T): --- / --- / ---") { X = 1, Y = strategyRowStart + 1, Width = Dim.Fill()-1, ColorScheme = _realTimeColors };
            _strategyFeesLabel = new Label("Strat Fees: ---") { X = 1, Y = strategyRowStart + 2, ColorScheme = _realTimeColors };
            
            // Error Messages Label - Placed below strategy specific info
            _errorMessagesLabel = new Label("No recent errors.") 
            {
                X = 1, 
                Y = strategyRowStart + 4, // Position after strategy fees
                Width = Dim.Fill() - 2, // Occupy most of the width
                Height = 5, // Allow for up to 5 lines of errors, will be clipped by window if less space
                ColorScheme = new ColorScheme { Normal = Terminal.Gui.Attribute.Make(Color.Red, _realTimeColors.Normal.Background) } // Red text for errors
            };
            // Add more labels for strategy data here if needed

            _realTimeWindow.Add(
                infoLabel,
                _modeLabel, _statusLabel, 
                _equityLabel, _exchangeRealizedPnlLabel, 
                _walletBalanceLabel, _availableBalanceLabel, _usdtBalanceLabel,
                _spotPriceLabel, _spotBidAskLabel,
                _futuresPriceLabel, _futuresMarkLabel, _futuresBidAskLabel, _futuresFundingLabel,
                _stepsLabel,
                _strategyPnlLabel, // Added strategy PnL label
                _strategyFeesLabel, // Added strategy fees label
                _errorMessagesLabel // Added error label to the window
            );
        }

        /// <summary>
        /// Sets up the command input bar at the bottom of the screen.
        /// </summary>
        private void SetupCommandBar()
        {
            _commandContainer = new View() // Use a simple View as the container
            {
                X = 0,
                Y = Pos.AnchorEnd(1),
                Width = Dim.Fill(),
                Height = 1,
                ColorScheme = _consoleColors
            };

            _commandLabel = new Label("Cmd:") { X = 0, Y = 0, ColorScheme = _consoleColors };
            _commandInput = new TextField("")
            {
                X = Pos.Right(_commandLabel) + 1,
                Y = 0,
                Width = Dim.Fill(),
                Height = 1,
                ColorScheme = _inputColorScheme
            };

            // Handle command input via KeyPress handler
            _commandInput.KeyPress += (args) =>
            {
                if (args.KeyEvent.Key == Key.Enter)
                {
                    args.Handled = true;
                    string commandText = _commandInput.Text?.ToString();
                    _commandInput.Text = "";
                    _commandInput.SetFocus();

                    // Always fire CommandEntered, even for empty input (so Enter is detected for shutdown)
                    CommandEntered?.Invoke(this, commandText ?? string.Empty);
                }
                else { args.Handled = false; }
            };

            _commandContainer.Add(_commandLabel, _commandInput);
        }

        #endregion

        #region Event Handlers & UI Control

        /// <summary>
        /// Sets up global key handlers (F2 for view toggle, Ctrl+Q for quit).
        /// </summary>
        private void SetupKeyHandlers()
        {
            Application.Top.KeyPress += (args) => {
                if (args.KeyEvent.Key == Key.F2) { ToggleUI(); args.Handled = true; }
            };
            Application.Top.KeyPress += (args) => {
                if (args.KeyEvent.Key == (Key.Q | Key.CtrlMask))
                {
                    Console.WriteLine("UI Thread: Ctrl+Q pressed, raising ShutdownRequested event.");
                    // Raise event for Program.cs to handle cancellation
                    ShutdownRequested?.Invoke(this, EventArgs.Empty);
                    args.Handled = true;
                }
            };
            Application.Top.KeyPress += (args) => {
                if (args.KeyEvent.Key == Key.PageDown)
                {
                    _consoleView.IsScrollToEndEnabled = true;
                    _consoleView.ScrollToEnd(); // Manually scroll to end
                    args.Handled = true;
                }
            };
        }

        /// <summary>
        /// Sets up a global mouse handler to return focus to the command input on mouse release.
        /// </summary>
        private void SetupMouseHandler()
        {
            _mouseHandler = (mouseEvent) => // Assign to field for unsubscribing
            {
                if (mouseEvent.Flags.HasFlag(MouseFlags.Button1Released))
                {
                    Application.MainLoop.Invoke(() => {
                        if (_commandInput != null && !_commandInput.HasFocus)
                        {
                            _commandInput.SetFocus();
                        }
                    });
                }
                if (mouseEvent.Flags.HasFlag(MouseFlags.WheeledUp))
                {
                    Application.MainLoop.Invoke(() =>
                    {
                        _consoleView.IsScrollToEndEnabled = false;
                    });
                }
                if (mouseEvent.Flags.HasFlag(MouseFlags.WheeledDown))
                {
                    // Check *after* the default scroll action if the view is now at the bottom.
                    Application.MainLoop.Invoke(() =>
                    {
                        if (_consoleView != null && _consoleView.Lines > 0 && _consoleView.Bounds.Height > 0)
                        {
                            // Calculate the theoretical TopRow when the last line is visible
                            int bottomTopRow = Math.Max(0, _consoleView.Lines - _consoleView.Bounds.Height);
                            
                            // If the current TopRow is at or past this position, re-enable auto-scroll.
                            // Add a small tolerance (e.g., 1 line) in case Bounds.Height calculation is off slightly
                            // or the scroll stops just before the absolute bottom.
                            if (_consoleView.TopRow >= bottomTopRow - 1) 
                            {
                                _consoleView.IsScrollToEndEnabled = true;
                                // Optionally, snap exactly to the end when re-enabling via wheel
                                _consoleView.ScrollToEnd(); 
                            }
                        }
                    });
                }
            };
            Application.RootMouseEvent += _mouseHandler;
        }

        /// <summary>
        /// Shows the console UI, hides the real-time UI, and ensures command input has focus.
        /// Uses MainLoop.Invoke for thread safety.
        /// </summary>
        public void ShowConsoleUI()
        {
            if (Application.MainLoop == null) 
            { 
                Console.WriteLine("ShowConsoleUI: MainLoop not available."); 
                return; 
            }
            
            Application.MainLoop.Invoke(() => {
                if (_consoleWindow == null || _realTimeWindow == null) return;
                if (_consoleWindow.Visible) { _commandInput?.SetFocus(); return; } // Already visible, just focus

                _realTimeWindow.Visible = false;
                _consoleWindow.Visible = true;
                _commandInput?.SetFocus();
                RefreshUI();
            });
        }

        /// <summary>
        /// Shows the real-time UI, hides the console UI, updates labels, and ensures command input has focus.
        /// Uses MainLoop.Invoke for thread safety.
        /// Call 'UpdateLabels' to update the real-time view labels.
        /// </summary>
        public void ShowRealTimeUI()
        {
            if (Application.MainLoop == null)
            { 
                Console.WriteLine("Show RealTime UI: MainLoop not available"); 
                return;
            }
            
            Application.MainLoop.Invoke(() => 
            {
                if (_consoleWindow == null || _realTimeWindow == null) 
                    return;

                if (_realTimeWindow.Visible)  // Already visible, just focus
                { 
                    _commandInput?.SetFocus(); 
                    return; 
                }

                _consoleWindow.Visible = false;
                _realTimeWindow.Visible = true;
                _commandInput?.SetFocus();
                RefreshUI();
            });
        }

        /// <summary>
        /// Toggles between the console and real-time views.
        /// </summary>
        public void ToggleUI()
        {
            if (Application.MainLoop == null) 
                return; // Can't toggle if loop is gone
                
            Application.MainLoop.Invoke(() => 
            {
                if (_consoleWindow == null || _realTimeWindow == null) return; // Safety check
                if (_consoleWindow.Visible) 
                    ShowRealTimeUI();
                else 
                    ShowConsoleUI();
            });
        }

        /// <summary>
        /// Forces a UI refresh.
        /// </summary>
        public void RefreshUI()
        {
            if (Application.MainLoop == null) return;
            Application.MainLoop.Invoke(() => Application.Refresh());
        }

        /// <summary>
        /// Updates the labels in the Real-Time view using data from a StrategyUIData snapshot.
        /// </summary>
        /// <param name="snapshot">The latest snapshot of strategy data, or null if unavailable.</param>
        public void UpdateLabels(StrategyUIData? snapshot)
        {
            // Helper to format nullable decimals
            string Fmt(decimal? value, string format = "F2") => value?.ToString(format) ?? "---";
            string Pct(decimal? value, string format = "P4") => value?.ToString(format) ?? "---"; // Percentage format

            if (Application.MainLoop == null || _realTimeWindow?.Visible != true) return;

            Application.MainLoop.Invoke(() =>
            {
                try
                {
                    // Default state if snapshot is null
                    if (snapshot == null)
                    {
                        _modeLabel.Text = "API: Unavailable";
                        _statusLabel.Text = "Strategy: Unavailable";
                        _equityLabel.Text = "Equity: --- | uPnL: ---";
                        _exchangeRealizedPnlLabel.Text = "PnL (Exch R): ---";
                        _walletBalanceLabel.Text = "Wallet Bal (Total): ---";
                        _availableBalanceLabel.Text = "Wallet Bal (Avail): ---";
                        _usdtBalanceLabel.Text = "USDT (Total/Avail): --- / ---";
                        _spotPriceLabel.Text = "Spot Last: ---";
                        _spotBidAskLabel.Text = "Spot Bid/Ask: --- / ---";
                        _futuresPriceLabel.Text = "Futures Last: ---";
                        _futuresMarkLabel.Text = "Futures Mark: ---";
                        _futuresBidAskLabel.Text = "Futures Bid/Ask: --- / ---";
                        _futuresFundingLabel.Text = "Funding Rate: ---";
                        _stepsLabel.Text = "Steps: --- | High: --- | Low: ---";
                        _errorMessagesLabel.Text = "No recent errors.";
                        // Reset strategy PnL labels
                        _strategyPnlLabel.Text = "Strat PnL (C/P/T): --- / --- / ---";
                        _strategyFeesLabel.Text = "Strat Fees: ---";
                        return; // Exit early
                    }

                    // --- Update API/Account/Strategy Info ---
                    _modeLabel.Text = $"API: {snapshot.APIStatus}";
                    _statusLabel.Text = $"Strategy: {snapshot.StrategyStatus ?? "---"}";
                    _equityLabel.Text = $"Equity: {Fmt(snapshot.TotalEquity)} | uPnL: {Fmt(snapshot.UnrealizedPnL)}";
                    _exchangeRealizedPnlLabel.Text = $"PnL (Exch R): {Fmt(snapshot.RealizedPnL)}";
                    _walletBalanceLabel.Text = $"Wallet Bal (Total): {Fmt(snapshot.TotalWalletBalance)}";
                    _availableBalanceLabel.Text = $"Wallet Bal (Avail): {Fmt(snapshot.TotalAvailableBalance)}";
                    _usdtBalanceLabel.Text = $"USDT (Total/Avail): {Fmt(snapshot.USDTWalletBalance)} / {Fmt(snapshot.USDTAvailableBalance)}";
                    // TODO: AccountInitialMarginRate
                    // TODO: AccountMaintenanceMarginRate

                    // --- Update Market Data Info ---
                    _spotPriceLabel.Text = $"Spot Last: {Fmt(snapshot.SpotLastPrice)}";
                    _spotBidAskLabel.Text = $"Spot Bid/Ask: {Fmt(snapshot.SpotBid)} / {Fmt(snapshot.SpotAsk)}";
                    _futuresPriceLabel.Text = $"Futures Last: {Fmt(snapshot.FuturesLastPrice)}";
                    _futuresMarkLabel.Text =  $"Futures Mark: {Fmt(snapshot.FuturesMarkPrice)}";
                    _futuresBidAskLabel.Text =$"Futures Bid/Ask: {Fmt(snapshot.FuturesBid)} / {Fmt(snapshot.FuturesAsk)}";
                    _futuresFundingLabel.Text=$"Funding Rate: {Pct(snapshot.FuturesFundingRate)}";

                    // --- Update Strategy Specific Info ---
                    // TODO: Modify to something else which is relvant for this implementation
                    _stepsLabel.Text = $"Steps: {snapshot.OpenStepCount?.ToString() ?? "---"} | High: {Fmt(snapshot.HighestStepPrice)} | Low: {Fmt(snapshot.LowestStepPrice)}";

                    // Update Strategy PnL and Fees
                    string stratPnlC = Fmt(snapshot.StrategyCalculatedRealizedPnL, "F4");
                    string stratPnlP = Fmt(snapshot.PeakStrategyRealizedPnL, "F4");
                    string stratPnlT = Fmt(snapshot.TroughStrategyRealizedPnL, "F4");
                    _strategyPnlLabel.Text = $"Strat PnL (C/P/T): {stratPnlC} / {stratPnlP} / {stratPnlT}";
                    _strategyFeesLabel.Text = $"Strat Fees: {Fmt(snapshot.TotalStrategyFees, "F4")}";

                    if (snapshot.LastErrorMessages != null && snapshot.LastErrorMessages.Any())
                    {
                        _errorMessagesLabel.Text = string.Join("\n", snapshot.LastErrorMessages.Select(e => $"- {e}"));
                    }
                    else
                    {
                        _errorMessagesLabel.Text = "No recent errors.";
                    }
                }
                catch (Exception ex)
                {
                    _log.Warning(ex, "Exception during UpdateLabels execution");
                    // Safely handle exceptions in UI thread - set labels to error state
                    _modeLabel.Text = "API: Error";
                    _statusLabel.Text = "Strategy: Error Updating";
                    // ... set other labels to error state ...
                    _stepsLabel.Text = "Steps: Error | High: Error | Low: Error";
                    _errorMessagesLabel.Text = $"Error: {ex.Message}";
                }
            });
        }

        /// <summary>
        /// Internal loop started on the UI thread to periodically fetch data and update labels.
        /// </summary>
        private async Task StartInternalUIUpdateLoopAsync(CancellationToken token)
        {
            _log.Information("Internal UI Update Loop Starting...");
            try
            {
                while (!token.IsCancellationRequested)
                {
                     StrategyUIData? snapshot = null;
                     try
                     {
                        // Get snapshot from the injected provider
                        snapshot = _dataProvider?.GetUIDataSnapshot();
                     }
                     catch (Exception ex)
                     {
                        _log.Error(ex, "Error fetching UI data snapshot from provider.");
                        // Snapshot will remain null, UpdateLabels will show defaults/errors
                     }

                    // Update UI labels with the fetched snapshot (handles null)
                    UpdateLabels(snapshot);

                    // Wait for the next interval or cancellation
                    await Task.Delay(TimeSpan.FromMilliseconds(500), token); // Update interval
                }
            }
            catch (OperationCanceledException)
            {
                _log.Information("Internal UI Update Loop Cancelled.");
            }
            catch (Exception ex)
            {
                _log.Error(ex, "Internal UI Update Loop CRITICAL FAILURE");
                // Signal failure, Program.cs might catch this via the UI Task
                _uiShutdownTcs.TrySetException(ex);
            }
            finally
            {
                _log.Information("Internal UI Update Loop Stopped.");
            }
        }

        #endregion

        #region Dispose

        private bool _disposed = false;

        public void Dispose()
        {
            if (_disposed) return;
            _disposed = true;

            try
            {
                if (!_internalCts.IsCancellationRequested)
                {
                    _internalCts.Cancel();
                }
                _internalCts.Dispose();
            }
            catch (ObjectDisposedException) { }

            GC.SuppressFinalize(this);
        }

        #endregion
    }
}
