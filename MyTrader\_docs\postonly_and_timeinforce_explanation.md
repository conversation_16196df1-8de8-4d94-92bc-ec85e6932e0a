﻿Okay, let's break down TimeInForce and reduceOnly and discuss the other points.
## 1. TimeInForce Explained
 * `TimeInForce (TIF)` dictates how long an order remains active before it is executed or expires. It's primarily relevant for limit orders, as market orders typically execute immediately. Here are the common types:
* `GoodTillCancel (GTC)`: (Most common) The order remains active until it is either fully filled or explicitly cancelled by the user. This is the default behavior for most limit orders if no TIF is specified. Our current simulation implicitly uses this for limit orders placed via CreatePending...Order.
* `ImmediateOrCancel (IOC)`: The order tries to fill as much as possible immediately at the limit price or better. Any portion of the order that cannot be filled immediately is instantly cancelled. It does not rest on the order book. Useful if you want to execute aggressively but only at your desired price level without waiting. Note: Bybit states when opening Market order that TimeInForce is ImmediateOrCancel
* `FillOrKill (FOK)`: The entire order must be filled immediately at the limit price or better. If the entire quantity cannot be filled right away, the entire order is cancelled (killed). It never rests on the order book. Useful for ensuring an all-or-nothing execution at a specific price.
* `PostOnly (Maker Only)`: This applies only to limit orders. The order is only accepted if it does not immediately match with an existing order on the order book (i.e., it does not cross the spread). If placing the order would cause it to execute immediately (making you a "taker"), the order is rejected. This guarantees your order will be a "maker" order (adding liquidity to the book), which often comes with lower fees. This is very commonly used by strategies that rely on maker rebates or want to avoid taker fees.
Note: *`PostOnly`* can be only *GoodTillCanceled* !
## 2. ReduceOnly Explained
reduceOnly is a flag applied to an order (usually futures) to ensure it can only reduce the size of an existing position in the specified direction and symbol. It cannot increase a position or open a new one.
Your Understanding: You are correct that PositionDirection (for hedge mode) and OrderSide define the intent of the order – which position you want to modify and whether you want to add (buy long/sell short) or remove (sell long/buy short).
The reduceOnly Safety Net: Imagine you have a long position (Direction=Buy) and you want to take profit by placing a Sell limit order for that position. You'd set OrderSide = Sell and PositionDirection = Buy. Normally, this works.
Scenario: What if, due to latency or a rapid market move, your long position gets stopped out or liquidated just before your take-profit sell order executes?
Without reduceOnly: Your Sell order (with PositionDirection = Buy) would now execute, but since there's no long position to reduce, it would open a new short position under the Buy direction slot in hedge mode (or flip your position in one-way mode). This is likely not what you intended.
With reduceOnly = true: In the same scenario, when the exchange processes your Sell order, it sees the reduceOnly flag. It checks if a Buy position exists. Since it doesn't (it just closed), the exchange rejects or cancels the sell order instead of opening an unwanted new short position.