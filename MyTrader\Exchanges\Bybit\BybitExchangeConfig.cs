using Bybit.Net;
using Bybit.Net.Enums;
using System.Text.Json.Serialization;

namespace MyTraderSpace.Exchanges.Bybit
{
    public class BybitExchangeConfig : ExchangeConfig
    {
        // Bybit-specific settings
        public string? ApiKey { get; set; }
        public string? ApiSecret { get; set; }
        public string? KeysFile { get; set; }

        [JsonConverter(typeof(BybitEnvironmentConverter))]
        public BybitEnvironment Environment { get; set; } = BybitEnvironment.DemoTrading;
        
        // API behavior
        public int MaxRetries { get; set; } = 3;
        public TimeSpan RetryDelay { get; set; } = TimeSpan.FromSeconds(1);
    }
} 