using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using MyTraderSpace.Exchanges;
using MyTraderSpace.Logging;
using MyTraderSpace.Utils;


namespace MyTraderSpace.Models
{
    // TODO: Should we have our own Wallet PnL calculated based on the updates ?!
    public class Wallet
    {
        private readonly ConcurrentDictionary<CoinType, decimal> _balances = new();
        private readonly LogManager _log;
        private readonly IMarketDataService _marketDataService;
        private readonly object _updateLock = new object(); // Lock for accessing _lastUpdate

        // Wallet state - Store the last successfully processed update
        private WalletUpdate _lastUpdate = new WalletUpdate(); // Initialize with default

        public event Action<WalletUpdate>? OnWalletUpdate;

        public Wallet(IMarketDataService marketDataService)
        {
            _marketDataService = marketDataService;
            _log = new LogManager("Wallet", LogLevel.Information);
        }

        /// <summary>
        /// Processes an update from the exchange, updating internal balances and the cached state.
        /// </summary>
        public void UpdateFromExchange(WalletUpdate update)
        {
            if (update == null)
            {
                _log.Warning("UpdateFromExchange called with null update.");
                return;
            }

            string GetAssetBalanceLog(AssetBalance asset)
            {
                return $"Asset: {asset.Asset}, Total: {asset.Total:F8}, Avail: {asset.Available:F8}, InOrder: {asset.InOrder:F8}";
            }

            lock (_updateLock) // Protect access to _lastUpdate
            {
                _lastUpdate = update; // Cache the latest full update

                _log.Information("WALLET UPDATE RECEIVED:");
                _log.Information($"  AccountType: {update.AccountType}");
                _log.Information($"  TotalEquity: {update.TotalEquity?.ToString("F8") ?? "N/A"}");
                // TODO _log.Information($"  AccountInitialMarginRate: {update.AccountInitialMarginRate?.ToString("F8") ?? "N/A"}");
                // TODO _log.Information($"  AccountMaintenanceMarginRate: {update.AccountMaintenanceMarginRate?.ToString("F8") ?? "N/A"}");
                _log.Information($"  TotalWalletBalance: {update.TotalWalletBalance?.ToString("F8") ?? "N/A"}");
                _log.Information($"  TotalMarginBalance: {update.TotalMarginBalance?.ToString("F8") ?? "N/A"}");
                _log.Information($"  TotalAvailableBalance: {update.TotalAvailableBalance?.ToString("F8") ?? "N/A"}");
                _log.Information($"  TotalPerpUnrealizedPnl: {update.TotalPerpUnrealizedPnl?.ToString("F8") ?? "N/A"}");
                _log.Information($"  TotalInitialMargin: {update.TotalInitialMargin?.ToString("F8") ?? "N/A"}");
                _log.Information($"  TotalMaintenanceMargin: {update.TotalMaintenanceMargin?.ToString("F8") ?? "N/A"}");

                if (update.Assets != null && update.Assets.Any())
                {
                    _log.Information("  Asset Balances:");
                    foreach (var asset in update.Assets)
                    {
                        _log.Information($"    - {GetAssetBalanceLog(asset)}");
                    }
                }
                else
                {
                    _log.Information("  No asset balances provided in this update.");
                }

                // Clear old balances before applying new ones? Or update existing?
                // Let's update/add based on the assets in the new update
                _balances.Clear(); // Simplest for now: clear and repopulate
                foreach (var asset in update.Assets ?? Enumerable.Empty<AssetBalance>())
                {
                    if (Helpers.TryParseCoin(asset.Asset, out var coinType))
                    {
                        SetBalance(coinType, asset.Total); // SetBalance logs internally now
                    }
                    else
                    {
                        _log.Warning($"Could not parse asset '{asset.Asset}' during wallet update populate.");
                    }
                }
            } // Release lock

            // TODO / Note / Optional: Recalculate derived values if needed, but GetLastUpdate focuses on raw data
            // var calculatedUpdate = CreateWalletUpdate(); // This recalculates based on market data

            // Invoke event with the raw update received from the exchange
            OnWalletUpdate?.Invoke(update);
            // Redundant log, more detailed logging is now inside the lock
            // _log.Information($"Wallet updated from exchange - Total Equity: {update.TotalEquity?.ToString("F2") ?? "N/A"}");
        }

        /// <summary>
        /// Gets the last cached WalletUpdate received from the exchange.
        /// This is a synchronous method returning cached data.
        /// </summary>
        /// <returns>The last known WalletUpdate, or a default/empty one if none received.</returns>
        public WalletUpdate GetLastUpdate()
        {
            lock (_updateLock)
            {
                // Return a copy to prevent external modification? For records, maybe not strictly needed, but safer.
                // For now, return the direct reference as WalletUpdate is a record (value-like semantics).
                return _lastUpdate ?? new WalletUpdate();
            }
        }

        // Remove CreateWalletUpdate or adapt it if needed for other purposes
        // private WalletUpdate CreateWalletUpdate() { ... }

        // Basic balance operations (from SimulatedWallet)
        public void SetBalance(CoinType coin, decimal amount)
        {
            if (amount < 0)
            {
                _log.Error($"Attempted to set negative balance for {coin}: {amount}. Clamping to 0.");
                amount = 0; // Or throw new ArgumentException("Balance cannot be negative", nameof(amount));
            }
            _log.Debug($"Setting balance for {coin} to {amount:F8}");
            _balances[coin] = amount;
            // Does NOT update _lastUpdate intentionally
        }

        public decimal GetBalance(CoinType coin) => _balances.GetValueOrDefault(coin, 0m);

        public bool HasAsset(CoinType coin) => _balances.ContainsKey(coin);

        public virtual IDictionary<CoinType, AssetBalance> GetAllBalances()
        {
            // Return balances based on the internal dictionary, not necessarily _lastUpdate
            return _balances.ToDictionary(
                kvp => kvp.Key,
                kvp => new AssetBalance
                {
                    Asset = kvp.Key.ToString(),
                    Total = kvp.Value,
                    Available = kvp.Value, // Simplification: assume all is available
                    InOrder = 0
                });
        }

        public bool TryDeduct(CoinType coin, decimal amount)
        {
            if (amount < 0)
            {
                _log.Error($"Attempted to deduct negative amount for {coin}: {amount}. Operation aborted.");
                return false; // Or throw new ArgumentException("Amount cannot be negative", nameof(amount));
            }

            _log.Debug($"Attempting to deduct {amount:F8} {coin}.");
            bool success = false;
            _balances.AddOrUpdate(
                coin,
                (key) => 
                { 
                    _log.Warning($"Deduction failed: No existing balance for {coin} to deduct {amount:F8} from.");
                    // throw new InvalidOperationException($"No balance for {coin} to deduct from.");
                    return 0; // Cannot complete deduction if coin doesn't exist.
                },
                (key, oldBalance) =>
                {
                    if (oldBalance < amount)
                    {
                        _log.Warning($"Deduction failed: Insufficient {coin} balance ({oldBalance:F8}) to deduct {amount:F8}.");
                        // throw new InvalidOperationException($"Insufficient {coin} balance ({oldBalance}) to deduct {amount}.");
                        return oldBalance; // Return old balance if deduction fails
                    }
                    decimal newBalance = oldBalance - amount;
                    _log.Information($"Deducted {amount:F8} {coin}. Old: {oldBalance:F8}, New: {newBalance:F8}.");
                    success = true;
                    return newBalance;
                });
            return success; // Return actual success based on whether update logic completed deduction
        }

        public void Add(CoinType coin, decimal amount)
        {
             if (amount < 0)
             {
                _log.Error($"Attempted to add negative amount for {coin}: {amount}. Operation aborted.");
                return; // Or throw new ArgumentException("Amount cannot be negative", nameof(amount));
             }
            
            _balances.AddOrUpdate(
                 coin,
                 amount,
                 (key, oldBalance) => 
                 {
                     decimal newBalance = oldBalance + amount;
                     _log.Information($"Added {amount:F8} {coin}. Old: {oldBalance:F8}, New: {newBalance:F8}.");
                     return newBalance;
                 });
        }

        // Wallet state accessors using the *cached* update
        public decimal? TotalEquity => GetLastUpdate().TotalEquity;
        public decimal? TotalWalletBalance => GetLastUpdate().TotalWalletBalance;
        public decimal? TotalMarginBalance => GetLastUpdate().TotalMarginBalance;
        public decimal? TotalAvailableBalance => GetLastUpdate().TotalAvailableBalance;
        public decimal? TotalPerpUnrealizedPnl => GetLastUpdate().TotalPerpUnrealizedPnl;
        public decimal? TotalInitialMargin => GetLastUpdate().TotalInitialMargin;
        public decimal? TotalMaintenanceMargin => GetLastUpdate().TotalMaintenanceMargin;
    }
}